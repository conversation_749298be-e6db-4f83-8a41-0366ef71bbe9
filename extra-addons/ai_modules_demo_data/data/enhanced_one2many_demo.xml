<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- Enhanced Module Template with One2many Fields -->
        <record id="demo_template_project_management" model="module.template">
            <field name="name">Project Management System</field>
            <field name="code">project_management</field>
            <field name="description">Complete project management system with task tracking, milestone management, team collaboration, and resource allocation features. Showcases advanced one2many field handling in frontend.</field>
            <field name="template_type">form_based</field>
            <field name="state">active</field>
            <field name="module_author">Oneclickvakil</field>
            <field name="module_version">********.0</field>
            <field name="module_category">Project Management</field>
        </record>

        <!-- Enhanced Form Builder for Project Management -->
        <record id="demo_form_project_management_main" model="form.builder">
            <field name="name">Project Management Form</field>
            <field name="description">Main project management form with advanced one2many fields</field>
            <field name="template_id" ref="demo_template_project_management"/>
            <field name="form_type">main</field>
            <field name="model_name">project.management</field>
            <field name="sequence">1</field>
            <field name="active">True</field>
        </record>

        <!-- Basic Project Fields -->
        <record id="demo_field_project_name" model="field.definition">
            <field name="name">name</field>
            <field name="field_description">Project Name</field>
            <field name="field_type">char</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="help_text">Enter the project name</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_project_description" model="field.definition">
            <field name="name">description</field>
            <field name="field_description">Project Description</field>
            <field name="field_type">text</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">False</field>
            <field name="sequence">20</field>
            <field name="help_text">Detailed project description</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_project_start_date" model="field.definition">
            <field name="name">start_date</field>
            <field name="field_description">Start Date</field>
            <field name="field_type">date</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="help_text">Project start date</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_project_end_date" model="field.definition">
            <field name="name">end_date</field>
            <field name="field_description">End Date</field>
            <field name="field_type">date</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
            <field name="help_text">Project end date</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- One2many Field: Project Tasks -->
        <record id="demo_field_project_tasks" model="field.definition">
            <field name="name">task_ids</field>
            <field name="field_description">Project Tasks</field>
            <field name="field_type">one2many</field>
            <field name="widget">one2many_list</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">False</field>
            <field name="sequence">50</field>
            <field name="help_text">Add and manage project tasks with inline editing capabilities</field>
            <field name="relation_model">project.task</field>
            <field name="relation_field">project_id</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="one2many_fields">name,description,date_deadline,priority,stage_id,user_ids</field>
            <field name="one2many_editable">True</field>
            <field name="one2many_create">True</field>
            <field name="one2many_delete">True</field>
        </record>

        <!-- One2many Field: Project Milestones -->
        <record id="demo_field_project_milestones" model="field.definition">
            <field name="name">milestone_ids</field>
            <field name="field_description">Project Milestones</field>
            <field name="field_type">one2many</field>
            <field name="widget">one2many_inline</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">False</field>
            <field name="sequence">60</field>
            <field name="help_text">Define project milestones with inline form editing</field>
            <field name="relation_model">project.milestone</field>
            <field name="relation_field">project_id</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="one2many_fields">name,description,deadline,is_reached</field>
            <field name="one2many_editable">True</field>
            <field name="one2many_create">True</field>
            <field name="one2many_delete">True</field>
        </record>

        <!-- One2many Field: Team Members -->
        <record id="demo_field_project_team" model="field.definition">
            <field name="name">team_member_ids</field>
            <field name="field_description">Team Members</field>
            <field name="field_type">one2many</field>
            <field name="widget">one2many_kanban</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">False</field>
            <field name="sequence">70</field>
            <field name="help_text">Manage team members with kanban view and popup editing</field>
            <field name="relation_model">project.team.member</field>
            <field name="relation_field">project_id</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="one2many_fields">user_id,role,hourly_rate,allocation_percentage</field>
            <field name="one2many_editable">True</field>
            <field name="one2many_create">True</field>
            <field name="one2many_delete">True</field>
        </record>

        <!-- One2many Field: Project Resources -->
        <record id="demo_field_project_resources" model="field.definition">
            <field name="name">resource_ids</field>
            <field name="field_description">Project Resources</field>
            <field name="field_type">one2many</field>
            <field name="widget">one2many_tree</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">False</field>
            <field name="sequence">80</field>
            <field name="help_text">Track project resources and equipment with tree view</field>
            <field name="relation_model">project.resource</field>
            <field name="relation_field">project_id</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="one2many_fields">name,resource_type,quantity,unit_cost,total_cost</field>
            <field name="one2many_editable">True</field>
            <field name="one2many_create">True</field>
            <field name="one2many_delete">True</field>
        </record>

        <!-- Project Status Field -->
        <record id="demo_field_project_status" model="field.definition">
            <field name="name">status</field>
            <field name="field_description">Project Status</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">True</field>
            <field name="sequence">90</field>
            <field name="help_text">Current project status</field>
            <field name="selection_options">draft,Draft;in_progress,In Progress;on_hold,On Hold;completed,Completed;cancelled,Cancelled</field>
            <field name="default_value">draft</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- Project Budget Field -->
        <record id="demo_field_project_budget" model="field.definition">
            <field name="name">budget</field>
            <field name="field_description">Project Budget</field>
            <field name="field_type">float</field>
            <field name="form_builder_id" ref="demo_form_project_management_main"/>
            <field name="required">False</field>
            <field name="sequence">100</field>
            <field name="help_text">Total project budget</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- Workflow States for Project Management -->
        <record id="demo_workflow_state_draft" model="workflow.state">
            <field name="name">Draft</field>
            <field name="code">draft</field>
            <field name="template_id" ref="demo_template_project_management"/>
            <field name="sequence">10</field>
            <field name="is_initial">True</field>
            <field name="is_final">False</field>
            <field name="description">Project is in draft state</field>
        </record>

        <record id="demo_workflow_state_planning" model="workflow.state">
            <field name="name">Planning</field>
            <field name="code">planning</field>
            <field name="template_id" ref="demo_template_project_management"/>
            <field name="sequence">20</field>
            <field name="is_initial">False</field>
            <field name="is_final">False</field>
            <field name="description">Project planning phase</field>
        </record>

        <record id="demo_workflow_state_in_progress" model="workflow.state">
            <field name="name">In Progress</field>
            <field name="code">in_progress</field>
            <field name="template_id" ref="demo_template_project_management"/>
            <field name="sequence">30</field>
            <field name="is_initial">False</field>
            <field name="is_final">False</field>
            <field name="description">Project is actively being worked on</field>
        </record>

        <record id="demo_workflow_state_review" model="workflow.state">
            <field name="name">Review</field>
            <field name="code">review</field>
            <field name="template_id" ref="demo_template_project_management"/>
            <field name="sequence">40</field>
            <field name="is_initial">False</field>
            <field name="is_final">False</field>
            <field name="description">Project under review</field>
        </record>

        <record id="demo_workflow_state_completed" model="workflow.state">
            <field name="name">Completed</field>
            <field name="code">completed</field>
            <field name="template_id" ref="demo_template_project_management"/>
            <field name="sequence">50</field>
            <field name="is_initial">False</field>
            <field name="is_final">True</field>
            <field name="description">Project completed successfully</field>
        </record>

    </data>
</odoo>
