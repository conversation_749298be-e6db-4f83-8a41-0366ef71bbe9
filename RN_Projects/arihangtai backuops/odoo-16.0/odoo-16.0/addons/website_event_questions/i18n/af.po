# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_questions
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-04 09:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Afrikaans (https://app.transifex.com/odoo/teams/41243/af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_2
msgid "A friend"
msgstr ""

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_1
msgid "Allergies"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__name
msgid "Answer"
msgstr ""

#. module: website_event_questions
#: model:ir.actions.act_window,name:website_event_questions.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "Answers"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__once_per_order
msgid "Ask once per order"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_1
msgid "Commercials"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_0
msgid "Consumers"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_uid
msgid "Created by"
msgstr "Geskep deur"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_date
msgid "Created on"
msgstr "Geskep op"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__display_name
msgid "Display Name"
msgstr "Vertoningsnaam"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_event
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__event_id
msgid "Event"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question
msgid "Event Question"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question_answer
msgid "Event Question Answer"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration
msgid "Event Registration"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration_answer
msgid "Event Registration Answer"
msgstr ""

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_type
msgid "Event Template"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_type_id
msgid "Event Type"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__general_question_ids
msgid "General Questions"
msgstr ""

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_7_question_1
msgid "How did you hear about us ?"
msgstr ""

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_2
msgid "How did you learn about this event?"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__id
msgid "ID"
msgstr "ID"

#. module: website_event_questions
#: model:ir.model.fields,help:website_event_questions.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer____last_update
msgid "Last Modified on"
msgstr "Laas Gewysig op"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_uid
msgid "Last Updated by"
msgstr "Laas Opgedateer deur"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_date
msgid "Last Updated on"
msgstr "Laas Opgedateer op"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__is_mandatory_answer
msgid "Mandatory Answer"
msgstr ""

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_0
msgid "Meal Type"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_0
msgid "Mixed"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_type_data_conference_question_0_answer_1
msgid "No"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_0
msgid "Our website"
msgstr ""

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_type_data_conference_question_0
msgid "Participate in Social Event"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "Question"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__question_type
msgid "Question Type"
msgstr ""

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid "Question cannot belong to both the event category and itself."
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_form_inherit_question
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_type_view_form_inherit_question
msgid "Questions"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__registration_id
msgid "Registration"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_2
msgid "Research"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_1
msgid "Sales"
msgstr "Verkope"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_tree
msgid "Selected Answers"
msgstr ""

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_tree
msgid "Selected answer"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields.selection,name:website_event_questions.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__sequence
msgid "Sequence"
msgstr "Volgorde"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields.selection,name:website_event_questions.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr ""

#. module: website_event_questions
#: model:ir.model.constraint,message:website_event_questions.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr ""

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__title
msgid "Title"
msgstr "Titel"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_form_inherit_question
msgid "Type"
msgstr "Soort"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr ""

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_7_question_0
msgid "Which field are you working in"
msgstr ""

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_type_data_conference_question_0_answer_0
msgid "Yes"
msgstr ""

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr ""

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid ""
"You cannot delete a question that has already been answered by attendees."
msgstr ""

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid ""
"You cannot delete an answer that has already been selected by attendees."
msgstr ""

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "e.g. \"Do you have any diet restrictions?\""
msgstr ""
