<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="PaymentScreenPaymentLines" t-inherit="point_of_sale.PaymentScreenPaymentLines" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('send_payment_reversal')]/.." position="replace">
            <t t-if="line.canBeAdjusted() &amp;&amp; line.order.get_total_paid() &lt; line.order.get_total_with_tax()">
                <div class="button send_adjust_amount" title="Adjust Amount" t-on-click="() => this.trigger('send-payment-adjust', line)">
                    Adjust Amount
                </div>
            </t>
            <t t-elif="line.can_be_reversed">
                <div class="button send_payment_reversal" title="Reverse Payment" t-on-click="() => this.trigger('send-payment-reverse', line)">
                    Reverse
                </div>
            </t>
        </xpath>
    </t>

</templates>
