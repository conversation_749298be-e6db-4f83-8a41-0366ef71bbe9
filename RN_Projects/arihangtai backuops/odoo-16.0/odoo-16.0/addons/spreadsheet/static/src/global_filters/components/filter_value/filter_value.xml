<?xml version="1.0" encoding="utf-8"?>
<templates>
    <t t-name="spreadsheet_edition.FilterValue"
        owl="1">
        <div class="o-filter-value d-flex align-items-start w-100">
            <t t-set="filter"
                t-value="props.filter"/>
            <t t-set="filterValue"
                t-value="getters.getGlobalFilterValue(filter.id)"/>
            <div t-if="filter.type === 'text'" class="w-100">
                <input type="text"
                    class="o_input o-text-filter-input"
                    t-att-placeholder="env._t(filter.label)"
                    t-att-value="filterValue"
                    t-on-change="(e) => this.onTextInput(filter.id, e.target.value)"/>
            </div>
            <span t-if="filter.type === 'relation'" class="w-100">
                <RecordsSelector placeholder="' ' + env._t(filter.label)"
                    resModel="filter.modelName"
                    resIds="filterValue"
                    onValueChanged="(value) => this.onTagSelected(filter.id, value)" />
            </span>
            <div t-if="filter.type === 'date'" class="w-100">
                <DateFilterValue t-if="filter.rangeType !== 'relative'"
                    period="filterValue &amp;&amp; filterValue.period"
                    yearOffset="filterValue &amp;&amp; filterValue.yearOffset"
                    type="filter.rangeType"
                    onTimeRangeChanged="(value) => this.onDateInput(filter.id, value)" />
                <select t-if="filter.rangeType === 'relative'"
                    t-on-change="(e) => this.onDateInput(filter.id, e.target.value || undefined)"
                    class="date_filter_values o_input me-3"
                    required="true">
                    <option value="">Select period...</option>
                    <t t-foreach="relativeDateRangesTypes"
                        t-as="range"
                        t-key="range.type">
                        <option t-att-selected="range.type === filterValue"
                            t-att-value="range.type">
                            <t t-esc="range.description"/>
                        </option>
                    </t>
                </select>
            </div>
            <i t-if="getters.isGlobalFilterActive(filter.id)"
                class="fa fa-times btn btn-link text-muted o_side_panel_filter_icon ms-1"
                title="Clear"
                t-on-click="() => this.onClear(filter.id)"/>
        </div>
    </t>
</templates>
