<?xml version="1.0" encoding="utf-8"?>
<templates>
    <div t-name="spreadsheet.RecordsSelector" class="o_field_widget o_field_many2many_tags" owl="1">
        <div class="o_field_tags d-inline-flex flex-wrap mw-100 o_tags_input o_input">
            <TagsList tags="tags"/>
            <div class="o_field_many2many_selection d-inline-flex w-100">
                <Many2XAutocomplete
                    placeholder="props.placeholder"
                    resModel="props.resModel"
                    fieldString="props.placeholder"
                    activeActions="{}"
                    update.bind="update"
                    getDomain.bind="searchDomain"
                    isToMany="true"
                />
            </div>
        </div>
    </div>
</templates>
