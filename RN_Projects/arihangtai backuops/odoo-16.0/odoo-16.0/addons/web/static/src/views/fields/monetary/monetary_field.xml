<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="web.MonetaryField" owl="1">
        <span t-if="props.readonly" t-esc="formattedValue" />
        <div class="text-nowrap d-inline-flex w-100 align-items-baseline" t-else="">
            <span t-if="!props.hideSymbol and currency" t-out="currencySymbol" />
            <input t-ref="numpadDecimal" t-att-id="props.id" t-att-type="props.inputType" t-att-placeholder="props.placeholder" class="o_input flex-grow-1 flex-shrink-1"/>
        </div>
    </t>

</templates>
