<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.SignatureField" owl="1">
        <t t-if="props.value">
            <img class="o_signature img img-fluid"
                alt="Binary file"
                t-att-src="this.getUrl"
                t-att-name="props.name"
                t-att-style="sizeStyle"
                t-on-click="onClickSignature"
                t-on-error.stop="onLoadFailed"
                />
        </t>
        <t t-else="">
            <div class="o_signature o_signature_empty" t-att-style="sizeStyle" t-on-click="onClickSignature">
                <svg t-on-error.stop="onLoadFailed"></svg>
                <p>SIGNATURE</p>
            </div>
        </t>
    </t>

</templates>
