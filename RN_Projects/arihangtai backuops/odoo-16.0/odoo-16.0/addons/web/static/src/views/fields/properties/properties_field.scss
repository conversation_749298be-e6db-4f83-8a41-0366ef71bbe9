.o_field_properties {
    width: 100%;
}

// The web client will add a "o_field_invalid" class when the field is considered
// as not valid (at least one empty label). In that case we want to have in red
// only the empty label, and not all inputs / components.
.o_field_properties,
.o_field_properties.o_field_invalid,
.o_property_field_popover {
    .o_input,
    .o_dropdown_button,
    .o_datepicker_button {
        @include print-variable(o-input-border-color, $o-form-lightsecondary);
        color: $o-main-text-color !important;
    }
    .o_dropdown_button,
    .o_datepicker_button {
        color: initial !important;
    }
    .o_input_dropdown {
        color: $o-main-text-color !important;
    }

    & {
        .o_input:focus,
        .dropdown:focus ~ .o_dropdown_button,
        .dropdown:focus-within ~ .o_dropdown_button,
        .o_input:focus ~ .o_datepicker_button,
        .o_dropdown_button:focus {
            @include print-variable(o-input-border-color, $o-brand-primary);
            * {
                @include print-variable(o-input-border-color, $o-brand-primary);
            }
        }
    }
    // only the empty property label should be affected by `o_field_invalid`
    .o_field_property_empty_label {
        color: map-get($theme-colors, 'danger');
    }
}

.o_property_field {
    .popover-body {
        min-width: 500px;
    }

    .o_field_property_label {
        &:hover .o_field_property_open_popover,
        .o_field_property_open_popover:focus {
            opacity: 1;
        }
        .o_field_property_open_popover {
            opacity: 0;
            cursor: pointer;
            transition: 0.1s;
        }
        .o_field_property_open_popover:hover {
            color: $primary;
        }
    }

    .o-dropdown {
        .dropdown-menu {
            max-height: 300px;
        }
    }

    .o_property_field_highlight .o_field_property_empty_label,
    .o_property_field_highlight b {
        border: 1px solid $primary;
        padding: 5px;
        margin: -5px;
        border-radius: 3px;
    }
}

.o_field_property_add {
    // cancel button padding to align the
    // text button with properties labels
    margin-left: -1rem;
}

.o_property_field_popover {
    // put the popover behind datetime component
    z-index: $zindex-popover !important;
    font-size: inherit;
    box-shadow: 0 3rem 5rem rgba($black, .18) !important;
    box-sizing: border-box;
}

.o_xxs_form_view .o_field_properties {
    // mobile view
    .o_field_property_add,
    .o_field_property_open_popover {
        display: none;
    }
    .o_field_property_open_popover.disabled {
        pointer-events: none;
    }
}
