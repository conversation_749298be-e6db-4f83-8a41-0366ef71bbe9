<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.BadgeSelectionField" owl="1">
        <t t-if="props.readonly">
            <span t-esc="string" t-att-raw-value="value" />
        </t>
        <t t-else="">
            <t t-foreach="options" t-as="option" t-key="option[0]">
                <span
                    class="o_selection_badge"
                    t-att-class="{ active: value === option[0] }"
                    t-att-value="stringify(option[0])"
                    t-esc="option[1]"
                    t-on-click="() => this.onChange(option[0])"
                />
            </t>
        </t>
    </t>

</templates>
