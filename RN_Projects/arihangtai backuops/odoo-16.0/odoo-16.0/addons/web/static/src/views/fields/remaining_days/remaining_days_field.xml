<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.RemainingDaysField" owl="1">
        <t t-if="props.readonly">
            <t t-set="days" t-value="diffDays" />
            <t t-set="formatted" t-value="formattedValue" />
            <div
                t-att-class="{
                    'fw-bold': days !== null and days lte 0,
                    'text-danger': days !== null and days lt 0,
                    'text-warning': days !== null and days === 0,
                }"
                t-att-title="formatted"
            >
                <t t-esc="diffString"/>
            </div>
        </t>
        <t t-else="">
            <div>
                <t t-component="pickerComponent" date="props.value or false" onDateTimeChanged.bind="onDateTimeChanged" />
            </div>
        </t>
    </t>

</templates>
