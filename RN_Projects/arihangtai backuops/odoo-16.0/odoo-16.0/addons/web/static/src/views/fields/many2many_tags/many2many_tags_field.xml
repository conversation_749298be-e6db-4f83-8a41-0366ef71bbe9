<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.Many2ManyTagsField" owl="1">
        <div
            class="o_field_tags d-inline-flex flex-wrap"
            t-att-class="{'o_tags_input o_input': !props.readonly}"
        >
            <TagsList tags="tags"/>
            <div t-if="showM2OSelectionField" class="o_field_many2many_selection d-inline-flex w-100" t-on-keydown="onAutoCompleteKeydown" t-ref="autoComplete">
                <Many2XAutocomplete
                    id="props.id"
                    placeholder="tags.length ? '' : props.placeholder"
                    resModel="props.relation"
                    autoSelect="true"
                    fieldString="string"
                    activeActions="activeActions"
                    update="update"
                    quickCreate="activeActions.create ? quickCreate : null"
                    context="context"
                    getDomain.bind="getDomain"
                    isToMany="true"
                    nameCreateField="props.nameCreateField"
                />
            </div>
        </div>
    </t>

    <t t-name="web.Many2ManyTagsFieldColorListPopover" owl="1">
        <div class="o_tag_popover m-2">
            <ColorList colors="props.colors" forceExpanded="true" onColorSelected="(id) => props.switchTagColor(id, props.tag)"/>
            <CheckBox className="'pt-2'" value="props.tag.colorIndex === 0" onChange.alike="(isChecked) => props.onTagVisibilityChange(isChecked, props.tag)">Hide in kanban</CheckBox>
        </div>
    </t>

</templates>
