<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="web.Form.ButtonBox" owl="1" >
    <t t-set="allButtons" t-value="getButtons()" />
    <t t-set="visibleButtons" t-value="allButtons['visible']" />
    <t t-set="additionalButtons" t-value="allButtons['additional']" />
    <div class="o-form-buttonbox oe_button_box position-relative text-end" t-attf-class="{{ visibleButtons.length >= getMaxButtons() ? 'o_full' : 'o_not_full'}} {{this.props.class}}">
        <t t-slot="{{ button_value }}" t-foreach="visibleButtons" t-as="button" t-key="button_value"/>
        <t t-if="additionalButtons.length" >
            <Dropdown togglerClass="'btn btn-light o_button_more border-0 border-start border-bottom'" menuClass="'o_dropdown_more position-absolute start-0 top-100 w-100 p-0 border-0'" class="'oe_stat_button btn position-relative p-0 border-0'">
                <t t-set-slot="toggler"><span>More</span></t>
                <DropdownItem t-foreach="additionalButtons" t-as="button" t-key="button_value" class="'d-flex flex-column p-0'">
                    <t t-slot="{{ button_value }}" />
                </DropdownItem>
            </Dropdown>
        </t>
    </div>
</t>

</templates>
