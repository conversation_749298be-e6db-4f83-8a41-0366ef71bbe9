<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.PercentPieField" owl="1">
        <div class="o_pie">
            <div class="o_mask" t-attf-class="{{transform.value >= 360 ? 'o_full' : ''}}" t-attf-style="transform: rotate({{transform.left}}deg)"/>
            <div class="o_mask" t-attf-class="{{transform.value >= 180 ? 'o_full' : ''}}" t-attf-style="transform: rotate({{transform.right}}deg)"/>
            <div class="o_pie_value">
                <span t-esc="formattedValue"/>
            </div>
        </div>
        <span t-esc="props.string"/>
    </t>

</templates>
