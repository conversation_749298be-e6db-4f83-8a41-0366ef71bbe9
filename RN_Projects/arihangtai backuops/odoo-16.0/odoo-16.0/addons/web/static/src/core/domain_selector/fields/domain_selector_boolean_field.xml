<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="web.DomainSelectorBooleanField" owl="1">
        <div class="o_ds_value_cell">
            <select class="o_input" t-on-change="onChange">
                <option value="1" t-att-selected="props.value">set (true)</option>
                <option value="0" t-att-selected="!props.value">not set (false)</option>
            </select>
        </div>
    </t>

</templates>
