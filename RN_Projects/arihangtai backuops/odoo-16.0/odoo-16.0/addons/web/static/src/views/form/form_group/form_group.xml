<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="web.Form.OuterGroup" owl="1">
    <div class="o_group row align-items-start" t-attf-class="{{ allClasses }}" t-att-style="props.style">
        <t t-slot="title" t-if="props.slots and props.slots.title" />
        <t t-foreach="getItems()" t-as="item" t-key="item_index">
            <span class="o_newline" t-if="item.newline" />
            <t t-slot="{{ item.name }}" className="item.colspan !== props.maxCols ? 'col-lg-' + item.size : ''"/>
        </t>
    </div>
</t>

<t t-name="web.Form.InnerGroup" owl="1">
    <div t-attf-class="{{ allClasses }}" class="o_inner_group grid" t-att-style="props.style">
        <div t-if="props.slots and props.slots.title" t-attf-class="g-col-sm-{{ props.maxCols }}">
            <t t-slot="title" />
        </div>
        <div t-foreach="getRows()" t-as="row" t-key="row_index" class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0" t-if="row.isVisible">
            <t t-foreach="row" t-as="cell" t-key="cell_index">

                <t t-if="cell.subType === 'item_component'">
                    <t t-call="web.Form.InnerGroup.ItemComponent"><t t-set="cell" t-value="cell" /></t>
                </t>

                <t t-else="">
                    <div
                        class="o_cell flex-grow-1 flex-sm-grow-0"
                        t-attf-style="{{ cell.itemSpan > 1 ? 'grid-column: span ' + cell.itemSpan + ';' : '' }}{{ cell.width ? 'width: ' + cell.width + '%' + ';' : '' }}"
                        t-attf-class="{{ cell.subType === 'label' ? 'o_wrap_label w-100 text-break text-900' : null }}"
                        t-if="cell.isVisible">
                        <t t-slot="{{ cell.name }}" />
                    </div>
                </t>

            </t>
        </div>
    </div>
</t>

<t t-name="web.Form.InnerGroup.ItemComponent" owl="1">
    <t t-if="cell.props.fieldInfo.FieldComponent.name !== 'BooleanField'">
        <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900">
            <t t-component="cell.Component" t-if="cell.isVisible" t-props="cell.props"/>
        </div>
        <div
        class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break"
        t-attf-style="{{ cell.itemSpan -1 > 1 ? 'grid-column: span ' + (cell.itemSpan -1) + ';' : '' }}{{ cell.width ? 'width: ' + cell.width + '%' + ';' : '' }}">
            <t t-slot="{{ cell.name }}"/>
        </div>
    </t>
    <t t-else="">
        <div class="o_wrap_field_boolean d-flex flex-wrap d-sm-contents flex-sm-nowrap">
            <div class="o_cell o_wrap_label flex-sm-grow-0 text-break text-900">
                <t t-component="cell.Component" t-if="cell.isVisible" t-props="cell.props"/>
            </div>
            <div class="o_cell o_wrap_input order-first flex-sm-grow-0 order-sm-0"
                t-attf-style="{{ cell.itemSpan -1 > 1 ? 'grid-column: span ' + (cell.itemSpan -1) + ';' : '' }}">
                <t t-slot="{{ cell.name }}" />
            </div>
        </div>
    </t>
</t>

</templates>
