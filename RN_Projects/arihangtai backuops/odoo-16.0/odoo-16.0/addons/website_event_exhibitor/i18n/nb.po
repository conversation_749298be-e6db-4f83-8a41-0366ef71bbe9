# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_exhibitor
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-10 10:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: <PERSON>l (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid ""
")\n"
"                    to meet them !"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid ""
"<i class=\"fa fa-folder-open\"/>\n"
"                By Country"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid ""
"<i class=\"fa fa-folder-open\"/>\n"
"                By Level"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<i class=\"fa fa-fw fa-arrow-right\"/>\n"
"                    Write one."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<small class=\"badge text-bg-danger\">Unpublished</small>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_search_tag
msgid "<span class=\"btn border-0 py-1\">×</span>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<span class=\"h5 m-3\">Other exhibitors</span>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is currently closed</span><br/>\n"
"                    Come back between"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is full</span><br/>Come back later to have a chat with"
" us!"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"A non-profit international educational and scientific\n"
"                    organisation, hosting three departments (aeronautics and\n"
"                    aerospace, environmental and applied fluid dynamics, and\n"
"                    turbomachinery and propulsion)."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "About"
msgstr "Om"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevd"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__active
msgid "Active"
msgstr "Aktiv"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorering for Aktivitetsunntak"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_state
msgid "Activity State"
msgstr "Aktivitetsstatus"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon type Aktivitet"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "Add some exhibitors to get started !"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid "All Countries"
msgstr "Alle land"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid "All Levels"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Archived"
msgstr "Arkivert"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "As a team, we are happy to contribute to this event."
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Attendees will be able to join to meet"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Available from"
msgstr ""

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type1
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__bronze
msgid "Bronze"
msgstr "Bronse"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__can_publish
msgid "Can Publish"
msgstr "Kan publisere"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__chat_room_id
msgid "Chat Room"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "Come back between"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Come see us live, we hope to meet you !"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Connect"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Countries"
msgstr "Land"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_id
msgid "Country"
msgstr "Land"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_flag_url
msgid "Country Flag"
msgstr "Flagg"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid "Create a Sponsor / Exhibitor"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid "Create a Sponsor Level"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Customer Relationship Management"
msgstr "Administrasjon av kunderelasjoner (CRM)"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Deco Addict designs, develops, integrates and supports HR and Supply\n"
"                Chain processes in order to make our customers more productive,\n"
"                responsive and profitable."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Deco Addict integrates ERP for Global Companies and supports PME\n"
"                with Open Sources software to manage their businesses. Our\n"
"                consultants are experts in the following areas:"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_description
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Description"
msgstr "Beskrivelse"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Discover more"
msgstr "Oppdag mer"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__display_name
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_type__exhibitor_menu
msgid ""
"Display exhibitors on website, in the footer of every page of the event."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Display in footer"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Email"
msgstr "E-post"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_to
msgid "End hour"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model:ir.model,name:website_event_exhibitor.model_event_event
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "Event"
msgstr "Arrangement"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Event Page"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor
msgid "Event Sponsor"
msgstr "Arrangementssponsor"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor_type
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_tree
msgid "Event Sponsor Level"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_form
msgid "Event Sponsor Levels"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action_from_event
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Event Sponsors"
msgstr "Arrangementssponsorer"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_type
msgid "Event Template"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__exhibitor
msgid "Exhibitor"
msgstr ""

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#, python-format
msgid "Exhibitors"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_type_view_form
msgid "Exhibitors Menu Item"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu_ids
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__website_event_menu__menu_type__exhibitor
msgid "Exhibitors Menus"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome-ikon, for eksempel fa-tasks"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__sponsor
msgid "Footer Logo Only"
msgstr ""

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type3
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__gold
msgid "Gold"
msgstr "Gull"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Group By"
msgstr "Grupper etter"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Happy to be Sponsor"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__has_message
msgid "Has Message"
msgstr "Har melding"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__id
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__id
msgid "ID"
msgstr "ID"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for å indikere aktivitetsunntak."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis haket av, vil nye meldinger kreve din oppmerksomhet."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis haket av, har enkelte meldinger leveringsfeil."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_128
msgid "Image 128"
msgstr "Bilde 128"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_256
msgid "Image 256"
msgstr "Bilde 256"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_image_url
msgid "Image URL"
msgstr "Bilde-URL"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Inventory and Warehouse management"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_published
msgid "Is Published"
msgstr "Er publisert"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"It provides post-graduate education in fluid dynamics\n"
"                    (research master in fluid dynamics, former \"Diploma\n"
"                    Course\", doctoral program, stagiaire program and lecture\n"
"                    series) and encourages \"training in research through\n"
"                    research\"."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"It undertakes and promotes research in the field of fluid\n"
"                    dynamics. It possesses about fifty different wind tunnels,\n"
"                    turbomachinery and other specialized test facilities, some\n"
"                    of which are unique or the largest in the world. Extensive\n"
"                    research on experimental, computational and theoretical\n"
"                    aspects of gas and liquid flows is carried out under the\n"
"                    direction of the faculty and research engineers, sponsored\n"
"                    mainly by governmental and international agencies as well\n"
"                    as industries."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Jitsi Name"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "Join us next time to meet"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Join us there to meet"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_lang_id
msgid "Language"
msgstr "Språk"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor____last_update
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_last_activity
msgid "Last activity"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Level"
msgstr "Nivå"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Level:"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Live"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_512
msgid "Logo"
msgstr "Logo"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hovedvedlegg"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Materials Management"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_capacity
msgid "Max capacity"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error
msgid "Message Delivery error"
msgstr "Melding ved leveringsfeil"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_mobile
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Mobile"
msgstr "Mobil"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "More info"
msgstr "Mer info"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "MIn aktivitets tidsfrist"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_name
msgid "Name"
msgstr "Navn"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Neste kalender aktivitet"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Frist for neste aktivitet"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_summary
msgid "Next Activity Summary"
msgstr "Oppsummering av neste aktivitet"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_id
msgid "Next Activity Type"
msgstr "Neste aktivitetstype"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__no_ribbon
msgid "No Ribbon"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "No exhibitor found."
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Online"
msgstr "På nett"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__online
msgid "Online Exhibitor"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Opening Hours"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_from
msgid "Opening hour"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Our experts invent, imagine and develop solutions which meet\n"
"                your business requirements. They build a new technical\n"
"                environment for your company, but they always take the already\n"
"                installed IT software into account. That is why Idealis\n"
"                Consulting delivers excellence in HR and SC Management."
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_participant_count
msgid "Participant count"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Partner"
msgstr "Partner"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Peak participants"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Personnel Administration"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Phone"
msgstr "Telefon"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Published"
msgstr "Publisert"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid ""
"Rank your sponsors based on your own grading system (e.g. \"Gold, Silver, "
"Bronze\")."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Register"
msgstr "Registrer"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Reporting"
msgstr "Rapportering"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruker"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_ribbon_style
msgid "Ribbon Style"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_is_full
msgid "Room Is Full"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_name
msgid "Room Name"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Leveringsfeil"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Sales and Distribution"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sequence
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_type__exhibitor_menu
msgid "Showcase Exhibitors"
msgstr ""

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type2
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__silver
msgid "Silver"
msgstr "Sølv"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__subtitle
msgid "Slogan"
msgstr ""

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/models/event_sponsor.py:0
#, python-format
msgid "Sponsor"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_count
msgid "Sponsor Count"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__email
msgid "Sponsor Email"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__name
msgid "Sponsor Level"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_type_action
#: model:ir.ui.menu,name:website_event_exhibitor.menu_event_sponsor_type
msgid "Sponsor Levels"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__mobile
msgid "Sponsor Mobile"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__name
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Sponsor Name"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__phone
msgid "Sponsor Phone"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__exhibitor_type
msgid "Sponsor Type"
msgstr "Sponsortype"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__url
msgid "Sponsor Website"
msgstr "Sponsornettsted"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Sponsor image"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Sponsors"
msgstr "Sponsorer"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid ""
"Sponsors are advertised on your event pages.<br>\n"
"    Exhibitors have a dedicated page a with chat room for people to connect with them."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Sponsorship"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sponsor_type_id
msgid "Sponsorship Level"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basert på aktiviteter\n"
"Utgått: Fristen er allerede passert\n"
"I dag: Aktiviteten skal gjøres i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Talent Management"
msgstr ""

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/controllers/website_event_main.py:0
#, python-format
msgid ""
"The event %s starts on %s (%s). \n"
"Join us there to meet %s !"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_url
msgid "The full URL to access the document through the website."
msgstr "Fullstendig link for å nå dokumentet via nettstedet."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "The sponsor website description is missing."
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_date_tz
msgid "Timezone"
msgstr "Tidssone"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Top Bar Filter"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type unntaks-aktivitet på posten."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_thumb_details
msgid "Unpublished"
msgstr "Upublisert"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_published
msgid "Visible on current website"
msgstr "Synlig på nåværende nettsted"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "We did not find any exhibitor matching your"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_tree
msgid "Website"
msgstr "Nettsted"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_website_event_menu
msgid "Website Event Menu"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website Messages"
msgstr "Meldinger fra nettsted"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_url
msgid "Website URL"
msgstr "Nettsted-URL"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website communication history"
msgstr " Kommunikasjonshistorikk for nettsted"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_in_opening_hours
msgid "Within opening hours"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "a few seconds"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "e.g. \"Openwood specializes in home decoration...\""
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : OpenWood Decoration"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : https://www.odoo.com"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : <EMAIL>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. Your best choice for your home"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "is not available right now."
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "is over."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "search."
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "starts in"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "starts on"
msgstr ""
