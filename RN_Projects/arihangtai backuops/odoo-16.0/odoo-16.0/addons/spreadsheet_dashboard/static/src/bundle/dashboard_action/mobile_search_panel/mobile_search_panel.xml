<?xml version="1.0" encoding="utf-8"?>
<templates>
    <div t-name="documents_spreadsheet.DashboardMobileSearchPanel" class="o_search_panel o_search_panel_summary btn w-100 overflow-visible" owl="1">
        <t t-if="state.isOpen">
            <t t-portal="'body'">
                <div class="o_spreadsheet_dashboard_search_panel o_search_panel o_searchview o_mobile_search">
                    <div class="o_mobile_search_header">
                        <button type="button" class="o_mobile_search_button btn" t-on-click="() => this.state.isOpen = false">
                            <i class="fa fa-arrow-left" />
                            <strong class="ml8">BACK</strong>
                        </button>
                    </div>
                    <div class="o_mobile_search_content">
                        <div class="o_search_panel flex-grow-0 flex-shrink-0 border-end pe-2 pb-5 ps-4 h-100 bg-view overflow-auto">
                            <section t-foreach="props.groups" t-as="group" t-key="group.id" class="o_search_panel_section o_search_panel_category">
                                <header class="o_search_panel_section_header pt-4 pb-2 text-uppercase cursor-default">
                                    <b t-esc="group.name"/>
                                </header>
                                <ul class="list-group d-block o_search_panel_field">
                                    <li t-foreach="group.dashboards" t-as="dashboard" t-key="dashboard.id" t-on-click="() => this.onDashboardSelected(dashboard.id)" class="o_search_panel_category_value list-group-item py-1 o_cursor_pointer border-0 ps-0 pe-2">
                                        <header class="list-group-item list-group-item-action d-flex align-items-center p-0 border-0" t-att-class="{'active text-900 fw-bold': dashboard.id === props.activeDashboard.id}">
                                            <div class="o_search_panel_label d-flex align-items-center overflow-hidden w-100 o_cursor_pointer mb-0">
                                                <!-- empty button to mimick the standard search panel -->
                                                <button class="o_toggle_fold btn p-0 flex-shrink-0 text-center"/>
                                                <span t-esc="dashboard.displayName" class="o_search_panel_label_title text-truncate"/>
                                            </div>

                                        </header>
                                    </li>
                                </ul>
                            </section>
                        </div>
                    </div>
                </div>
            </t>
        </t>
        <div t-elif="props.groups.length" t-on-click="openDashboardSelection" class="d-flex align-items-center">
            <i class="fa fa-fw fa-filter"/>
            <div t-esc="searchBarText" class="o_search_panel_current_selection text-truncate ms-2 me-auto"/>
        </div>
    </div>
</templates>

