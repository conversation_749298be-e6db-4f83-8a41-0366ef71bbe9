// Preferences to allow unattended install of R-Kiosk extension
// Needed for Odoo IoT Box Customer display
pref("app.update.checkInstallTime", false);
pref("devtools.webide.widget.autoinstall", false);
pref("xpinstall.customConfirmationUI", false);
pref("xpinstall.signatures.required", false);
pref("browser.shell.checkDefaultBrowser", false);
// Open all links in the same tab
// Needed to change URL without having multiple tabs running
pref("browser.link.open_newwindow", 1);
pref("browser.shell.checkDefaultBrowser",false);
pref("dom.max_chrome_script_run_time", 0);
