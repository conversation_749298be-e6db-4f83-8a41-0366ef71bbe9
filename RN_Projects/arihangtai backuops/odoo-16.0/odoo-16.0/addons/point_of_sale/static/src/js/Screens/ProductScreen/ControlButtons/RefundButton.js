odoo.define('point_of_sale.RefundButton', function (require) {
    'use strict';

    const PosComponent = require('point_of_sale.PosComponent');
    const ProductScreen = require('point_of_sale.ProductScreen');
    const Registries = require('point_of_sale.Registries');
    const { useListener } = require("@web/core/utils/hooks");

    class RefundButton extends PosComponent {
        setup() {
            super.setup();
            useListener('click', this._onClick);
        }
        _onClick() {
            const partner = this.env.pos.get_order().get_partner();
            const searchDetails = partner ? { fieldName: 'PARTNER', searchTerm: partner.name } : {};
            this.showScreen('TicketScreen', {
                ui: { filter: 'SYNCED', searchDetails },
                destinationOrder: this.env.pos.get_order(),
            });
        }
    }
    RefundButton.template = 'point_of_sale.RefundButton';

    ProductScreen.addControlButton({
        component: RefundButton,
        condition: function () {
            return true;
        },
    });

    Registries.Component.add(RefundButton);

    return RefundButton;
});
