# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_meet
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-10 10:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "<b>&amp;#9900;&amp;nbsp;</b>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch me-3\"/>\n"
"                        <span>Loading your room...</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "<span class=\"badge text-bg-danger\">Unpublished</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "<span class=\"h5 m-3\">Other Rooms</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "<span>Create a Room</span>"
msgstr "<span>Creați o cameră</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "<span>Join a room</span>"
msgstr "<span>Alăturați-vă unei camere</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<span>Oops! This room is full !</span><br/>Come back later to have a chat "
"with us!"
msgstr ""
"<span>Oops! Această cameră este plină!</span><br/> Reveniți mai târziu "
"pentru a discuta cu noi!"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"<span>This room is not open right now!</span><br/>\n"
"                        Join us here on the"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "A chat among"
msgstr "O discuție printre"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__active
msgid "Active"
msgstr "Activ"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "All Languages"
msgstr "Toate limbile"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,field_description:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Allow Room Creation"
msgstr "Permiteți crearea camerei"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
#, python-format
msgid "Are you sure you want to close this room ?"
msgstr "Sigur doriți să închideți această cameră?"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
#, python-format
msgid "Are you sure you want to duplicate this room ?"
msgstr "Sigur doriți să duplicați această cameră?"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__target_audience
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Audience"
msgstr "Public / Audiență"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid ""
"Be sure you are ready to spend at least 10 minutes in the room if you want "
"to initiate a new topic."
msgstr ""
"Asigurați-vă că sunteți gata să petreceți cel puțin 10 minute în cameră dacă"
" doriți să inițiați un subiect nou."

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_0
msgid "Best wood for furniture"
msgstr "Cel mai bun lemn pentru mobilă"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__can_publish
msgid "Can Publish"
msgstr "Poate Publica"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Capacity"
msgstr "Capacitate"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__chat_room_id
msgid "Chat Room"
msgstr "Cameră de conversație"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid ""
"Choose a topic that interests you and start talking with the community. "
"<br/> Don't forget to setup your camera and microphone."
msgstr ""
"Alegeți un subiect care vă interesează și începeți să vorbiți cu "
"comunitatea. <br/>Nu uitați să vă configurați camera și microfonul."

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Close"
msgstr "Închide"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Create"
msgstr "Creează"

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Create a Room"
msgstr "Creați o cameră"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Create one to get conversations going"
msgstr "Creați unu pentru a menține conversațiile"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_date
msgid "Created on"
msgstr "Creat în"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Discard"
msgstr "Abandonează"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Dropdown menu"
msgstr "Meniul derulant"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Duplicate"
msgstr "Duplicare"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_event
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__event_id
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Event"
msgstr "Eveniment"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_meeting_room
msgid "Event Meeting Room"
msgstr "Cameră Întalnire Eveniment"

#. module: website_event_meet
#: model:ir.model.fields.selection,name:website_event_meet.selection__website_event_menu__menu_type__meeting_room
msgid "Event Meeting Room Menus"
msgstr "Meniuri Cameră Întalnire Eveniment"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.snippet_options
msgid "Event Page"
msgstr ""

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.action_meeting_room_from_event
msgid "Event Rooms"
msgstr ""

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_type
msgid "Event Template"
msgstr "Șablon Eveniment"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Full"
msgstr "Complet"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__id
msgid "ID"
msgstr "ID"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_pinned
msgid "Is Pinned"
msgstr "Este fixat"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_published
msgid "Is Published"
msgstr "Este Publicat"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us next time to chat about"
msgstr "Alătură-te nouă data viitoare pentru a discuta despre"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us there to chat about"
msgstr "Alăturați-vă acolo pentru a discuta"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_lang_id
#, python-format
msgid "Language"
msgstr "Limba"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Languages Menu"
msgstr "Meniu limbi"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_last_activity
msgid "Last activity"
msgstr "Ultima activitate"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Launch a new topic"
msgstr "Lansați un nou subiect"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,help:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Let Visitors Create Rooms"
msgstr "Lăsați vizitatorii să creeze camere"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_0
msgid "Let's talk about wood types for furniture"
msgstr "Să vorbim despre tipurile de lemn pentru mobilier"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_capacity
msgid "Max capacity"
msgstr "Capacitate maximă"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr "Numărul maxim de participanți ajunși în cameră în același timp"

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Meeting Room"
msgstr "Cameră de întâlnire"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_ids
msgid "Meeting rooms"
msgstr "Camere de întâlniri"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Tip Meniu"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "No Room Open"
msgstr "Nicio cameră deschisă"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_participant_count
msgid "Participant count"
msgstr "Număr de participanți"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Peak participants"
msgstr "Participanți de vârf"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_1
msgid "Reducing the ecological footprint with wood ?"
msgstr "Reducerea amprentei ecologice cu lemnul?"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
msgid "Reporting"
msgstr "Raportare"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.snippet_options
msgid "Room Creation (Specific)"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_is_full
msgid "Room Is Full"
msgstr "Camera este plină"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_name
msgid "Room Name"
msgstr "Nume Cameră"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Room Topic"
msgstr "Subiectul camerei"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_count
msgid "Room count"
msgstr "Numărul camerei"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Room creation will be available when event starts at"
msgstr "Crearea sălii va fi disponibilă la începutul evenimentului"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_event_view_form
msgid "Rooms"
msgstr "Camere"

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
msgid ""
"Rooms allow your event attendees to meet up and chat on different topics."
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_1
msgid "Share your tips to reduce your ecological footprint using wood."
msgstr ""
"Împărtășiți sfaturile dvs. pentru a reduce amprenta dvs. ecologică folosind "
"lemnul"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Short Summary"
msgstr "Rezumat Scurt"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Start a topic"
msgstr "Porniți un subiect"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__summary
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Summary"
msgstr "Sumar"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Target People"
msgstr "Țintește oamenii"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "The event"
msgstr "Evenenimentul"

#. module: website_event_meet
#: code:addons/website_event_meet/controllers/website_event_main.py:0
#, python-format
msgid ""
"The event %s starts on %s (%s). \n"
"Join us there to chat about \"%s\" !"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"URL-ul complet pentru accesarea documentului prin intermediul site-ului web."

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__name
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Topic"
msgstr "Subiect"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Total Participant Count"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Unpublished"
msgstr "Nepublicat"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_2
msgid ""
"Venez partager vos meubles préférés et l'utilisation que vous en faites."
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_published
msgid "Visible on current website"
msgstr "Vizibil pe site-ul curent"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_2
msgid "Vos meubles préférés ?"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Want to create your own discussion room ?"
msgstr "Doriți să vă creați propria sală de discuții?"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_website_event_menu
msgid "Website Event Menu"
msgstr "Meniul evenimentului site-ului web"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_url
msgid "Website URL"
msgstr "URL website"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "a few seconds"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_2
msgid "client(s)"
msgstr "client (i)"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Accountants"
msgstr "de exemplu. Contabili"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Finance"
msgstr "de exemplu. Finanţe"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Let's talk about Corporate Finance"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_1
msgid "ecologist(s)"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"is over.\n"
"                        <br/>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "participant(s)"
msgstr "participant(i)"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts in"
msgstr "începe în"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts on"
msgstr "începe pe"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "to have a chat with us!"
msgstr "pentru a avea o discuție cu noi!"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_0
msgid "wood expert(s)"
msgstr ""
