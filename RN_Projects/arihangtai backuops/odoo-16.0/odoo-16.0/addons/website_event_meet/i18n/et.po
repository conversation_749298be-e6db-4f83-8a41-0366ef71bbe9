# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_meet
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> Õigus <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> Randmets, 2022
# Anna, 2023
# JanaAvalah, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-10 10:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: JanaAvalah, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "<b>&amp;#9900;&amp;nbsp;</b>"
msgstr "<b>&amp;#9900;&amp;nbsp;</b>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch me-3\"/>\n"
"                        <span>Loading your room...</span>"
msgstr ""
"<i class=\"fa fa-spin fa-circle-o-notch me-3\"/>\n"
"                         <span>Teie jututoa laadimine...</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "<span class=\"badge text-bg-danger\">Unpublished</span>"
msgstr "<span class=\"badge text-bg-danger\">Avalikustamata</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "<span class=\"h5 m-3\">Other Rooms</span>"
msgstr "<span class=\"h5 m-3\">Teised jututoad</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "<span>Create a Room</span>"
msgstr "<span>Loo jututuba</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "<span>Join a room</span>"
msgstr "<span>Liitu jututoaga</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<span>Oops! This room is full !</span><br/>Come back later to have a chat "
"with us!"
msgstr ""
"<span>Vabandust! See jututuba hõivatud!</span><br/>Tulge hiljem tagasi, et "
"meiega vestelda!"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"<span>This room is not open right now!</span><br/>\n"
"                        Join us here on the"
msgstr ""
"<span>See jututuba ei ole praegu avatud!</span><br/>\n"
"                         Liituge meiega siin lehel"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "A chat among"
msgstr "Vestlus omavahel"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__active
msgid "Active"
msgstr "Tegev"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "All Languages"
msgstr "Kõik keeled"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,field_description:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Allow Room Creation"
msgstr "Luba jututoa loomine"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
#, python-format
msgid "Are you sure you want to close this room ?"
msgstr "Kas olete kindel, et soovite selle jututoa sulgeda?"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
#, python-format
msgid "Are you sure you want to duplicate this room ?"
msgstr "Kas olete kindel, et soovite seda jututuba kopeerida?"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__target_audience
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Audience"
msgstr "Audients"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid ""
"Be sure you are ready to spend at least 10 minutes in the room if you want "
"to initiate a new topic."
msgstr ""
"Kui soovite uut teemat algatada, veenduge, et olete valmis jututoas veetma "
"vähemalt 10 minutit."

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_0
msgid "Best wood for furniture"
msgstr "Parim puit mööbli jaoks"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__can_publish
msgid "Can Publish"
msgstr "Võib avalikustada"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Capacity"
msgstr "Maht"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__chat_room_id
msgid "Chat Room"
msgstr "Jututuba"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid ""
"Choose a topic that interests you and start talking with the community. "
"<br/> Don't forget to setup your camera and microphone."
msgstr ""
"Valige teema, mis Teid huvitab, ja hakake inimestega rääkima. <br/> Ärge "
"unustage seadistada kaamerat ja mikrofoni."

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Close"
msgstr "Sulge"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Create"
msgstr "Loo"

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Create a Room"
msgstr "Loo jututuba"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Create one to get conversations going"
msgstr "Looge vestluste käivitamiseks üks"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Discard"
msgstr "Loobu"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Dropdown menu"
msgstr "Rippmenüü"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Duplicate"
msgstr "Tee koopia"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_event
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__event_id
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Event"
msgstr "Sündmus"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_meeting_room
msgid "Event Meeting Room"
msgstr "Ürituse koosolekuaken"

#. module: website_event_meet
#: model:ir.model.fields.selection,name:website_event_meet.selection__website_event_menu__menu_type__meeting_room
msgid "Event Meeting Room Menus"
msgstr "Ürituse koosolekuakna menüüd"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.snippet_options
msgid "Event Page"
msgstr "Sündmuse leht"

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.action_meeting_room_from_event
msgid "Event Rooms"
msgstr "Sündmuste aknad"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_type
msgid "Event Template"
msgstr "Sündmuse mall"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Full"
msgstr "Täis"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Group By"
msgstr "Grupeeri"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__id
msgid "ID"
msgstr "ID"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_pinned
msgid "Is Pinned"
msgstr "On kinnitatud"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_published
msgid "Is Published"
msgstr "On avalikustatud"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us next time to chat about"
msgstr "Liituge meiega järgmine kord, et vestelda"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us there to chat about"
msgstr "Liituge meiega seal, et vestelda"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_lang_id
#, python-format
msgid "Language"
msgstr "Keel"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Languages Menu"
msgstr "Keelemenüüd"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_last_activity
msgid "Last activity"
msgstr "Viimane tegevus"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Launch a new topic"
msgstr "Käivitage uus teema"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,help:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Let Visitors Create Rooms"
msgstr "Võimalda külastajatel vestlusi luua"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_0
msgid "Let's talk about wood types for furniture"
msgstr "Räägime mööbli puidutüüpidest"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_capacity
msgid "Max capacity"
msgstr "Maksimaalne mahutavus"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr "Jututoas on saavutatud maksimaalne osalejate arv"

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Meeting Room"
msgstr "Kohtumise ruum"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_ids
msgid "Meeting rooms"
msgstr "Kohtumise ruumid"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Menüü tüüp"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "No Room Open"
msgstr "Ühtegi avatud sündmust pole"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_participant_count
msgid "Participant count"
msgstr "Osalejate arv"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Peak participants"
msgstr "Maksimum osalejad"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_1
msgid "Reducing the ecological footprint with wood ?"
msgstr "Puidu abil ökoloogilise jalajälje vähendamine?"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
msgid "Reporting"
msgstr "Aruandlus"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.snippet_options
msgid "Room Creation (Specific)"
msgstr "Jututoa loomine (konkreetne)"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_is_full
msgid "Room Is Full"
msgstr "Jututuba on täis"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_name
msgid "Room Name"
msgstr "Jututoa nimi"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Room Topic"
msgstr "Jututoa teema"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_count
msgid "Room count"
msgstr "Jututoa arv"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Room creation will be available when event starts at"
msgstr "Jututoa loomine on saadaval siis, kui üritus algab kell"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_event_view_form
msgid "Rooms"
msgstr "Jututoad"

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
msgid ""
"Rooms allow your event attendees to meet up and chat on different topics."
msgstr ""
"Jututoad võimaldavad Teie üritusel osalejatel kohtuda ja erinevatel teemadel"
" vestelda."

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_1
msgid "Share your tips to reduce your ecological footprint using wood."
msgstr ""
"Jaga oma näpunäiteid ökoloogilise jalajälje vähendamiseks (puidu abil)."

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Short Summary"
msgstr "Lühikokkuvõte"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Start a topic"
msgstr "Alusta teemat"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__summary
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Summary"
msgstr "Kokkuvõte"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Target People"
msgstr "Target People"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "The event"
msgstr "Sündmus"

#. module: website_event_meet
#: code:addons/website_event_meet/controllers/website_event_main.py:0
#, python-format
msgid ""
"The event %s starts on %s (%s). \n"
"Join us there to chat about \"%s\" !"
msgstr ""
"Sündmus %s algab %s (%s).\n"
"Liituge meiega, et vestelda teemal \"%s\"!"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__website_url
msgid "The full URL to access the document through the website."
msgstr "Dokumendile veebisaidi kaudu juurdepääsuks täielik URL."

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__name
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Topic"
msgstr "Teema"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Total Participant Count"
msgstr "Osalejate koguarv"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Unpublished"
msgstr "Avalikustamata"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_2
msgid ""
"Venez partager vos meubles préférés et l'utilisation que vous en faites."
msgstr ""
"Venez partager vos meubles préférés et l'utilisation que vous en faites."

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_published
msgid "Visible on current website"
msgstr "Nähtav praegusel veebilehel"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_2
msgid "Vos meubles préférés ?"
msgstr "Vos meubles préférés ?"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Want to create your own discussion room ?"
msgstr "Kas soovite luua oma vestlusruumi?"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_website_event_menu
msgid "Website Event Menu"
msgstr "Veebilehe sündmuste menüü"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_url
msgid "Website URL"
msgstr "Veebilehe URL"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "a few seconds"
msgstr "paar sekundit"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_2
msgid "client(s)"
msgstr "kliendid"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Accountants"
msgstr "nt. Raamatupidajad"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Finance"
msgstr "nt. Finants"

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Let's talk about Corporate Finance"
msgstr "nt. Räägime ettevõtte rahandusest"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_1
msgid "ecologist(s)"
msgstr "ökoloog(id)"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"is over.\n"
"                        <br/>"
msgstr ""
"on läbi.\n"
"                         <br/>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""
"on läbi.\n"
"                <br/>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "participant(s)"
msgstr "osaleja(d)"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts in"
msgstr "algab"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts on"
msgstr "algab"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "to have a chat with us!"
msgstr "meiega vestlema!"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_0
msgid "wood expert(s)"
msgstr "puiduekspert(id)"
