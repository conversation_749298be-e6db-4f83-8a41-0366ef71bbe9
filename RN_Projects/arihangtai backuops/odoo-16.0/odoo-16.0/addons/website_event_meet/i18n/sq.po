# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_meet
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-10 10:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Language-Team: Albanian (https://app.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "&amp;nbsp;"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "<b>&amp;#9900;&amp;nbsp;</b>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch me-3\"/>\n"
"                        <span>Loading your room...</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "<span class=\"badge text-bg-danger\">Unpublished</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "<span class=\"h5 m-3\">Other Rooms</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "<span>Create a Room</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "<span>Join a room</span>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<span>Oops! This room is full !</span><br/>Come back later to have a chat "
"with us!"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"<span>This room is not open right now!</span><br/>\n"
"                        Join us here on the"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "A chat among"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__active
msgid "Active"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "All Languages"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,field_description:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Allow Room Creation"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
#, python-format
msgid "Are you sure you want to close this room ?"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
#, python-format
msgid "Are you sure you want to duplicate this room ?"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__target_audience
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Audience"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid ""
"Be sure you are ready to spend at least 10 minutes in the room if you want "
"to initiate a new topic."
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_0
msgid "Best wood for furniture"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__can_publish
msgid "Can Publish"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Capacity"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__chat_room_id
msgid "Chat Room"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid ""
"Choose a topic that interests you and start talking with the community. "
"<br/> Don't forget to setup your camera and microphone."
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Close"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Create"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Create a Room"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Create one to get conversations going"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_uid
msgid "Created by"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_date
msgid "Created on"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Discard"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__display_name
msgid "Display Name"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Dropdown menu"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Duplicate"
msgstr ""

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_event
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__event_id
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Event"
msgstr ""

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_meeting_room
msgid "Event Meeting Room"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields.selection,name:website_event_meet.selection__website_event_menu__menu_type__meeting_room
msgid "Event Meeting Room Menus"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.snippet_options
msgid "Event Page"
msgstr ""

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.action_meeting_room_from_event
msgid "Event Rooms"
msgstr ""

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_type
msgid "Event Template"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Full"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Group By"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__id
msgid "ID"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_pinned
msgid "Is Pinned"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_published
msgid "Is Published"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us next time to chat about"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us there to chat about"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_lang_id
#, python-format
msgid "Language"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Languages Menu"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room____last_update
msgid "Last Modified on"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_uid
msgid "Last Updated by"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_date
msgid "Last Updated on"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_last_activity
msgid "Last activity"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Launch a new topic"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,help:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Let Visitors Create Rooms"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_0
msgid "Let's talk about wood types for furniture"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_capacity
msgid "Max capacity"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr ""

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Meeting Room"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_ids
msgid "Meeting rooms"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "No Room Open"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_participant_count
msgid "Participant count"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Peak participants"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_1
msgid "Reducing the ecological footprint with wood ?"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
msgid "Reporting"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.snippet_options
msgid "Room Creation (Specific)"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_is_full
msgid "Room Is Full"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_name
msgid "Room Name"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Room Topic"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_count
msgid "Room count"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Room creation will be available when event starts at"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_event_view_form
msgid "Rooms"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
msgid ""
"Rooms allow your event attendees to meet up and chat on different topics."
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_1
msgid "Share your tips to reduce your ecological footprint using wood."
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Short Summary"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Start a topic"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__summary
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Summary"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#, python-format
msgid "Target People"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "The event"
msgstr ""

#. module: website_event_meet
#: code:addons/website_event_meet/controllers/website_event_main.py:0
#, python-format
msgid ""
"The event %s starts on %s (%s). \n"
"Join us there to chat about \"%s\" !"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__name
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Topic"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Total Participant Count"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Unpublished"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_2
msgid ""
"Venez partager vos meubles préférés et l'utilisation que vous en faites."
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_2
msgid "Vos meubles préférés ?"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Want to create your own discussion room ?"
msgstr ""

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_website_event_menu
msgid "Website Event Menu"
msgstr ""

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_url
msgid "Website URL"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "a few seconds"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_2
msgid "client(s)"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Accountants"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Finance"
msgstr ""

#. module: website_event_meet
#. openerp-web
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#, python-format
msgid "e.g. Let's talk about Corporate Finance"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_1
msgid "ecologist(s)"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"is over.\n"
"                        <br/>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "participant(s)"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts in"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts on"
msgstr ""

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "to have a chat with us!"
msgstr ""

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_0
msgid "wood expert(s)"
msgstr ""
