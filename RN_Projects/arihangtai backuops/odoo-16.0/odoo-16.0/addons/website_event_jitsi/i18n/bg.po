# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_jitsi
# 
# Translators:
# <PERSON> Ilie<PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-09 14:06+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: Nikola Iliev, 2023\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_jitsi
#: model:ir.model,name:website_event_jitsi.model_res_config_settings
msgid "Config Settings"
msgstr "Настройки"

#. module: website_event_jitsi
#: model:ir.model.fields,field_description:website_event_jitsi.field_res_config_settings__jitsi_server_domain
msgid "Jitsi Server Domain"
msgstr ""

#. module: website_event_jitsi
#: model:ir.model.fields,help:website_event_jitsi.field_res_config_settings__jitsi_server_domain
msgid ""
"The Jitsi server domain can be customized through the settings to use a "
"different server than the default \"meet.jit.si\""
msgstr ""

#. module: website_event_jitsi
#: model_terms:ir.ui.view,arch_db:website_event_jitsi.res_config_settings_view_form
msgid "meet.jit.si"
msgstr ""
