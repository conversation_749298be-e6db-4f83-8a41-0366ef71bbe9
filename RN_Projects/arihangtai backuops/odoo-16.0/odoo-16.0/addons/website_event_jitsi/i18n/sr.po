# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_jitsi
# 
# Translators:
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-09 14:06+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.vukosavl<PERSON><EMAIL>>, 2022\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_event_jitsi
#: model:ir.model,name:website_event_jitsi.model_res_config_settings
msgid "Config Settings"
msgstr "Podešavanje konfiguracije"

#. module: website_event_jitsi
#: model:ir.model.fields,field_description:website_event_jitsi.field_res_config_settings__jitsi_server_domain
msgid "Jitsi Server Domain"
msgstr "Jitsi Server Domain"

#. module: website_event_jitsi
#: model:ir.model.fields,help:website_event_jitsi.field_res_config_settings__jitsi_server_domain
msgid ""
"The Jitsi server domain can be customized through the settings to use a "
"different server than the default \"meet.jit.si\""
msgstr ""
"Domen Jitsi servera se može prilagoditi kroz podešavanja da bi se koristio "
"server koji nije podrazumevani „meet.jit.si“"

#. module: website_event_jitsi
#: model_terms:ir.ui.view,arch_db:website_event_jitsi.res_config_settings_view_form
msgid "meet.jit.si"
msgstr "meet.jit.si"
