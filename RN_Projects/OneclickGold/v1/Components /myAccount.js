import React, { useState, useEffect,useContext } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Image, ActivityIndicator } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import AntDesign from '@expo/vector-icons/AntDesign';
import editInformation from './editInformation';
import { SessionContext } from './login';


const AccountScreen = ({ navigation }) => {
  const { sessionId, loadSessionData } = useContext(SessionContext);
  
  // Define states to store the user data and loading state
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const sellMetal = (item) => {
    navigation.navigate('SellMetalNavigation', { item });
  };

  const mockData = {
     menuItems: [
       {
         title: "Your Invoices",
        subtitle: "Follow, download or pay your invoices",
      },
       { title: "Sip" },
       { title: "Sip Deliveries" },
       { title: "Sip Transactions" },
       { title: "Bank Accounts" },
       { title: "E-wallet To Bank Account Transactions" },
       { title: "Nominee Contacts" },
       { title: "Payment Methods" },
       { title: "Connection & Security"},
     ],
   };
    

  // Fetch the user data from API

  
  // Helper component for menu items
 const MenuSection = ({ title, subtitle }) => (
  <TouchableOpacity className="flex-row items-center justify-between py-3">
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[350px] h-[80px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">{title}</Text>
        {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>}
      </View>
    </View>
  </TouchableOpacity>
);

  const YourInvoice = () => {

    navigation.navigate('YourInvoice');
  }
  
  const editInformation = () => {
    navigation.navigate('editInformation');
  }

  const Sip = () => {

    navigation.navigate('Sip');
  }
  const SipDeliveries = () => {

    navigation.navigate('SipDeliveries');
  }

  const SipTransactions = () => {

    navigation.navigate('SipTransactions');
  }

  const BankAccount = () => {

    navigation.navigate('BankAccountList');
  }

  const walletTransactions = () => {

    navigation.navigate('WalletTransactionScreen');
  }

  const Nominee = () => {

    navigation.navigate('NomineeList');
  }

  const Payment = () => {

    navigation.navigate('Payment');
  }

  const Connection = () => {

    navigation.navigate('connections');
  }
 

  const fetchUserData = async () => {
    try {
      const response = await fetch('https://gold.arihantai.com/api/v1/my_account', {
        method: 'GET',
        headers: {
          // 'Cookie': 'frontend_lang=en_US; session_id=1671807358f0b263c43d8bd273fd2b7e4311d61d',
          'Cookie': `session_id=${sessionId}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log("account data",data);
      
      // Check if the API response is successful
      if (response.ok && data.status === 'success') {
        setUserData(data.data); // Store the user data from the API response
      } else {
        setError('Failed to load data');
      }
    } catch (err) {
      setError('Error fetching data');
    } finally {
      setLoading(false);
    }
  };

  // Use useEffect to call the fetch function when the component mounts
  useEffect(() => {
    fetchUserData();
  }, []);

  if (loading) {
    return <ActivityIndicator size="large" color="#0000ff" />;
  }

  if (error) {
    return <Text>{error}</Text>;
  }

  if (!userData) {
    return <Text>No data available</Text>;
  }

  // Extract user data and vault data from the fetched response
  const { user, vault , billing_address} = userData;
  const { metal_balances } = vault;

  return (
    <ScrollView className="flex-1 bg-white">
      {/* Header */}
      {/* <View className="flex-row items-center p-4">
        <TouchableOpacity className="ml-3">
          <Ionicons name="arrow-back-sharp" size={24} color="black" />
        </TouchableOpacity>
        <Text className="ml-4 text-lg font-semibold">My Account</Text>
      </View> */}

      {/* Team Section */}
      <View className="p-3 top-2">
        <View className="flex-row justify-between items-center mb-2">
          <View className="flex-row justify-between">
            <View className="w-10 h-10 border-[#013443] rounded-full justify-center items-center">
              <FontAwesome name="user-circle-o" size={24} color="#013443" />
            </View>
            <Text className="font-bold ml-4 mt-2">{user.name}</Text>
            {/* <Text className="text-gray-600 mt-4">
               {/* <FontAwesome name="fa-wallet" sixe={16} color={"#000"} className="ml-2" /> */}
               {/* <FontAwesome icon="fa-solid fa-wallet" name="wallet" /> */}
               {/* <FontAwesome icon="fa-solid fa-wallet" /> */}

            {/* <Ionicons name="wallet" size={20} color="#333" />    */}
              {/* {`₹${vault.total_wallet.toFixed(2)}`}</Text> */} 
            {/* <View className="ml-3 flex-1"> */}
            <View className="ml-60 mt-2 ">

         
              <TouchableOpacity onPress={editInformation}>
                <FontAwesome5 name="user-edit" size={22} color="#013443" />
              </TouchableOpacity>
              <Text className="text-[16px]">Edit</Text>
            {/* </View> */}

            </View>
                     </View>
        </View>

        {/* Contact Info */}
        <View className="space-y-3 border-b border-b-[#D9D9D9] mb-4">
        <Text className="text-black-200  ml-3">
               {/* <FontAwesome name="fa-wallet" sixe={16} color={"#000"} className="ml-2" /> */}
               {/* <FontAwesome icon="fa-solid fa-wallet" name="wallet" /> */}
               {/* <FontAwesome icon="fa-solid fa-wallet" /> */}

               <Ionicons name="wallet" size={20} color="#333" className="mr-2" />   

              {`₹${vault.total_wallet.toFixed(2)}`}</Text>

          <View className="flex-row items-center ml-3 mt-2">
            <Ionicons name="mail-outline" size={20} color="#202244" />
            <Text className="ml-2 text-[#000000] text-[13px] bottom-1">{user.email}</Text>
          </View>
          <View className="flex-row items-center ml-3 mt-2 mb-5">
            <Ionicons name="call-outline" size={20} color="#202244" />
            <Text className="ml-2 text-[#000000] text-[13px] bottom-1">{user.phone ? user.phone : 'N/A'}</Text>
            {/* <Text className="ml-2 text-[#000000] text-[13px] bottom-1">{billing_address.street ? billing_address.street : 'N/A'}</Text> */}
          </View>
          <View className="flex-row items-center ml-3 mb-5">
            <Ionicons name="location-outline" size={20} color="#202244" />
            <Text className="ml-2 text-[#000000] text-[13px] bottom-1">{billing_address.street ? billing_address.street : 'N/A'}{billing_address.street2 ? billing_address.street2 : 'N/A'}
            {/* </Text> */}
             <Text className="mt-1 ml-1">  {billing_address.city ? billing_address.city : 'N/A'}, </Text>
            <Text className="mt-1 ml-1">{billing_address.state ? billing_address.state : 'N/A'},</Text>
            <Text className="mt-1 ml-1">{billing_address.country ? billing_address.country : 'N/A'}</Text>
            </Text>
          </View>
        </View>
      </View>

      {/* My Vault */}
      <View className="p-4 border-b border-dashed border-b-[#D9D9D9] mt-1">
        <Text className="text-lg font-semibold mb-3 ml-4">My Vault</Text>
        <View className="flex-row space-x-4 ml-4 mb-5">
          {/* Loop through the metal balances */}
          {metal_balances.map((metal, index) => (
            <View key={index} className="items-center w-[150px] h-[80px] border-[#D9D9D9] border p-3 rounded-lg mr-4">
              <View className="flex-row">
                <Image
                  source={{ uri: metal.metal_name === 'Gold' ? 'https://gold.arihantai.com/web/image/product.template/13/image_1024/Happy%20Birthday%20Gold%20Coin%205%20gm%2024kt%20%28995%29?unique=08f1ed8' : 'https://gold.arihantai.com/web/image/product.product/61/image_1024/Kalpavruksha%20Pure%20999%20Silver%20Coin?unique=54300d1' }} // Add relevant image URLs
                  className="w-[48px] h-[48px]"
                />
                <View className="flex-col ml-2">
                  <Text className="font-semibold">{metal.metal_name}</Text>
                  <Text className="text-sm text-gray-600">
                    {metal.balance} {metal.metal_name === 'Gold' ? 'g' : 'g'} 
                  </Text>
                  <TouchableOpacity className="bg-[#013443] w-[42px] h-[20px] rounded-md p-1" onPress={() => sellMetal(metal)}>
                    <Text className="text-white text-center bottom-1">sell</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* Menu Items */}  
      {/* Add your menu items as before */}

      <View className="p-3">
<TouchableOpacity className="flex-row items-center justify-between py-3" onPress={YourInvoice}>
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[350px] h-[60px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">Your Invoices</Text>
        {/* {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>} */}
      </View>
    </View>
  </TouchableOpacity>

<TouchableOpacity className="flex-row items-center justify-between py-3" onPress={SipDeliveries}>
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[350px] h-[60px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">Sip Deliveries</Text>
        {/* {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>} */}
      </View>
    </View>
  </TouchableOpacity>

  
<TouchableOpacity className="flex-row items-center justify-between py-3" onPress={SipTransactions}>
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[350px] h-[60px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">Sip Transactions</Text>
        {/* {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>} */}
      </View>
    </View>
  </TouchableOpacity>

  
<TouchableOpacity className="flex-row items-center justify-between py-3" onPress={BankAccount}>
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[350px] h-[60px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">Bank Accounts</Text>
        {/* {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>} */}
      </View>
    </View>
  </TouchableOpacity>

  
<TouchableOpacity className="flex-row items-center justify-between py-3" onPress={walletTransactions}>
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[352px] h-[60px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">E-wallet to bankaccount transactions</Text>
        {/* {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>} */}
      </View>
    </View>
  </TouchableOpacity>

  
<TouchableOpacity className="flex-row items-center justify-between py-3" onPress={Nominee}>
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[350px] h-[60px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">Nominee Contacts</Text>
        {/* {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>} */}
      </View>
    </View>
  </TouchableOpacity>

  
<TouchableOpacity className="flex-row items-center justify-between py-3" onPress={Connection}>
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[350px] h-[60px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">Connection & Security</Text>
        {/* {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>} */}
      </View>
    </View>
  </TouchableOpacity>

  
<TouchableOpacity className="flex-row items-center justify-between py-3">
    <View className="ml-4 mr-4 rounded-md bg-[#E7E4E440] mb-2 w-[350px] h-[60px] ">
      <View className="ml-4 mt-3">
        <Text className="font-bold text-[19px] mb-2">Payment Methods</Text>
        {/* {subtitle && <Text className="text-[13px] text-[#000000B2]">{subtitle}</Text>} */}
      </View>
    </View>
  </TouchableOpacity>
         {/* ))} */}
       </View>
       <View className="flex-row ml-8">
         <AntDesign name="logout" size={24} color="black" />
         <Text className="ml-4 text-[18px]">Logout</Text>
       </View>
     </ScrollView>
   );
 };

export default AccountScreen;
