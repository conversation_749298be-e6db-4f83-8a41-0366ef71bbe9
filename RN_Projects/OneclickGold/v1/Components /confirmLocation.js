// import React, { useState, useEffect } from 'react';
// import {
//   View, Text, TextInput, Modal, TouchableOpacity,
//   Alert, ActivityIndicator, StyleSheet
// } from 'react-native';
// // import MapView, { Marker } from 'react-native-maps';
// import MapView from 'expo-map-view';
// import * as Location from 'expo-location';
// import { Ionicons } from '@expo/vector-icons'; // for the close icon

// const ConfirmLocationScreen = () => {
//   const [modalVisible, setModalVisible] = useState(false);
//   const [selectedPlace, setSelectedPlace] = useState('Select a location');
//   const [isLoading, setIsLoading] = useState(false);
//   const [currentLocation, setCurrentLocation] = useState({
//     latitude: 23.0225,
//     longitude: 72.5714,
//     latitudeDelta: 0.01,
//     longitudeDelta: 0.01,
//   });

//   const [formData, setFormData] = useState({
//     addressType: 'Home',
//     floor: '',
//     tower: '',
//     state: 'Gujarat',
//     pincode: '',
//     phone: '',
//     street: '',
//     street2: '',
//     city: 'Ahmedabad'
//   });

//   // Get current location on component mount
//   useEffect(() => {
//     (async () => {
//       let { status } = await Location.requestForegroundPermissionsAsync();
//       if (status !== 'granted') {
//         Alert.alert('Permission to access location was denied');
//         return;
//       }

//       let location = await Location.getCurrentPositionAsync({});
//       setCurrentLocation({
//         latitude: location.coords.latitude,
//         longitude: location.coords.longitude,
//         latitudeDelta: 0.01,
//         longitudeDelta: 0.01,
//       });
//     })();
//   }, []);

//   const handleMapPress = async (e) => {
//     const { coordinate } = e.nativeEvent;

//     try {
//       const address = await Location.reverseGeocodeAsync(coordinate);
//       if (address.length > 0) {
//         const place = address[0];
//         setSelectedPlace(place.name || place.street || 'Selected Location');

//         setFormData({
//           ...formData,
//           street: place.street || '',
//           street2: place.district || '',
//           city: place.city || 'Ahmedabad',
//           state: place.region || 'Gujarat',
//           pincode: place.postalCode || ''
//         });
//       }
//     } catch (error) {
//       console.error('Error getting address:', error);
//       Alert.alert('Error', 'Could not get address details');
//     }
//   };

//   const handleConfirmAddress = async () => {
//     if (!formData.pincode || !formData.street || !formData.city) {
//       Alert.alert('Error', 'Please fill in all required fields (Street, City, Pincode).');
//       return;
//     }

//     setIsLoading(true);

//     const requestBody = {
//       jsonrpc: 2.0,
//       params: {
//         name: formData.addressType || 'Home',
//         type: 'delivery',
//         street: formData.street,
//         street2: formData.street2,
//         city: formData.city,
//         state_id: 588, // static
//         zip: formData.pincode,
//         country_id: 104, // static
//         phone: formData.phone || "+1234567890",
//         mobile: formData.phone || "+1234567890",
//         email: "<EMAIL>"
//       }
//     };

//     try {
//       const response = await fetch('https://gold.arihantai.com/api/v1/addresses/create', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//           'Cookie': 'frontend_lang=en_US; session_id=5d7965ad1cf8a8c6acc826033296527271bbf913'
//         },
//         body: JSON.stringify(requestBody)
//       });

//       const data = await response.json();

//       if (data.result) {
//         Alert.alert('Success', 'Address saved successfully.');
//         setModalVisible(false);
//       } else {
//         Alert.alert('Error', 'Failed to save address.');
//         console.error('API Error:', data);
//       }

//     } catch (error) {
//       console.error('API call failed:', error);
//       Alert.alert('Error', 'Something went wrong. Please try again.');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const handleInputChange = (name, value) => {
//     setFormData(prev => ({
//       ...prev,
//       [name]: value
//     }));
//   };

//   return (
//     <View style={{ flex: 1 }}>
     
//       {/* Full-size map before modal */}
//       <MapView
//         style={{ flex: 1 }}
//         region={currentLocation}
//         onPress={handleMapPress}
//       >
//         <Marker coordinate={{
//           latitude: currentLocation.latitude,
//           longitude: currentLocation.longitude
//         }} />
//       </MapView>

//       {/* Change button */}
//       <View style={{ padding: 20, backgroundColor: 'white' }}>
//         <Text style={{ fontWeight: 'bold', fontSize: 16 }}>DELIVERING YOUR ORDER TO</Text>
//         <Text style={{ fontSize: 18, marginVertical: 5 }}>{selectedPlace}</Text>
//         <Text style={{ color: '#666' ,marginBottom:4}}>{formData.street2}, {formData.city}</Text>
        
//       <TouchableOpacity
//         onPress={() => setModalVisible(true)}
//         style={{
//           backgroundColor: '#013443',
//           padding: 10,
//           margin:2,
//           alignItems: 'center'
//         }}
//       >
//         <Text style={{ color: 'white', fontWeight: 'bold' }}>Change / Confirm Address</Text>
//       </TouchableOpacity>
//       </View>


//       {/* Modal for form input */}
//       <Modal visible={modalVisible} animationType="slide" transparent={true}>
//         <View style={styles.modalContainer}>
//           <View style={styles.modalInnerContainer}>
//             {/* Close Icon */}
//             <TouchableOpacity
//               style={styles.closeIcon}
//               onPress={() => setModalVisible(false)}
//             >
//               <Ionicons name="close" size={24} color="#333" />
//             </TouchableOpacity>

//             <Text style={styles.modalTitle}>Enter complete address</Text>

//             <View style={{ flexDirection: 'row', marginBottom: 15 }}>
//               {['Home', 'Work', 'Other'].map((type) => (
//                 <TouchableOpacity
//                   key={type}
//                   onPress={() => handleInputChange('addressType', type)}
//                   style={[
//                     styles.addressTypeButton,
//                     formData.addressType === type && styles.selectedAddressType
//                   ]}
//                 >
//                   <Text style={formData.addressType === type ? { color: 'white' } : {}}>{type}</Text>
//                 </TouchableOpacity>
//               ))}
//             </View>

//             <TextInput
//               style={styles.input}
//               placeholder="Floor"
//               value={formData.floor}
//               onChangeText={(text) => handleInputChange('floor', text)}
//             />

//             <TextInput
//               style={styles.input}
//               placeholder="Street 1"
//               value={formData.street}
//               onChangeText={(text) => handleInputChange('street', text)}
//             />

//             <TextInput
//               style={styles.input}
//               placeholder="Street 2"
//               value={formData.street2}
//               onChangeText={(text) => handleInputChange('street2', text)}
//             />

//             <TextInput
//               style={styles.input}
//               placeholder="City"
//               value={formData.city}
//               onChangeText={(text) => handleInputChange('city', text)}
//             />

//             <TextInput
//               style={styles.input}
//               placeholder="State"
//               value={formData.state}
//               onChangeText={(text) => handleInputChange('state', text)}
//             />

//             <TextInput
//               style={styles.input}
//               placeholder="Pincode"
//               value={formData.pincode}
//               onChangeText={(text) => handleInputChange('pincode', text)}
//               keyboardType="numeric"
//             />

//             <TextInput
//               style={styles.input}
//               placeholder="Phone Number"
//               value={formData.phone}
//               onChangeText={(text) => handleInputChange('phone', text)}
//               keyboardType="phone-pad"
//             />

//        <TextInput
//               style={styles.input}
//               placeholder="Email"
//               value={formData.email}
//               onChangeText={(text) => handleInputChange('email', text)}
//             />



//             <TouchableOpacity
//               style={styles.confirmButton}
//               onPress={handleConfirmAddress}
//               disabled={isLoading}
//             >
//               {isLoading ? (
//                 <ActivityIndicator color="white" />
//               ) : (
//                 <Text style={{ color: 'white', fontWeight: 'bold' }}>Confirm Address</Text>
//               )}
//             </TouchableOpacity>
//           </View>
//         </View>
//       </Modal>
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   modalContainer: {
//     flex: 1,
//     justifyContent: 'flex-end',
//     backgroundColor: 'rgba(0,0,0,0.5)',
//   },
//   modalInnerContainer: {
//     backgroundColor: 'white',
//     padding: 20,
//     borderTopRightRadius: 10,
//     borderTopLeftRadius: 10,
//     maxHeight: '90%'
//   },
//   modalTitle: {
//     fontSize: 20,
//     fontWeight: 'bold',
//     marginBottom: 15
//   },
//   addressTypeButton: {
//     padding: 10,
//     marginRight: 10,
//     backgroundColor: '#f0f0f0',
//     borderRadius: 5
//   },
//   selectedAddressType: {
//     backgroundColor: '#013443'
//   },
//   input: {
//     height: 42,
//     borderColor: '#ddd',
//     borderWidth: 1,
//     marginBottom: 5,
//     padding: 10,
//     borderRadius: 5,
//     backgroundColor: '#f9f9f9'
//   },
//   confirmButton: {
//     backgroundColor: '#013443',
//     height: 50,
//     justifyContent: 'center',
//     alignItems: 'center',
//     borderRadius: 5,
//     marginTop: 10
//   },
//   closeIcon: {
//     position: 'absolute',
//     right: 10,
//     top: 10,
//     zIndex: 1
//   }
// });

// export default ConfirmLocationScreen;



import React, { useState, useEffect , useContext  } from 'react';
import {
  View, Text, TextInput, Modal, TouchableOpacity,
  Alert, ActivityIndicator, StyleSheet
} from 'react-native';
import MapView, { Marker } from 'react-native-maps';  // Correct import for map
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons'; // for the close icon
import { SessionContext } from './login';


const ConfirmLocationScreen = () => {
  const { sessionId, loadSessionData } = useContext(SessionContext);
  
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPlace, setSelectedPlace] = useState('Select a location');
  const [isLoading, setIsLoading] = useState(false);
  const [currentLocation, setCurrentLocation] = useState({
    latitude: 23.0225,
    longitude: 72.5714,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  const [formData, setFormData] = useState({
    addressType: 'Home',
    floor: '',
    tower: '',
    state: 'Gujarat',
    pincode: '',
    phone: '',
    street: '',
    street2: '',
    city: 'Ahmedabad'
  });

  // Get current location on component mount
  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission to access location was denied');
        return;
      }

      let location = await Location.getCurrentPositionAsync({});
      setCurrentLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    })();
  }, []);

  const handleMapPress = async (e) => {
    const { coordinate } = e.nativeEvent;

    try {
      const address = await Location.reverseGeocodeAsync(coordinate);
      if (address.length > 0) {
        const place = address[0];
        setSelectedPlace(place.name || place.street || 'Selected Location');

        setFormData({
          ...formData,
          street: place.street || '',
          street2: place.district || '',
          city: place.city || 'Ahmedabad',
          state: place.region || 'Gujarat',
          pincode: place.postalCode || ''
        });
      }
    } catch (error) {
      console.error('Error getting address:', error);
      Alert.alert('Error', 'Could not get address details');
    }
  };

  const handleConfirmAddress = async () => {
    if (!formData.pincode || !formData.street || !formData.city) {
      Alert.alert('Error', 'Please fill in all required fields (Street, City, Pincode).');
      return;
    }

    setIsLoading(true);

    const requestBody = {
      jsonrpc: 2.0,
      params: {
        name: formData.addressType || 'Home',
        type: 'delivery',
        street: formData.street,
        street2: formData.street2,
        city: formData.city,
        state_id: 588, // static
        zip: formData.pincode,
        country_id: 104, // static
        phone: formData.phone || "+1234567890",
        mobile: formData.phone || "+1234567890",
        email: "<EMAIL>"
      }
    };

    try {
      const response = await fetch('https://gold.arihantai.com/api/v1/addresses/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Cookie': 'frontend_lang=en_US; session_id=5d7965ad1cf8a8c6acc826033296527271bbf913'
          'Cookie': `session_id=${sessionId}`,

        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      if (data.result) {
        Alert.alert('Success', 'Address saved successfully.');
        setModalVisible(false);
      } else {
        Alert.alert('Error', 'Failed to save address.');
        console.error('API Error:', data);
      }

    } catch (error) {
      console.error('API call failed:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <View style={{ flex: 1 }}>

      {/* Full-size map */}
      <MapView
        style={{ flex: 1 }}
        region={currentLocation}
        onPress={handleMapPress}
        showsUserLocation={true} // Show user location on the map
      >
        <Marker coordinate={{
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude
        }} />
      </MapView>

      {/* Location Display & Button */}
      <View style={{ padding: 20, backgroundColor: 'white' }}>
        <Text style={{ fontWeight: 'bold', fontSize: 16 }}>DELIVERING YOUR ORDER TO</Text>
        <Text style={{ fontSize: 18, marginVertical: 5 }}>{selectedPlace}</Text>
        <Text style={{ color: '#666', marginBottom: 4 }}>{formData.street2}, {formData.city}</Text>

        <TouchableOpacity
          onPress={() => setModalVisible(true)}
          style={{
            backgroundColor: '#013443',
            padding: 10,
            margin: 2,
            alignItems: 'center'
          }}
        >
          <Text style={{ color: 'white', fontWeight: 'bold' }}>Change / Confirm Address</Text>
        </TouchableOpacity>
      </View>

      {/* Modal for form input */}
      <Modal visible={modalVisible} animationType="slide" transparent={true}>
        <View style={styles.modalContainer}>
          <View style={styles.modalInnerContainer}>
            {/* Close Icon */}
            <TouchableOpacity
              style={styles.closeIcon}
              onPress={() => setModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>

            <Text style={styles.modalTitle}>Enter complete address</Text>

            {/* Address Type Options */}
            <View style={{ flexDirection: 'row', marginBottom: 15 }}>
              {['Home', 'Work', 'Other'].map((type) => (
                <TouchableOpacity
                  key={type}
                  onPress={() => handleInputChange('addressType', type)}
                  style={[styles.addressTypeButton, formData.addressType === type && styles.selectedAddressType]}
                >
                  <Text style={formData.addressType === type ? { color: 'white' } : {}}>{type}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Address Form */}
            <TextInput
              style={styles.input}
              placeholder="Floor"
              value={formData.floor}
              onChangeText={(text) => handleInputChange('floor', text)}
            />

            <TextInput
              style={styles.input}
              placeholder="Street 1"
              value={formData.street}
              onChangeText={(text) => handleInputChange('street', text)}
            />

            <TextInput
              style={styles.input}
              placeholder="Street 2"
              value={formData.street2}
              onChangeText={(text) => handleInputChange('street2', text)}
            />

            <TextInput
              style={styles.input}
              placeholder="City"
              value={formData.city}
              onChangeText={(text) => handleInputChange('city', text)}
            />

            <TextInput
              style={styles.input}
              placeholder="State"
              value={formData.state}
              onChangeText={(text) => handleInputChange('state', text)}
            />

            <TextInput
              style={styles.input}
              placeholder="Pincode"
              value={formData.pincode}
              onChangeText={(text) => handleInputChange('pincode', text)}
              keyboardType="numeric"
            />

            <TextInput
              style={styles.input}
              placeholder="Phone Number"
              value={formData.phone}
              onChangeText={(text) => handleInputChange('phone', text)}
              keyboardType="phone-pad"
            />

            <TextInput
              style={styles.input}
              placeholder="Email"
              value={formData.email}
              onChangeText={(text) => handleInputChange('email', text)}
            />

            {/* Confirm Address Button */}
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleConfirmAddress}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={{ color: 'white', fontWeight: 'bold' }}>Confirm Address</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalInnerContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    maxHeight: '90%'
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15
  },
  addressTypeButton: {
    padding: 10,
    marginRight: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 5
  },
  selectedAddressType: {
    backgroundColor: '#013443'
  },
  input: {
    height: 42,
    borderColor: '#ddd',
    borderWidth: 1,
    marginBottom: 5,
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#f9f9f9'
  },
  confirmButton: {
    backgroundColor: '#013443',
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    marginTop: 10
  },
  closeIcon: {
    position: 'absolute',
    right: 10,
    top: 10,
    zIndex: 1
  }
});

export default ConfirmLocationScreen;

