import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';

const WalletTransactionScreen = ({navigation}) => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch the transaction data from the API
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await fetch('https://gold.arihantai.com/api/wallet/transactions', {
        method: 'GET',
        headers: {
          'Cookie': 'frontend_lang=en_US; session_id=5d7965ad1cf8a8c6acc826033296527271bbf913',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && Array.isArray(data)) {
        // Format each transaction
        const formattedTransactions = data.map(transaction => ({
          id: transaction.id,
          reference: `TXN${transaction.id}`,
          date: formatDate(transaction.transaction_date),
          partner: transaction.partner_id,
          bankName: transaction.bank_id,
          amount: `₹ ${transaction.amount.toFixed(2)}`,
          status: capitalizeFirstLetter(transaction.status),
          notes: transaction.notes,
        }));
        
        setTransactions(formattedTransactions);
      } else {
        setError('No transaction data found');
      }
    } catch (error) {
      console.error('Error fetching data: ', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  useEffect(() => {
    fetchData(); // Fetch data when the component mounts
  }, []);

  const handleTransfer = () => {
    navigation.navigate('WalletBalance');
  };

  const handleRefresh = () => {
    setError(null);
    fetchData();
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="large" color="#013443" />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center bg-white px-4">
        <Text className="text-red-500 mb-4">{error}</Text>
        <TouchableOpacity 
          className="bg-[#013443] px-4 py-2 rounded-lg"
          onPress={handleRefresh}
        >
          <Text className="text-white">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white px-4 py-4">
      {/* Header */}
      <View className="flex-row items-center mb-6">
        <TouchableOpacity onPress={() => navigation.goBack()} className="ml-3">
          <Ionicons name="arrow-back-sharp" size={24} color="black" />
        </TouchableOpacity>
        <Text className="text-lg font-bold ml-4 flex-1 text-center">E-Wallet Transactions</Text>
      </View>

      {/* Transaction List */}
      {transactions.length > 0 ? (
        transactions.map((transaction) => (
          <View key={transaction.id} className="rounded-lg border border-gray-200 mb-4 p-4">
            <View className="flex-row justify-between mb-3">
              <Text className="font-bold">Reference:</Text>
              <Text>{transaction.reference}</Text>
            </View>
            
            <View className="flex-row justify-between mb-3">
              <Text className="font-bold">Date:</Text>
              <Text>{transaction.date}</Text>
            </View>
            
            <View className="flex-row justify-between mb-3">
              <Text className="font-bold">Bank:</Text>
              <Text>{transaction.bankName}</Text>
            </View>
            
            <View className="flex-row justify-between mb-3">
              <Text className="font-bold">Amount:</Text>
              <Text className={transaction.amount.startsWith('₹ -') ? 'text-red-500' : 'text-green-500'}>
                {transaction.amount}
              </Text>
            </View>
            
            <View className="flex-row justify-between mb-3">
              <Text className="font-bold">Status:</Text>
              <View className={`px-2 py-1 rounded-md ${
                transaction.status.toLowerCase() === 'completed' ? 'bg-green-100' : 
                transaction.status.toLowerCase() === 'failed' ? 'bg-red-100' : 'bg-yellow-100'
              }`}>
                <Text className={
                  transaction.status.toLowerCase() === 'completed' ? 'text-green-800' : 
                  transaction.status.toLowerCase() === 'failed' ? 'text-red-800' : 'text-yellow-800'
                }>
                  {transaction.status}
                </Text>
              </View>
            </View>
            
            {transaction.notes && (
              <View className="mt-2">
                <Text className="font-bold">Notes:</Text>
                <Text className="text-gray-600">{transaction.notes}</Text>
              </View>
            )}
          </View>
        ))
      ) : (
        <View className="flex-1 justify-center items-center py-8">
          <Text className="text-gray-500">No transactions found</Text>
        </View>
      )}

      {/* Transfer Button */}
      <TouchableOpacity
        className="bg-[#013443] rounded-lg mt-8 py-4 px-4 flex-row items-center justify-center mb-8"
        onPress={handleTransfer}
      >
        <Text className="text-white font-semibold">
          Transfer E-wallet to Bank Account
        </Text>
        <Ionicons name="arrow-forward-sharp" size={24} color="#FFFFFF" className="ml-1" />
      </TouchableOpacity>
    </ScrollView>
  );
};

export default WalletTransactionScreen;