import React, { useState, useEffect } from "react";
import { View, Text, TextInput, TouchableOpacity, Alert } from "react-native";

const NomineeForm = ({ route }) => {
  const { nomineeId } = route.params; // Get nomineeId passed from the previous screen
  const [nomineeData, setNomineeData] = useState({
    name: "",
    phone: "",
    email: "",
    relation: "",
  });

  useEffect(() => {
    // Fetch nominee data based on the nomineeId passed
    const fetchNomineeData = async () => {
      try {
        const response = await fetch(`https://gold.arihantai.com/api/v1/nominees/${nomineeId}`);
        const data = await response.json();
        if (data.status === "success" && data.data) {
          setNomineeData(data.data); // Set nominee data to state
        } else {
          Alert.alert("Error", "Failed to load nominee data.");
        }
      } catch (error) {
        console.error(error);
        Alert.alert("Error", "Failed to fetch nominee data.");
      }
    };

    fetchNomineeData();
  }, [nomineeId]);

  // Show loading state if nominee data is not available
  if (!nomineeData) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg font-semibold">Loading nominee data...</Text>
      </View>
    );
  }

  // Handle form submission
  const handleSave = async () => {
    const updatedData = {
      jsonrpc: "2.0",
      params: {
        child_id: nomineeData.id, // Use the nominee ID (child_id)
        name: nomineeData.name,
        phone: nomineeData.phone,
        email: nomineeData.email,
        relation: nomineeData.relation,
      },
    };

    console.log(updatedData, "data ::"); // Debugging: Log the data to verify it

    try {
      const response = await fetch("https://gold.arihantai.com/update_nominee_contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Cookie": "frontend_lang=en_US; session_id=d37013ba2566577ea41657f0200686ad4a3e0ab3", // Add session cookie
        },
        body: JSON.stringify(updatedData),
      });

      const result = await response.json();
      console.log(result);

      // Check if the result was successful
      if (result.result.success) {
        Alert.alert("Success", "Nominee contact updated successfully.");
      } else {
        Alert.alert("Error", "Failed to update nominee contact.");
      }
    } catch (error) {
      console.error(error);
      Alert.alert("Error", "An error occurred while updating the nominee contact.");
    }
  };

  return (
    <View className="flex-1 bg-white px-4 py-6 mb-2">
      <Text className="text-lg font-bold justify-center items-center">Nominee Form</Text>

      <View className="mb-4 mt-2 p-2">
        {/* Nominee Name */}
        <View className="mb-4">
          <Text className="text-[#000000] text-[16px] ml-1 mb-2 font-bold">Nominee Name</Text>
          <TextInput
            className="border border-[#D9D9D9] rounded-lg px-3 py-2 text-[#000000]"
            value={nomineeData.name}
            onChangeText={(value) => setNomineeData({ ...nomineeData, name: value })}
          />
        </View>

        {/* Nominee Phone */}
        <View className="mb-4">
          <Text className="text-[#000000] text-[16px] ml-1 mb-2 font-bold">Nominee Phone</Text>
          <TextInput
            className="border border-[#D9D9D9] rounded-lg px-3 py-2 text-[#000000]"
            value={nomineeData.phone}
            onChangeText={(value) => setNomineeData({ ...nomineeData, phone: value })}
          />
        </View>

        {/* Nominee Email */}
        <View className="mb-4">
          <Text className="text-[#000000] text-[16px] ml-1 mb-2 font-bold">Nominee Email</Text>
          <TextInput
            className="border border-[#D9D9D9] rounded-lg px-3 py-2 text-[#000000]"
            value={nomineeData.email}
            onChangeText={(value) => setNomineeData({ ...nomineeData, email: value })}
          />
        </View>

        {/* Relation */}
        <View className="mb-4">
          <Text className="text-[#000000] text-[16px] ml-1 mb-2 font-bold">Relation</Text>
          <TextInput
            className="border border-[#D9D9D9] rounded-lg px-3 py-2 text-[#000000]"
            value={nomineeData.relation}
            onChangeText={(value) => setNomineeData({ ...nomineeData, relation: value })}
          />
        </View>
      </View>

      {/* Save Button */}
      <View className="px-4 py-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className="bg-[#002E3D] border border-[#002E3D] rounded-md px-4 py-2"
          onPress={handleSave} // Save nominee data here
        >
          <Text className="text-white font-semibold text-center">Save</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default NomineeForm;
