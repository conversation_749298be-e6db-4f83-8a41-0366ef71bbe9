import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, ScrollView, Image, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from "@expo/vector-icons";

const LocationsScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [locationsData, setLocationsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch the locations data
  const fetchLocations = async () => {
    try {
      const response = await fetch('https://gold.arihantai.com/api/v1/warehouse', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cookie': 'frontend_lang=en_US; session_id=e40b9232ea60b8dc305cd328607b3292f9cdd2b8',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setLocationsData(data.data || []);
        setLoading(false);
      } else {
        setError('Failed to fetch locations');
        setLoading(false);
      }
    } catch (err) {
      setError('Error fetching data');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLocations();
  }, []);

  // Filter locations based on search query
  const filteredLocations = locationsData.filter(location =>
    location.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View className="bg-white p-5 ">
      <View className="flex-row items-center space-x-2 mb-1">
        {/* <View className="flex-1 flex-row items-center bg-gray-100 rounded-full px-4 py-2"> */}
        <View className="flex-1 flex-row items-center border mb-8 mt-10  border-gray-300  rounded-full px-4 py-2">
          <TextInput
            placeholder="Search all locations"
            value={searchQuery}
            onChangeText={setSearchQuery}
            className="flex-1 ml-2 text-black"
          />
        </View>
      </View>

      {/* Loading State */}
      {loading && <ActivityIndicator size="large" color="#0000ff" />}

      {/* Error State */}
      {error && !loading && <Text className="text-red-500 text-center">{error}</Text>}

      {/* Location Cards */}
      <ScrollView>
        {filteredLocations.map(location => (
          <View key={location.id} className="bg-white rounded-2xl border border-gray-200 mb-5 p-4">
            <View className="flex-row items-start space-x-4 mb-4">
              <Image
                source={{ uri: `https://gold.arihantai.com${location.image}` || 'https://via.placeholder.com/100' }}
                className="w-20 h-24 rounded-lg"
              />
              <View className="flex-1 ml-4 mb-2">
                <Text className="text-lg font-semibold text-black">{location.name}</Text>
                <Text className="mt-2 text-black-200">
                  {location.description}
                  {/* The trust oversees the temple’s operations, facilities for pilgrims, and various charitable initiatives, ensuring a smooth and devotional experience for millions of visitors each year. */}
                </Text>
              </View>
            </View>
            
            <TouchableOpacity
                  // className="mt-2 ml-4 border-[#013343] border py-2 px-20 self-start"
                  className="mt-2 mb-3 border-[#013443] flex-row justify-between  border p-2 rounded-md"
                  onPress={() => navigation.navigate('TrustProducts', { locationId: location.id })}
                >
                  <Text className="text-[#013343] text-lg rounded-lg">View Products</Text>
                  <Ionicons name="arrow-forward" size={20} color="#013443"  />

                </TouchableOpacity>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default LocationsScreen;