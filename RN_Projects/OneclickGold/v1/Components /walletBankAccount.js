import React, { useState, useEffect,useContext } from 'react';
import { View, Text, TextInput, Pressable, TouchableOpacity , ActivityIndicator , ScrollView,Alert} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SessionContext } from './login';


const WalletBalance = ({ navigation }) => {
  const { sessionId, loadSessionData } = useContext(SessionContext);
  
  const [selectedBank, setSelectedBank] = useState('');
  const [amount, setAmount] = useState('');
  const [note, setNote] = useState('');
  const [isSelected, setIsSelected] = useState(true);
  const [availableBalance, setAvailableBalance] = useState('');
  const [savedBanks, setSavedBanks] = useState([]);
  
  useEffect(() => {
    // Fetch wallet and bank details on component mount
    fetchWalletData();
  }, []);

const fetchWalletData = async () => {
  // const session_id = '5d7965ad1cf8a8c6acc826033296527271bbf913';
  const frontend_lang = 'en_US';

  try {
    const isSessionValid = session_id && session_id.length > 0;
    if (!isSessionValid) {
      throw new Error('Invalid session');
    }

    const response = await fetch('https://gold.arihantai.com/api/wallet/transfer', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': `frontend_lang=${frontend_lang}; session_id=${session_id}`,
      },
      credentials: 'include',
    });

    // First try to parse as JSON, if that fails, try to parse the text
    let responseData;
    try {
      responseData = await response.json();
    } catch (jsonError) {
      // If JSON parsing fails, try to get the text and parse manually
      const text = await response.text();
      try {
        responseData = JSON.parse(text);
      } catch (textError) {
        console.error('Failed to parse response:', text);
        throw new Error('Invalid response format');
      }
    }

    console.log(responseData, "Wallet data response");

    const wallet = responseData.user_wallet;
    const banks = responseData.user_banks;

    if (wallet && wallet.points !== undefined && Array.isArray(banks)) {
      setAvailableBalance(wallet.points); // Set the wallet balance
      const filteredBanks = banks.filter(bank => bank.bank_name && bank.bank_name.trim() !== '');
      setSavedBanks(filteredBanks); // Set the bank details
    } else {
      throw new Error('Invalid response data structure');
    }
  } catch (error) {
    console.error('Wallet data fetch error:', error);
    setAvailableBalance(0);
    setSavedBanks([]);

    Alert.alert(
      'Connection Error',
      'Unable to fetch wallet data. Please check your connection and try again.',
      [
        { 
          text: 'Retry', 
          onPress: () => fetchWalletData() 
        },
        { 
          text: 'OK',
          style: 'cancel'
        }
      ]
    );
  }
};



  const handleBankSelection = (bankName) => {
    setSelectedBank(bankName);
    setIsSelected(!isSelected); // Toggle the radio button state
  };

  const handleConfirmTransfer = async () => {
    // Input validation remains the same
    if (!selectedBank) {
      Alert.alert('Error', 'Please select a bank account');
      return;
    }
  
    if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }
  
    if (parseFloat(amount) > availableBalance) {
      Alert.alert('Error', 'Amount cannot exceed available balance');
      return;
    }
  
    const selectedBankObj = savedBanks.find(bank => bank.bank_name === selectedBank);
    if (!selectedBankObj) {
      Alert.alert('Error', 'Selected bank not found');
      return;
    }
  
    const session_id = '5d7965ad1cf8a8c6acc826033296527271bbf913';
    const frontend_lang = 'en_US';
  
    const requestData = {
      jsonrpc: "2.0",
      params: {
        amount: parseFloat(amount),
        bank_id: selectedBankObj.id,
        notes: note
      }
    };
  
    try {
      // setIsLoading(true); // Show loading state
      
      const response = await fetch('https://gold.arihantai.com/my/wallet/transfer/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cookie': `frontend_lang=${frontend_lang}; session_id=${session_id}`,
        },
        body: JSON.stringify(requestData),
      });
  
      const text = await response.text();
      const responseData = JSON.parse(text);
  
      if (responseData.error) {
        throw new Error(responseData.error.message || 'Transfer failed');
      }
  
      // Show success message
      Alert.alert(
        'Success', 
        `Transfer of ₹${amount} to ${selectedBank} was successful`,
        // [
        //   {
        //     text: 'OK',
        //     onPress: () => {
        //       // Clear form and refresh data
        //       // setAmount('');
        //       // setNote('');
        //       // setSelectedBank('');
        //       // fetchWalletData(); // Refresh wallet data
        //     }
        //   }
        // ]

      );
  
    } catch (error) {
      console.error('Transfer error:', error);
      Alert.alert(
        'Transfer Failed',
        error.message || 'Unable to process transfer. Please try again later.'
      );
    } finally {
      // setIsLoading(false); // Hide loading state
      Alert.alert("Transfer Failed", "Unable to process transfer. Please try again later.")
    }
    // fetchWalletData();
  };

  const Transactions = () => {
    navigation.navigate('WalletTransactionScreen'); // Navigate to WalletTransactionScreen
  };

  const CreateBankAccount = () => {
    navigation.navigate('CreateBankAccount')
  };

  return (
    <View className="flex-1 bg-white px-6 py-4">
    <ScrollView>
      {/* Header */}
      <View className="flex-row">
        <Ionicons name="arrow-back-sharp" size={24} color="black" />
        <Text className="text-lg text-center font-medium ml-4 text-black mb-6">Wallet Balance</Text>
      </View>

      {/* Available Balance */}
      <View className="flex-row justify-between items-center p-4 bg-[#FEF9ED] h-20 rounded-lg mb-4">
        <View>
          <Text className="text-base text-[#34B224]">Available Balance</Text>
          <Text className="text-base font-semibold ">₹ {availableBalance}</Text>
        </View>
        <TouchableOpacity className="flex-col items-center" onPress={Transactions}>
          <Ionicons name="eye" size={24} color="black" />
          <Text className="text-base font-medium text-black ml-2 underline">View Transactions</Text>
        </TouchableOpacity>
      </View>

      {/* Saved Bank Accounts */}
      <Text className="text-base font-medium text-black mb-2 mt-5">Saved Bank Accounts</Text>
      <View className="border-b border-dashed border-b-[#D9D9D9]" />
      <View>
        {savedBanks.length > 0 ? (
          savedBanks.map((bank, index) => (
            <Pressable
              key={index}
              className="flex-row items-center mt-2 h-12 justify-between rounded-lg p-4 mb-2"
              onPress={() => handleBankSelection(bank.bank_name)}
            >
              <Text className="text-sm text-black">{bank.bank_name}</Text>
              <Ionicons
                name={selectedBank === bank.bank_name ? 'radio-button-on' : 'radio-button-off'}
                size={20}
                color={selectedBank === bank.bank_name ? '#4184F3' : '#4184F3'}
              />
            </Pressable>
          ))
        ) : (
          <Text className="text-sm text-gray-500">No saved banks available</Text>
        )}
      </View>

      <Pressable className="flex-row mt-2 mb-2 h-12 p-3 border border-gray-300 rounded-lg" onPress={CreateBankAccount}>
        <Ionicons name="add" size={22} color="#013443" />
        <Text className="text-sm text-black ml-2">Add New Bank Account</Text>
      </Pressable>

      {/* Transfer to Bank Account */}
      <Text className="text-sm font-medium text-black mb-2">Transfer to Bank Account</Text>
      <View className="flex-row items-center h-10 border border-gray-300 rounded-lg mb-4">
        <Text className="px-4 text-lg text-black">₹</Text>
        <TextInput
          className="flex-1 p- text-[#D9D9D9] "
          placeholder="Custom Amount"
          placeholdercolor="#4184F3"
          keyboardType="numeric"
          value={amount}
          onChangeText={setAmount}
        />
      </View>

      {/* Add Note */}
      <Text className="text-sm font-medium text-black mb-2">Add Note (Optional)</Text>
      <TextInput
        className="border border-gray-300 h-24 rounded-lg p-3 mb-6 text-black"
        multiline
        value={note}
        onChangeText={setNote}
      />

      {/* Confirm Transfer Button */}
      <Pressable
        className="flex-row items-center justify-between w-full py-3 bg-[#013443] rounded-lg"
        onPress={handleConfirmTransfer}
      >
        <Text className="text-white text-lg font-medium ml-4">Confirm Transfer</Text>
        <View className="right-5">
          <Ionicons name="arrow-forward" size={20} color="white" className="ml-2" />
        </View>
      </Pressable>
      </ScrollView>
    </View>
  );
};

export default WalletBalance;

































































