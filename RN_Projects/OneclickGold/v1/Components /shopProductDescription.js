import React, { useState, useEffect,useContext } from "react";
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
} from "react-native";
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function ShopProduct({ route, navigation }) {
  // Fixed: Access the product ID correctly
  const prodId = route.params?.prodId;
  const initialProductData = route.params?.productData;
  
  console.log("Route params:", route.params);
  console.log("Product ID:", prodId);
  
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDeliveryOption, setSelectedDeliveryOption] = useState('Ambaji Trust - Ambaji (4 Day(s) Delivery)');
  const [selectedShippingMethod, setSelectedShippingMethod] = useState('Home Delivery - 4 Days Delivery to your doorstep');
  const [quantity, setQuantity] = useState(1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedTrust, setSelectedTrust] = useState('Ambaji');
  const [selectedWeight, setSelectedWeight] = useState(null);
  const [currentPrice, setCurrentPrice] = useState(initialProductData?.price || 0);
  const [currentVariant, setCurrentVariant] = useState(null);
  const [addingToCart, setAddingToCart] = useState(false);

  const placeholderImage = "https://via.placeholder.com/150";
  const trustOptions = ["Ambaji", "Other Trust"];
  const BASE_URL = "https://gold.arihantai.com";

  // Function to fetch product details from API
  const fetchProductDetails = async () => {
    if (!prodId) {
      setError("No product ID provided");
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      const response = await fetch(`${BASE_URL}/api/v1/shop_product/${prodId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }

      const data = await response.json();
      setProduct(data);
      
      // Initialize with first weight if available
      if (data?.data?.product?.weight_variants) {
        const weightOptions = Object.keys(data.data.product.weight_variants);
        if (weightOptions.length > 0) {
          const firstWeight = weightOptions[0];
          setSelectedWeight(firstWeight);
          updatePriceForWeight(firstWeight, data.data.product.weight_variants);
        }
      }
      
      setLoading(false);
    } catch (err) {
      console.error("Error fetching product details:", err);
      setError(err.message);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProductDetails();
  }, [prodId]);

  const updatePriceForWeight = (weight, weightVariants) => {
    if (weightVariants && weightVariants[weight]) {
      const variantInfo = weightVariants[weight];
      setCurrentPrice(variantInfo.price);
      setCurrentVariant(variantInfo.variant_id);
    }
  };

  const handleWeightChange = (weight) => {
    setSelectedWeight(weight);
    if (product?.data?.product?.weight_variants) {
      updatePriceForWeight(weight, product.data.product.weight_variants);
    }
  };

  const handleQuantityChange = (type) => {
    if (type === 'increment') {
      setQuantity(quantity + 1);
    } else if (type === 'decrement' && quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  // Function to add product to cart in AsyncStorage
  const addProductToCart = async (productData) => {
    try {
      // Get current cart items
      const cartItems = await AsyncStorage.getItem('cartItems');
      let cartArray = cartItems ? JSON.parse(cartItems) : [];
      
      // Check if the product is already in cart
      const existingProductIndex = cartArray.findIndex(
        item => item.variantId === productData.variantId
      );
      
      if (existingProductIndex >= 0) {
        // Update quantity if product already exists
        cartArray[existingProductIndex].quantity += productData.quantity;
        cartArray[existingProductIndex].totalPrice = 
          cartArray[existingProductIndex].price * cartArray[existingProductIndex].quantity;
      } else {
        // Add new product to cart
        cartArray.push(productData);
      }
      
      // Save updated cart back to AsyncStorage
      await AsyncStorage.setItem('cartItems', JSON.stringify(cartArray));
      
      return true;
    } catch (error) {
      console.error('Failed to save product to cart:', error);
      return false;
    }
  };
  
  const handleAddToCart = async () => {
    if (addingToCart) return;
  
    if (!currentVariant || !prodId) {
      Alert.alert("Error", "Please select a product variant");
      return;
    }  
    setAddingToCart(true);
  
    try {
      const response = await fetch(`${BASE_URL}/api/v1/cart/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: 2.0,
          params: {
            product_id: currentVariant,
            quantity: quantity,
          }
        }),
      });
  
      const data = await response.json();

      console.log(data, "data");
  
      if (response.ok && data?.result) {
        // Also save to AsyncStorage for offline access
        const cartProduct = {
          id: prodId,
          variantId: currentVariant,
          name: product?.data?.product?.display_name || initialProductData?.name,
          price: currentPrice,
          quantity: quantity,
          totalPrice: currentPrice * quantity,
          image: product?.data?.product?.image_1920 || initialProductData?.img_url,
          weight: selectedWeight
        };
        
        await addProductToCart(cartProduct);
        
        Alert.alert(
          "Success",
          "Product added to cart successfully!",
          [{ text: "OK", onPress: () => navigation.navigate('MyCart') }]
        );
      } else {
        throw new Error(data?.error?.message || 'Unexpected error');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      Alert.alert("Error", "Failed to add product to cart. Please try again.");
    } finally {
      setAddingToCart(false);
    }
  };
  
  const handleRetry = () => {
    setError(null);
    fetchProductDetails();
  };

  const RadioButton = ({ isSelected, onPress, label }) => (
    <TouchableOpacity
      onPress={onPress}
      className="flex-row items-center mb-2"
    >
      <View className={`w-4 h-4 rounded-full border-2 border-[#045180] items-center justify-center mr-3 ${isSelected ? 'border-[#045180]' : ''}`}>
        {isSelected && <View className="w-2 h-2 rounded-full bg-[#045180]" />}
      </View>
      <Text className="text-black text-sm">{label}</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#013443" />
        <Text className="mt-2">Loading product...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-4">
        <Text className="text-red-500 text-lg">Error: {error}</Text>
        <TouchableOpacity 
          className="mt-4 bg-[#013443] px-4 py-2 rounded-md"
          onPress={handleRetry}
        >
          <Text className="text-white">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Fallback to initial data if API fails but we have route params
  if ((!product || !product.data || !product.data.product) && initialProductData) {
    // Create a simplified view with just the data from navigation
    return (
      <View className="flex-1 bg-white p-4">
        <ScrollView>
          <Text className="text-lg font-bold mb-4" style={{color:'#202244'}}>
            {initialProductData.name}
          </Text>
          
          {initialProductData.img_url && (
            <View className="w-full h-64 justify-center items-center mb-4">
              <Image
                source={{ uri: initialProductData.img_url }}
                style={{ width: '100%', height: '100%' }}
                resizeMode="contain"
              />
            </View>
          )}
          
          <Text className="text-gray-600 font-bold text-base mb-4" style={{color:'#F4672A'}}>
            ₹{initialProductData.price?.toFixed(2) || '0.00'}/gm
          </Text>
          
          <View className="flex-row mt-5 mb-4">
            <View className="flex-row items-center w-[90px] h-[36px] border border-[#013443] rounded-md">
              <TouchableOpacity
                className="w-10 h-10 ml-2 mt-2 rounded-lg"
                onPress={() => handleQuantityChange('decrement')}
              >
                <Text className="text-xl font-bold">-</Text>
              </TouchableOpacity>
              <Text className="text-lg font-bold right-2">{quantity}</Text>
              <TouchableOpacity
                className="w-10 h-9 ml-3 mt-2"
                onPress={() => handleQuantityChange('increment')}
              >
                <Text className="text-xl font-bold">+</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity 
              className="bg-[#013443] w-[220px] h-[40px] left-4 rounded-md py-3 items-center"
              onPress={handleAddToCart}
            >
              <Text className="text-white text-md font-medium bottom-1">Add to Cart</Text>
            </TouchableOpacity>
          </View>
          
          <Text className="text-red-500 mt-4">
            Could not load detailed product information. Please check your connection and try again.
          </Text>
        </ScrollView>
      </View>
    );
  }

  if (!product || !product.data || !product.data.product) {
    return (
      <View className="flex-1 justify-center items-center p-4">
        <Text className="text-lg">No product data available</Text>
        <TouchableOpacity 
          className="mt-4 bg-[#013443] px-4 py-2 rounded-md"
          onPress={() => navigation.goBack()}
        >
          <Text className="text-white">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const productData = product.data.product;
  const warehouse = product.data.warehouses?.[0] || {};
  const alternative_products = product.data.alternative_products || [];
  const trending_products = product.data.trending_products || [];
  const weightVariants = productData.weight_variants || {};
  const weightOptions = Object.keys(weightVariants);
  const totalPrice = currentPrice * quantity;

  const getFullImageUrl = (imagePath) => {
    if (!imagePath) return placeholderImage;
    
    if (imagePath.startsWith('http')) return imagePath;
    
    return `${BASE_URL}${imagePath}`;
  };

  let productImageUrl = placeholderImage;
  
  // Handle different image formats from API
  if (productData.image_1920) {
    if (typeof productData.image_1920 === 'string') {
      if (productData.image_1920.startsWith('data:image')) {
        productImageUrl = productData.image_1920;
      } else if (productData.image_1920.startsWith('b\'') || productData.image_1920.startsWith('b"')) {
        // Handle Python byte string format
        const cleanedBase64Image = productData.image_1920.slice(2, -1);
        productImageUrl = `data:image/jpeg;base64,${cleanedBase64Image}`;
      } else {
        productImageUrl = getFullImageUrl(productData.image_1920);
      }
    }
  }

  const similar_products = alternative_products.map(product => ({
    id: product.id,
    image: getFullImageUrl(product.image_1920),
    title: product.name || "Product Name",
    price: `₹${product.list_price?.toFixed(2) || '0.00'}/gm`,
  }));

  // Prepare trending products data
  const trending_products_data = trending_products.map(product => ({
    id: product.id,
    image: getFullImageUrl(product.image_1920),
    title: product.name || "Product Name",
    price: `₹${product.list_price?.toFixed(2) || '0.00'}/gm`,
  }));

  return (
    <View className="flex-1 bg-white p-2">
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="bg-white">
          <Text className="mt-2 text-lg font-bold ml-4" style={{color:'#202244'}}>
            {productData.display_name}
          </Text>
          
          <View className="w-full h-64 justify-center items-center">
            <Image
              source={{ uri: productImageUrl }}
              style={{ width: '100%', height: '100%' }}
              resizeMode="contain"
              onError={(e) => {
                console.log("Image load error:", e.nativeEvent.error);
              }}
            />
          </View>
        </View>
        <View className="p-4 bg-white">
          <Text className="text-gray-600 font-bold text-base mb-4" style={{color:'#F4672A'}}>
            ₹{currentPrice.toFixed(2)}/gm
          </Text>
          
          {/* Weight Selection */}
          {weightOptions.length > 0 && (
            <>
              <Text className="text-lg font-bold mt-2" style={{color:'#202244'}}>Choose weight</Text>
              <View className="flex flex-row flex-wrap w-full mb-4 border-b border-[#D9D9D9] border-dashed">
                {weightOptions.map((weight, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleWeightChange(weight)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      width: '33%',
                      marginBottom: 18,
                    }}
                  >
                    <View
                      style={{
                        width: 16,
                        height: 16,
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor: selectedWeight === weight ? "#013443" : "#CCCCCC",
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: 5
                      }}
                    >
                      {selectedWeight === weight && (
                        <View
                          style={{
                            width: 8,
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: "#013443"
                          }}
                        />
                      )}
                    </View>
                    <Text style={{ color: selectedWeight === weight ? "#013443" : "#000" }}>
                      {weight}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </>
          )}

          {/* Product Properties */}
          {productData.attributes && productData.attributes.length > 0 && (
            <View className="border-b mr-2 border-dashed border-[#D9D9D9] pb-4 mb-4">
              <Text className="text-lg font-medium">Product Properties</Text>
              {productData.attributes.map((attribute, index) => (
                <View key={index} className="flex-row justify-between mt-2">
                  <Text className="text-black text-base font-medium text-center">
                    {attribute.attribute_name}
                  </Text>
                  <View className="pr-4">
                    <Text className="text-black text-base font-medium text-center">
                      {attribute.values.map(val => val.value_name).join(', ')}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          )}

          {/* Delivery Options */}
          <View className="mb-2 mt-2">
            <Text className="text-lg font-medium mb-2">Delivery Options</Text>
            
            <TouchableOpacity 
              onPress={() => setSelectedDeliveryOption(warehouse.name)}
              className="flex-row items-center my-2"
            >
              <View className={`w-4 h-4 rounded-full border justify-center items-center mr-2.5 ${
                selectedDeliveryOption === warehouse.name ? "border-[#013443]" : "border-[#CCCCCC]"
              }`}>
                {selectedDeliveryOption === warehouse.name && (
                  <View className="w-2 h-2 rounded-full bg-[#013443]" />
                )}
              </View>
              
              <TouchableOpacity 
                onPress={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex-1 flex-row items-center justify-between p-2 rounded"
              >
                <Text className={selectedDeliveryOption === warehouse.name ? "text-[#013443]" : "text-black"}>
                  {selectedTrust} Trust - {warehouse.name || 'Warehouse'} (4 Day(s) Delivery)
                </Text>
                <MaterialIcons 
                  name={isDropdownOpen ? "keyboard-arrow-up" : "keyboard-arrow-down"} 
                  size={24} 
                  color="black" 
                />
              </TouchableOpacity>
            </TouchableOpacity>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <View className="ml-6 bg-white rounded border border-gray-200 -mt-1">
                {trustOptions.map((trust, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      setSelectedTrust(trust);
                      setIsDropdownOpen(false);
                    }}
                    className={`p-2.5 ${
                      index !== trustOptions.length - 1 ? "border-b border-gray-200" : ""
                    }`}
                  >
                    <Text>{trust}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Shipping Methods */}
            <Text className="text-lg font-medium mb-2">Shipping Method</Text>
            {warehouse.delivery_options?.map((option, index) => (
              <RadioButton
                key={index}
                label={`${option.name} - ${option.description}`}
                isSelected={selectedShippingMethod === `${option.name} - ${option.description}`}
                onPress={() => setSelectedShippingMethod(`${option.name} - ${option.description}`)}
              />
            )) || (
              <RadioButton
                label="Home Delivery - 4 Days Delivery to your doorstep"
                isSelected={true}
                onPress={() => {}}
              />
            )}
          </View>

          {/* Quantity and Add to Cart */}
          <View className="flex-row mt-5 mb-2 border-b border-[#D9D9D9] ">
            <View className="flex-row items-center w-[90px] h-[36px] border border-[#013443] rounded-md mb-4">
              <TouchableOpacity
                className="w-10 h-10 ml-2 mt-2 rounded-lg"
                onPress={() => handleQuantityChange('decrement')}
              >
                <Text className="text-xl font-bold">-</Text>
              </TouchableOpacity>
              <Text className="text-lg font-bold right-2">{quantity}</Text>
              <TouchableOpacity
                className="w-10 h-9 ml-3 mt-2"
                onPress={() => handleQuantityChange('increment')}
              >
                <Text className="text-xl font-bold">+</Text>
              </TouchableOpacity>
            </View>

            {/* Add to Cart Button */}
            <TouchableOpacity 
              className="bg-[#013443] w-[220px] h-[40px] left-4 rounded-md py-3 items-center"
              onPress={handleAddToCart}
              disabled={addingToCart || !selectedWeight}
            >
              {addingToCart ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text className="text-white text-md font-medium bottom-1">Add to Cart</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Total Price */}
          <View className="flex-row justify-between border-b h-18 mb-2 mt-2 border-[#D9D9D9] bg-white">
            <Text className="text-lg font-bold ml-4 mb-2">₹{totalPrice.toFixed(2)}</Text>
            <Text className="text-lg font-bold mr-8 mb-2">Product Price Breakup</Text>
          </View>

          {/* Similar Products */}
          {similar_products.length > 0 && (
            <View className="mb-6 bg-white border-b border-[#D9D9D9]" >
              <Text className="text-xl font-bold mb-4 ml-4">Similar Products</Text>
              
              <FlatList
                data={similar_products}
                horizontal
                showsHorizontalScrollIndicator={false}
                renderItem={({ item }) => (
                  <TouchableOpacity 
                    className="ml-4 mb-4 w-40"
                    onPress={() => navigation.push('ShopProduct', { prodId: item.id })}
                  >
                    <Image
                      source={{ uri: item.image }}
                      className="w-full h-32 rounded-lg"
                    />
                    <Text 
                      numberOfLines={2} 
                      className="text-sm font-bold mt-2"
                      style={{color:'#202244'}}
                    >
                      {item.title}
                    </Text>
                    <Text 
                      className="text-xs font-bold mt-1 mb-1"
                      style={{color:'#F4672A'}}
                    >
                      {item.price}
                    </Text>
                    <TouchableOpacity 
                      className="bg-[#013443] w-[100px] h-[30px] p-2 rounded-md"
                      onPress={() => {
                        navigation.push('ShopProduct', { prodId: item.id });
                      }}
                    >
                      <Text className="text-white bottom-1">Add to Cart</Text>
                    </TouchableOpacity>
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item.id.toString()}
              />
            </View>
          )}

          <View className="mb-6 bg-white border-b border-[#D9D9D9]" style={{borderBottomColor:'#D9D9D9'}}>
            <Text className="text-xl ml-4 font-bold mb-4">About Us</Text>
            <Text className="text-black text-base text-justify ml-3 mr-3 mb-7">
              {productData.description_sale || "No description available"}
            </Text>
          </View>

          {trending_products_data.length > 0 && (
            <View className="mb-6 ml-5 bg-white border-b border-[#D9D9D9]">
              <Text className="text-xl font-bold mb-4">Trending Products</Text>
              <FlatList
                data={trending_products_data}
                horizontal
                showsHorizontalScrollIndicator={false}
                renderItem={({ item }) => (
                  <TouchableOpacity 
                    className="mb-6 mr-4 w-36"
                    onPress={() => navigation.push('ShopProduct', { prodId: item.id })}
                  >
                    <View className="bg-white border border-[#00000033] rounded-lg p-3 flex-col justify-between h-52">
                      <Image
                        source={{ uri: item.image }}
                        className="w-full h-24 rounded-lg"
                      />
                      <Text 
                        className="text-xs text-center font-bold mt-1"
                        numberOfLines={2}
                      >
                        {item.title}
                      </Text>
                      <Text className="text-[#F4672A] text-center mt-1 font-bold text-xs">
                        {item.price}
                      </Text>
                      <TouchableOpacity
                        className="bg-[#013443] mt-2 w-full py-1 rounded-lg items-center justify-center"
                        onPress={() => navigation.push('ShopProduct', { prodId: item.id })}
                      >
                        <Text className="text-white text-xs font-medium">View</Text>
                      </TouchableOpacity>
                    </View>
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item.id.toString()}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}