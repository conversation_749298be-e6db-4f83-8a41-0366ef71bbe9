import React, { useState,useContext } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, StyleSheet, ActivityIndicator, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';

const ForgotPasswordScreen = () => {
  // const { sessionId, loadSessionData } = useContext(SessionContext);
  
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigation = useNavigation();

  const handleResetPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await fetch('https://gold.arihantai.com/api/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Cookie': 'frontend_lang=en_US; session_id=${sessionId}',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.result?.error || 'Failed to send reset email');
      }

      Alert.alert(
        'Success', 
        data.result?.message || 'Password reset instructions sent to your email'
      );
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert(
        'Error',
        error.message || 'Network error. Please try again later.'
      );
    } finally {
      setIsLoading(false);
    }
    navigation.navigate('LoginPage')    
  };

  return (
    <View className="flex-1 bg-white p-4">
      <View className="top-5 p-6">
      <Image className="w-75 h-50 ml-8" source={require('../assets/1.png')} /> 
      <Text className="text-2xl font-bold mb-2 mt-5">Forgot Password?</Text>
      <Text className="text-gray-600 mb-4">Enter your email to receive a reset link</Text>
      
      <TextInput
        style={styles.input}
        placeholder="Email address"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
            
      <TouchableOpacity
        // style={[styles.button, isLoading && styles.disabledButton]}
        className="bg-[#013443] rounded-lg p-3 mb-4 w-full items-center"
        onPress={handleResetPassword}
        disabled={isLoading}
        activeOpacity={0.7}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Submit</Text>
        )}
      </TouchableOpacity>
    </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 24,
    backgroundColor: '#f8f9fa',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 32,
    textAlign: 'center',
    color: '#666',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 20,
    backgroundColor: '#fff',
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007bff',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
  },
  disabledButton: {
    backgroundColor: '#6c757d',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default ForgotPasswordScreen;