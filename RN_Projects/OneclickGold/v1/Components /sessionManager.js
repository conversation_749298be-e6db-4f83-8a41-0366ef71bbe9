import AsyncStorage from '@react-native-async-storage/async-storage';

// Session storage keys
const SESSION_ID_KEY = 'sessionId';
const USER_NAME_KEY = 'userName';

/**
 * Utility class for managing user session throughout the app
 */
class SessionManager {
  /**
   * Store session data after successful login
   * @param {string} sessionId - The session ID received from the server
   * @param {string} userName - The user's name
   * @returns {Promise<void>}
   */
  static async storeSession(sessionId, userName) {
    try {
      await AsyncStorage.multiSet([
        [SESSION_ID_KEY, sessionId],
        [USER_NAME_KEY, userName || ''],
      ]);
      console.log('Session stored successfully');
    } catch (error) {
      console.error('Error storing session:', error);
    }
  }

  /**
   * Get the current session ID
   * @returns {Promise<string|null>} The session ID or null if not found
   */
  static async getSessionId() {
    try {
      return await AsyncStorage.getItem(SESSION_ID_KEY);
    } catch (error) {
      console.error('Error retrieving session ID:', error);
      return null;
    }
  }

  /**
   * Get the current user name
   * @returns {Promise<string|null>} The user name or null if not found
   */
  static async getUserName() {
    try {
      return await AsyncStorage.getItem(USER_NAME_KEY);
    } catch (error) {
      console.error('Error retrieving user name:', error);
      return null;
    }
  }

  /**
   * Check if there is an active session
   * @returns {Promise<boolean>} True if there is an active session
   */
  static async hasActiveSession() {
    const sessionId = await this.getSessionId();
    return !!sessionId;
  }

  /**
   * Generate cookie string for API requests
   * @returns {Promise<string>} Cookie string with session ID
   */
  static async getCookieString() {
    const sessionId = await this.getSessionId();
    return sessionId ? `frontend_lang=en_US; session_id=${sessionId}` : '';
  }

  /**
   * Clear the session data (for logout)
   * @returns {Promise<void>}
   */
  static async clearSession() {
    try {
      await AsyncStorage.multiRemove([SESSION_ID_KEY, USER_NAME_KEY]);
      console.log('Session cleared successfully');
    } catch (error) {
      console.error('Error clearing session:', error);
    }
  }
}

export default SessionManager;