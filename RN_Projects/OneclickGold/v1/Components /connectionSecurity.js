import React, { useState,useContext } from "react";
import { View, Text, TextInput, TouchableOpacity, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from '@react-native-async-storage/async-storage';

const ConnectionSecurity = () => {
  const { sessionId, loadSessionData } = useContext(SessionContext);

  const [form, setForm] = useState({
    // password: "",
    newPassword: "",
    verifyPassword: "",
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (name, value) => {
    setForm({ ...form, [name]: value });
  };

  const handleSubmit = async () => {
    // Validate form
    if (!form.newPassword || !form.verifyPassword) {
      Alert.alert("Error", "All fields are required");
      return;
    }

    if (form.newPassword !== form.verifyPassword) {
      Alert.alert("Error", "New passwords don't match");
      return;
    }

    setLoading(true);

    try {
      // Get session_id from storage (you need to store it after login)
      // const sessionId = await AsyncStorage.getItem('session_id');
      // const sessionId = await AsyncStorage.getItem('session_id');
      const sessionId = sessionId;
      
      if (!sessionId) {
        throw new Error("Session not found. Please login again.");
      }

      const response = await fetch('https://gold.arihantai.com/api/change_password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `frontend_lang=en_US; session_id=${sessionId}`
        },
        body: JSON.stringify({
          password: form.password,
          new_password: form.newPassword,
          verify_password: form.verifyPassword
        })
      });

      const data = await response.json();
      console.log(data);

      if (data.success) {
        // Alert.alert("Success", "Password changed successfully");
        setForm({
          // password: "",
          newPassword: "",
          verifyPassword: "",
        });
      } 
      navigation.navigate('LoginPage');

    } catch (error) {
      console.error("Password change error:", error);
      // Alert.alert("Error", error.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
    navigation.navigate('HomePage');
  };

  return (
    <View className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center px-4 py-3">
        <TouchableOpacity>
          <Ionicons name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
        <Text className="text-lg font-bold ml-4 text-black">
          Connection & Security
        </Text>
      </View>

      {/* Form Card */}
      <View className="mx-4 mt-4 border-2 border-[#D9D9D9] rounded-lg p-4 bg-white">
        {/* Title */}
        <Text className="text-lg font-semibold mb-4 text-black">
          Change Password
        </Text>

        {/* Current Password Field */}
        {/* <View className="mb-4">
          <Text className="text-gray-700 mb-2 font-medium">Current Password:</Text>
          <TextInput
            className="border border-[#013443] rounded-md px-3 py-2 text-gray-700"
            secureTextEntry
            value={form.password}
            onChangeText={(value) => handleChange("password", value)}
            placeholder="Enter current password"
          />
        </View> */}

        {/* New Password Field */}
        <View className="mb-4">
          <Text className="text-gray-700 mb-2 font-medium">New Password:</Text>
          <TextInput
            className="border border-[#013443] rounded-md px-3 py-2 text-gray-700"
            secureTextEntry
            value={form.newPassword}
            onChangeText={(value) => handleChange("newPassword", value)}
            placeholder="Enter new password"
          />
        </View>

        {/* Verify Password Field */}
        <View className="mb-6">
          <Text className="text-gray-700 mb-2 font-medium">
            Verify New Password:
          </Text>
          <TextInput
            className="border border-[#013443] rounded-md px-3 py-2 text-gray-700"
            secureTextEntry
            value={form.verifyPassword}
            onChangeText={(value) => handleChange("verifyPassword", value)}
            placeholder="Re-enter new password"
          />
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          className="bg-[#002E3D] mb-5 justify-between rounded-md px-4 py-3 flex-row items-center"
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text className="text-white font-semibold mr-2 ml-4">
            {loading ? "Processing..." : "Change Password"}
          </Text>
          {!loading && <Ionicons name="arrow-forward" size={20} color="white" />}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ConnectionSecurity;