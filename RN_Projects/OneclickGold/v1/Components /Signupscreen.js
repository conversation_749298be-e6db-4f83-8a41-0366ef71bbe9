import React, { useState } from "react";
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView } from "react-native";

const SignUp = ({ navigation }) => {
  const [formData, setFormData] = useState({
    name: "",
    login: "", // Using this field for email
    phone: "",
    password: "",
    confirmPassword: "",
  });

  const handleInputChange = (key, value) => {
    setFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSubmit = async () => {
    // Input validation
    if (!formData.login || !formData.name || !formData.password || !formData.confirmPassword) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert("Error", "Passwords don't match");
      return;
    }

    try {
      // Prepare data in the format required by the API
      const requestData = {
        jsonrpc: "2.0",
        params: {
          login: formData.login,
          name: formData.name,
          password: formData.password,
          confirm_password: formData.confirmPassword,
        }
      };

      console.log("Sending data:", JSON.stringify(requestData));

      const response = await fetch("https://gold.arihantai.com/api/signup/otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      // console.log("API Response:", result);

      if (result.result && !result.error) {
        Alert.alert("Success", "OTP has been sent to your email!", [
          {
            text: "OK",
            onPress: () => {
              // Navigate to OTP verification screen with user data
              navigation.navigate("OTPVerify", {
                login: formData.login,
                name: formData.name,
                password: formData.password,
                confirmPassword: formData.confirmPassword,
                phone: formData.phone
              });
            }
          }
        ]);
      } else {
        // Handle error from API
        const errorMessage = result.error?.data?.message || "Something went wrong!";
        Alert.alert("Error", errorMessage);
      }
    } catch (error) {
      console.error("API Error:", error);
      Alert.alert("Error", "There was an issue connecting to the server.");
    }
  };

  return (
    <View className="flex-1 bg-white mb-2 p-5">
      <View className="flex-1 bg-white rounded-t-3xl p-2">
        <Text className="text-xl font-bold text-black p-2 text-center">Sign Up</Text>

        <ScrollView className="flex-1 mt-4 p-4" showsVerticalScrollIndicator={false}>
          {/* Email field */}
          <TextInput
            className="border border-gray-300 rounded-lg p-3 mb-4 w-full"
            placeholder="Enter email id"
            onChangeText={(text) => handleInputChange("login", text)}
            value={formData.login}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          {/* Name field */}
          <TextInput
            className="border border-gray-300 rounded-lg p-3 mb-4 w-full"
            placeholder="Enter Name"
            onChangeText={(text) => handleInputChange("name", text)}
            value={formData.name}
          />

          {/* Phone field */}
          <TextInput
            className="border border-gray-300 rounded-lg p-3 mb-4 w-full"
            placeholder="Enter Phone no"
            onChangeText={(text) => handleInputChange("phone", text)}
            value={formData.phone}
            keyboardType="phone-pad"
          />

          {/* Password field */}
          <TextInput
            className="border border-gray-300 rounded-lg p-3 mb-4 w-full"
            placeholder="Enter password"
            onChangeText={(text) => handleInputChange("password", text)}
            value={formData.password}
            secureTextEntry
          />

          {/* Confirm Password field */}
          <TextInput
            className="border border-gray-300 rounded-lg p-3 mb-4 w-full"
            placeholder="Confirm password"
            onChangeText={(text) => handleInputChange("confirmPassword", text)}
            value={formData.confirmPassword}
            secureTextEntry
          />

          {/* Submit button */}
          <TouchableOpacity
            className="bg-[#013443] rounded-lg py-3 mt-6"
            onPress={handleSubmit}
          >
            <Text className="text-white text-center text-lg font-medium">Sign Up</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </View>
  );
};

export default SignUp;