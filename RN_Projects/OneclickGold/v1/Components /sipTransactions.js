import React, { useState, useEffect,useContext} from 'react';
import { View, Text, TouchableOpacity, FlatList, Alert } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';

// import { Icon } from 'react-native-elements';

const SipTransactionsScreen = () => {
  const { sessionId, loadSessionData } = useContext(SessionContext);
  const [activeTab, setActiveTab] = useState('All');
  const [transactions, setTransactions] = useState([]); // Ensure transactions is initialized as an empty array
  const [loading, setLoading] = useState(false);
  const [metalBalance, setMetalBalance] = useState({
    Gold: { bought: 0, sold: 0 },
    Silver: { bought: 0, sold: 0 }
  });

  const [expandedTransactions, setExpandedTransactions] = useState({}); // Track expanded state for each transaction

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', options).replace(/ /g, '-');  // Replacing spaces with hyphen
  };

  const handleSell = () => {
    console.log('Sell button pressed');
  };

  const tabs = ['All Transactions', 'Gold Transactions', 'Silver Transactions'];

  // Fetch transactions from the API
  useEffect(() => {
    const fetchTransactions = async () => {
      setLoading(true);
      try {
        const response = await fetch('https://gold.arihantai.com/api/v1/transactions', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': `session_id=${sessionId}`,

            // 'Cookie': 'frontend_lang=en_US; session_id=d37013ba2566577ea41657f0200686ad4a3e0ab3', // replace with actual session ID
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch transactions');
        }

        const data = await response.json();
        if (data && data.data && Array.isArray(data.data.transactions)) {
          setTransactions(data.data.transactions); // Set transactions in state
          calculateMetalBalance(data.data.transactions); // Calculate metal balance on data load
        } else {
          console.error("Transactions data is not in the expected format");
          Alert.alert("Error", "Transactions data is not in the expected format");
        }
      } catch (error) {
        console.error(error);
        Alert.alert("Error", "Failed to load transactions");
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, []); // Runs only once when the component mounts

  // Calculate total bought and sold metal for Gold and Silver
  const calculateMetalBalance = (transactions) => {
    let newBalance = { Gold: { bought: 0, sold: 0 }, Silver: { bought: 0, sold: 0 } };

    transactions.forEach(transaction => {
      if (transaction.metal_name === 'Gold') {
        if (transaction.type === 'buy') {
          newBalance.Gold.bought += transaction.grams;
        } else if (transaction.type === 'sell') {
          newBalance.Gold.sold += transaction.grams;
        }
      } else if (transaction.metal_name === 'Silver') {
        if (transaction.type === 'buy') {
          newBalance.Silver.bought += transaction.grams;
        } else if (transaction.type === 'sell') {
          newBalance.Silver.sold += transaction.grams;
        }
      }
    });

    setMetalBalance(newBalance);
  };

  // Filter transactions based on activeTab
  const filterTransactions = () => {
    if (activeTab === 'Gold Transactions') {
      return transactions.filter(item => item.metal_name === 'Gold');
    }
    if (activeTab === 'Silver Transactions') {
      return transactions.filter(item => item.metal_name === 'Silver');
    }
    return transactions; // Default: All Transactions
  };

  const filteredTransactions = filterTransactions(); // Get the filtered transactions

  // Toggle the accordion for a given transaction
  const toggleTransactionAccordion = (transactionId) => {
    setExpandedTransactions(prevState => ({
      ...prevState,
      [transactionId]: !prevState[transactionId]
    }));
  };

  return (
    <View className="flex-1 bg-white p-4">
      <Text className="text-xl font-bold mb-4 mr-3">Your Metal Transactions</Text>

      <View className="bg-[#FEF9ED] p-3 rounded-xl mb-4">
        <View className="flex-row justify-between mt-2">
          <Text className="text-base font-semibold">Available Metal</Text>
          <Text className="text-base font-semibold">Buy (g)</Text>
          <Text className="text-base font-semibold">Sell (g)</Text>
        </View>
        <View className="flex-row justify-between mt-2 ml-12">
          {/* Show the available gold for buy and sell */}
          <Text>Gold</Text>
          <Text>{metalBalance.Gold.bought - metalBalance.Gold.sold}g</Text> {/* Available for sell */}
          <Text>{metalBalance.Gold.sold}g</Text> {/* Total sold */}
        </View>
        <View className="flex-row justify-between mt-2 ml-12">
          {/* Show the available silver for buy and sell */}
          <Text>Silver</Text>
          <Text>{metalBalance.Silver.bought - metalBalance.Silver.sold}g</Text> {/* Available for sell */}
          <Text>{metalBalance.Silver.sold}g</Text> {/* Total sold */}
        </View>
      </View>


      {/* Tabs */}
      <View className="flex-row mb-2 mr-2">
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            onPress={() => setActiveTab(tab)}
            className={`px-3 py-2 rounded-lg ${activeTab === tab ? 'bg-[#013443] text-white' : 'bg-[white]-200'}`}
          >
            <Text className={`${activeTab === tab ? 'text-white' : 'text-black'}`}>{tab}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Loading Indicator */}
      {loading && <Text className="text-center text-gray-600">Loading transactions...</Text>}

      {/* Transactions List */}
      {!loading && (
        <FlatList
          data={filteredTransactions}
          keyExtractor={(item) => item.id.toString()}
      //     renderItem={({ item }) => (
      //       <View className="mt-1 border border-gray-300 flex-row">
      //         {/* Metal Name with Border */}
      //         {/* <View className="flex-row border border-gray-200 justify-between items-center p-2"> */}
      //         <View className="flex-row justify-between items-center">
      //           {/* <Text className="font-semibold text-lg ml-3">{item.metal_name} 
      //             <Text className="ml-4">({item.grams}g)</Text></Text> */}
      //              <Text className="font-bold justify-between text-base">
      //   {item.metal_name} ({item.grams}g)
      // </Text>
      //           <Text className="ml- underline">{formatDate(item.date)}</Text>
      //           <TouchableOpacity
      //             onPress={() => toggleTransactionAccordion(item.id)} // Toggle accordion on press
      //             className="bg-[#013443] p-2 rounded-lg ml-20"
      //           >
      //             <Text className="text-white">{item.type}</Text>
      //           </TouchableOpacity>
      //           <TouchableOpacity
      //           className="ml-14"
      //             onPress={() => toggleTransactionAccordion(item.id)} // Toggle accordion on press
      //           >
      //                         <Ionicons name="caret-down-outline" size={24} color="#045180" />
                  
      //           </TouchableOpacity>
      //         </View>

      //         {/* Accordion for Transaction Details */}
      //         {expandedTransactions[item.id] && (
      //           <View className="mt-3 border-t border-gray-200 pt-2">
      //             <View className="flex-row justify-between mb-1">
      //               <Text className="text-[#000000cb]">Date</Text>
      //               <Text className="text-black font-medium">{item.date}</Text>
      //             </View>
      //             <View className="flex-row mt-1 p-1 justify-between border-b border-gray-200">
      //               <Text className="text-[#000000cb]">Metal</Text>
      //               <Text className="text-black font-medium">{item.metal_name}</Text>
      //             </View>
      //             <View className="flex-row mt-1 p-1 justify-between border-b border-gray-200">
      //               <Text className="text-[#000000cb]">Type</Text>
      //               <Text className="text-black font-medium">{item.type}</Text>
      //             </View>
      //             <View className="flex-row mt-1 p-1 justify-between border-b border-gray-200">
      //               <Text className="text-[#000000cb]">Grams</Text>
      //               <Text className="text-black font-medium">{item.grams}</Text>
      //             </View>
      //             <View className="flex-row mt-1 p-1 justify-between">
      //               <Text className="text-[#000000cb]">Price (₹)</Text>
      //               <Text className="text-black font-medium">{item.amount}</Text>
      //             </View>
      //           </View>
      //         )}
      //       </View>
      //     )}
      renderItem={({ item }) => (
        <View className="border border-gray-300 rounded-lg p-3 mb-3 bg-white">
          {/* Header Row */}
          <View className="flex-row justify-between items-center mb-2">
            {/* Left Section: Metal + Grams */}
            <Text className="font-semibold text-base flex-1">
              {item.metal_name} ({item.grams}g)
            </Text>
      
            {/* Center Section: Date */}
            <Text className="text-sm text-gray-600 mr-2 underline">
              {formatDate(item.date)}
            </Text>
      
            {/* Right Section: Buy/Sell Button */}
            <TouchableOpacity
              className={`px-3 py-1 rounded-lg ${item.type === 'buy' ? 'bg-green-600' : 'bg-red-600'}`}
              onPress={() => console.log(`${item.type} pressed`)}
            >
              <Text className="text-white capitalize">{item.type}</Text>
            </TouchableOpacity>
      
            {/* Arrow Toggle */}
            <TouchableOpacity
              onPress={() => toggleTransactionAccordion(item.id)}
              className="ml-3"
            >
              <Ionicons
                name={expandedTransactions[item.id] ? "caret-up-outline" : "caret-down-outline"}
                size={24}
                color="#045180"
              />
            </TouchableOpacity>
          </View>
      
          {/* Accordion Content */}
          {expandedTransactions[item.id] && (
            <View className="mt-2 border-t border-gray-200 pt-2">
              <View className="flex-row justify-between mb-1">
                <Text className="text-gray-600">Date</Text>
                <Text className="font-medium">{formatDate(item.date)}</Text>
              </View>
              <View className="flex-row justify-between mb-1">
                <Text className="text-gray-600">Metal</Text>
                <Text className="font-medium">{item.metal_name}</Text>
              </View>
              <View className="flex-row justify-between mb-1">
                <Text className="text-gray-600">Type</Text>
                <Text className="font-medium capitalize">{item.type}</Text>
              </View>
              <View className="flex-row justify-between mb-1">
                <Text className="text-gray-600">Grams</Text>
                <Text className="font-medium">{item.grams}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Price (₹)</Text>
                <Text className="font-medium">{item.amount}</Text>
              </View>
            </View>
          )}
        </View>
      )}
      
        />
      )}
    </View>
  );
};

export default SipTransactionsScreen;

