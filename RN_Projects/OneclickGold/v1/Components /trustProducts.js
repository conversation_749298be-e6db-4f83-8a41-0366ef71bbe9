import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Image, TouchableOpacity, ActivityIndicator, TextInput } from 'react-native';

const TrustProducts = ({ route, navigation }) => {
  const { locationId } = route.params; // Get the location ID from the params
  // console.log(route.params.locationId,"routes");
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [location, setLocation] = useState('');
  const [searchText, setSearchText] = useState('');

  // Function to navigate to the Product Page with product ID
  const productdesc = (productId) => {
    navigation.navigate('ProductPage', { prodId: productId });
  }

  // Function to fetch products based on locationId
  const fetchProducts = async () => {
    try {
      const response = await fetch(`https://gold.arihantai.com/api/v1/warehouse/${locationId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cookie': 'frontend_lang=en_US; session_id=87cc8b9fa670faf5ed501b9417c53415c9fa993e',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setLocation(data.warehouse);
        setProducts(data.products || []);
        setLoading(false);
      } else {
        setError('Failed to fetch products');
        setLoading(false);
      }
    } catch (err) {
      setError('Error fetching products');
      setLoading(false);
    }
  };

  const filteredProducts = products.filter((product) => {
    return product.name.toLowerCase().includes(searchText.toLowerCase());
  });

  useEffect(() => {
    fetchProducts();
  }, [locationId]);

  return (
    <View className="bg-white p-4">
      {/* Loading State */}
      {loading && <ActivityIndicator size="large" color="#0000ff" />}

      {/* Error State */}
      {error && !loading && <Text className="text-red-500 text-center">{error}</Text>}

      {/* Product Cards */}
      <ScrollView>
        <View className="flex-row items-center mb-4 border-b border-gray-500">
          <Text className="text-xl font-bold text-gray-800 mx-auto mt-2">{location.name || 'Ambaji Mata Trust'}</Text>
        </View>
        <View className="flex-row border border-gray-300 rounded-md px-4 py-2">
          <TextInput
            className=" text-gray-800 ml-2"
            placeholder="Search all Products"
            value={searchText}
            onChangeText={setSearchText}
          />
        </View>
        {filteredProducts.map(product => (
          <View key={product.id} className="bg-white border-b border-gray-500 mb-4 p-4">
            <View className="flex-row items-start space-x-4 ">
              <Image
                source={{ uri: `https://gold.arihantai.com${product.image_1920}` || 'https://via.placeholder.com/100' }}
                className="w-24 h-24 rounded-lg"
              />
              <View className="flex-1 ml-4">
                <Text className="text-lg font-semibold text-black">{product.name}</Text>
                {/* <Text className="mt-2 text-black-200">{product.description}</Text> */}
                <Text className="text-red-500 mt-1">₹{product.standard_price}</Text>

                {/* New Section: Show Available Grams */}
                {product.attributes && product.attributes.length > 0 && product.attributes[0].value_ids && product.attributes[0].value_ids.length > 0 && (
                  <Text className="text-sm text-gray-600 mt-2">
                    Also available in grams: {product.attributes[0].value_ids.map(value => value.name).join(", ")}
                  </Text>
                )}

                {/* Pass the product.id to the productdesc function */}
                <TouchableOpacity 
                  className="mt-2 bg-[#013343] border py-2 px-2 self-start w-40 h-10 rounded-xl"
                  onPress={() => productdesc(product.id)} // Use product.id here
                >
                  <Text className="text-white text-sm ml-2">See more Details</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default TrustProducts;

