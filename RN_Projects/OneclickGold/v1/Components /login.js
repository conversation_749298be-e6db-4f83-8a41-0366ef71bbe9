import React, { useState, useContext } from 'react';
import { View, Text, TextInput, Button, Image, Alert, TouchableOpacity, SafeAreaView, StatusBar, Platform, KeyboardAvoidingView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Ionicons from '@expo/vector-icons/Ionicons';

// Create a context for session management
export const SessionContext = React.createContext(null);

export const SessionProvider = ({ children }) => {
  const [sessionId, setSessionId] = useState(null);
  const [userName, setUserName] = useState('');
  const [userDetails, setUserDetails] = useState(null);

  const storeSessionData = async (sessionId, userName, userData) => {
    try {
      await AsyncStorage.setItem('sessionId', sessionId);
      await AsyncStorage.setItem('userName', userName);
      if (userData) {
        await AsyncStorage.setItem('userData', JSON.stringify(userData));
      }
      
      setSessionId(sessionId);
      setUserName(userName);
      setUserDetails(userData);
      return true;
    } catch (error) {
      console.error('Error storing session data:', error);
      return false;
    }
  };

  const clearSession = async () => {
    try {
      await AsyncStorage.multiRemove(['sessionId', 'userName', 'userData']);
      setSessionId(null);
      setUserName('');
      setUserDetails(null);
      return true;
    } catch (error) {
      console.error('Error clearing session data:', error);
      return false;
    }
  };

  const loadSessionData = async () => {
    try {
      const storedSessionId = await AsyncStorage.getItem('sessionId');
      const storedUserName = await AsyncStorage.getItem('userName');
      const storedUserData = await AsyncStorage.getItem('userData');
      
      setSessionId(storedSessionId);
      setUserName(storedUserName || '');
      setUserDetails(storedUserData ? JSON.parse(storedUserData) : null);
      
      return {
        sessionId: storedSessionId,
        userName: storedUserName,
        userData: storedUserData ? JSON.parse(storedUserData) : null
      };
    } catch (error) {
      console.error('Error loading session data:', error);
      return { sessionId: null, userName: '', userData: null };
    }
  };

  return (
    <SessionContext.Provider value={{ 
      sessionId, 
      userName, 
      userDetails,
      storeSessionData,
      clearSession,
      loadSessionData
    }}>
      {children}
    </SessionContext.Provider>
  );
};

const LoginPage = () => {
  const [login, setLogin] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigation = useNavigation();
  
  // Use the SessionContext
  const { storeSessionData } = useContext(SessionContext);

  const handleLogin = async () => {
    // Validation
    if (!login.trim()) {
      setError('Email is required');
      return;
    }
    if (!password.trim()) {
      setError('Password is required');
      return;
    }
    setError('');

    try {
      const response = await fetch('https://gold.arihantai.com/web/session/authenticate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          params: {
            db: 'gold.arihantai.com',
            login: login,
            password: password,
          }
        }),
      });

      const responseData = await response.json();
      
      if (responseData.error) {
        if (responseData.error.data && responseData.error.data.name === "odoo.exceptions.AccessDenied") {
          setError('User does not exist or incorrect credentials');
        } else {
          setError('An error occurred. Please try again.');
        }
        return;
      }

      // Extract the session ID from cookies
      const cookies = response.headers.get('Set-Cookie');
      const sessionId = extractSessionIdFromCookies(cookies);
      
      if (!sessionId) {
        setError('Failed to get session ID. Please try again.');
        return;
      }

      // Extract user information from the response
      const userName = responseData.result?.name || '';
      
      // Store all session data using the context function
      const stored = await storeSessionData(sessionId, userName, responseData.result);
      
      if (stored) {
        console.log('Session ID stored successfully:', sessionId);
        console.log('User logged in:', userName);
        
        // Navigate to Home with user name
        navigation.navigate('Gold', { userName });
      } else {
        setError('Failed to store session data. Please try again.');
      }
    } catch (error) {
      Alert.alert('Login Failed', 'Unable to login. Please try again later.');
      console.error('Login error:', error);
    }
  };

  const extractSessionIdFromCookies = (cookies) => {
    if (!cookies) return null;
    const match = cookies.match(/session_id=([^;]+)/);
    return match ? match[1] : null;
  };

  const ForgotPassword = () => {
    navigation.navigate('Forgot Password');
  }

  const LoginOtp = () => {
    navigation.navigate('LoginOTP');
  }

  const signup = () => {
    navigation.navigate('SignUpScreen');
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" />
      <Text className="text-3xl font-bold p-6"> Gold </Text>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1 justify-center p-2 bg-white"
      >
        {error ? <Text className="text-red-500 mb-4">{error}</Text> : null}

        <Text className="text-2xl font-bold underline mb-2"> LOGIN </Text>
        <Text style={{color:'#202244',fontSize:17}} className="mb-1 mt-2">Email id </Text>

        <TextInput
          className="border border-gray-300 rounded-lg p-3 mb-4 w-full"
          placeholder="Enter email id"
          value={login}
          onChangeText={setLogin}
          keyboardType="email-address"
        />
        
        <Text style={{color:'#202244',fontSize:17}} className="mb-1">Password</Text> 

        <TextInput
          className="border border-gray-300 rounded-lg p-3 mb-4 w-full"
          placeholder="Enter password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />
      
        <TouchableOpacity onPress={ForgotPassword}>
          <Text className="ml-60 text-md text-[#013443] mb-4 underline">Forgot Password?</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={handleLogin}
          className="bg-[#013443] rounded-lg p-3 mb-4 w-full items-center"
        >
          <Text className="text-white text-lg font-bold">Login</Text>
        </TouchableOpacity>

        <TouchableOpacity onPress={LoginOtp}>
          <Text className="ml-60 text-md text-[#013443] mb-4 underline">Login with OTP</Text>
        </TouchableOpacity>

        <Text className="text-sm ml-20">Not registered yet? <Text className="text-[#013443] underline" onPress={signup}>Create Account</Text></Text>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginPage;