// //     // import React, { useState, useEffect , useContext} from 'react';
// //     // import { View, Text, Image, ScrollView, TouchableOpacity, Modal } from 'react-native';
// //     // import { Ionicons } from '@expo/vector-icons';
// //     // import { SessionContext } from './login';
// //     // // import { NativeWindStyleSheet } from "nativewind";
// //     // // NativeWindStyleSheet.setOutput({
// //     // //   default: "native",
// //     // // });


// //     // const OrderConfirmationScreen = () => {
// //     //     const { sessionId, loadSessionData } = useContext(SessionContext);
// //     //     const [orderData, setOrderData] = useState(null);
// //     //     const [recommendedProducts, setRecommendedProducts] = useState([]);
// //     //     const [modalVisible, setModalVisible] = useState(false);

// //     //     useEffect(() => {
// //     //         // Fetch order data and recommended products
// //     //         // This is a mock implementation. Replace with actual API calls.
// //     //         setOrderData({
// //     //             orderId: '123456',
// //     //             total: 715.25,
// //     //             deliveryAddress: '2-566, New Arjun Commercial Complex, above Himalayan Fresh, Pratapgunj, Vadodara, Gujarat 390018',
// //     //             orderItems: [
// //     //                 { id: 1, name: 'Product 1', image: 'https://example.com/product1.jpg' },
// //     //                 { id: 2, name: 'Product 2', image: 'https://example.com/product2.jpg' },
// //     //             ],
// //     //         });

// //     //         setRecommendedProducts([
// //     //             { id: 1, name: 'SHARPE men Rio Tip', price: 499.0,date:'9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
// //     //             { id: 2, name: 'SHARPE men Rio Tip', price: 499.0,date:'9 August', image: 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,pg_1/max/5d027445c5e54e859945ac5a00e8bc3e_9366/Ultraboost_21_Shoes_White_GZ4966_01_standard.jpg' },
// //     //             { id: 3, name: 'SHARPE men Rio Tip', price: 499.0,date:'9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
// //     //             { id: 4, name: 'SHARPE men Rio Tip', price: 499.0,date:'9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
// //     //         ]);
// //     //     }, []);

// //     //     const [cart, setCart] = useState({});

// //     //     const addToCart = (productId) => {
// //     //         setCart((prevCart) => ({
// //     //             ...prevCart,
// //     //             [productId]: (prevCart[productId] || 0) + 1,
// //     //         }));
// //     //     };

// //     //     const removeFromCart = (productId) => {
// //     //         setCart((prevCart) => {
// //     //             const newCount = (prevCart[productId] || 0) - 1;
// //     //             if (newCount <= 0) {
// //     //                 const { [productId]: removed, ...remainingCart } = prevCart;
// //     //                 return remainingCart;
// //     //             }
// //     //             return { ...prevCart, [productId]: newCount };
// //     //         })
// //     //     }
// //     //     // const removeFromCart = (productId) => {
// //     //     //     setCart((prevCart) => ({
// //     //     //         ...prevCart,
// //     //     //         [productId]: Math.max((prevCart[productId] || 0) - 1, 0),
// //     //     //     }));
// //     //     // };

// //     //     if (!orderData) {
// //     //         return <Text className="text-center p-4">Loading...</Text>;
// //     //     }

// //     //     return (
// //     //         <View className="flex-1">
// //     //             <ScrollView>
// //     //                 <View>
// //     //                     <View className="absolute top-0 left-0 right-0 bg-[#1FD271] opacity-30 h-80 rounded-b-[70px]"></View>
// //     //                     <Image source={{ uri: 'https://s3-alpha-sig.figma.com/img/9025/3854/bfb524c9413296e4a197a335e1632b5c?Expires=1735516800&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4&Signature=VA9eaML4RjdXZwLpNDfEa32vcueQ253SCZIk6t9bXKsoQpmUycz4FTbl5MdMK3jZuzLXiqUFCBzqMgaNMr8Z6wkdTpo0VKBzP-X4JhCEgAZiCspXIUTcjJowUuLkvRwPI1hlaaKdXV40RMp2YZxfKao29I1uXuAK5h~Y2Pxg~5oXeYXHMP50bhFeF9fi1kiuM2r~Ph63DX9R6KwJNRyKfs5AQ-nJrmwHpMwlGdE~jYZI8BnzNFL7oDd1EueGqQV3xSIPxd4RNh0rBQzMeCLYCm1PLiIlIS-s1oT1IP90TJx0OMTDVIqQsdC6K1ETaGLF9d5mu5RuLfEw-YvLE~cw8g__'}} resizeMode='cover' className="h-20 w-60 self-center mt-14" />
// //     //                     <View className="bg-[#44E436] w-8 h-8 rounded-full self-center bottom-10 left-1">
// //     //                         <Ionicons name="checkmark-outline" size={24} color="white" style={{ top: 4, left: 4 }} />
// //     //                     </View>
// //     //                     <Text className="font-[Mulish] font-[900] text-[21px] leading-[21px] self-center right-3">Wohoo! Order Confirmed</Text>
// //     //                     <View className="bg-white mx-10  rounded-[50px] mt-4 shadow shadow-[#202244] h-[26px]"><Text className="text-[#29A41F] font-[Mulish] font-[700] text-[13px] leading-[13px] mt-2 left-3">You saved ₹{orderData.total.toFixed(2)} in this order</Text></View>
// //     //                     <View>
// //     //                         <View className="bg-white shadow-xl py-14 rounded-[13px] mx-8 mt-4 flex-row">
// //     //                             {orderData.orderItems.map((index, item) => (
// //     //                                 <TouchableOpacity
// //     //                                     key={item.id}
// //     //                                     onPress={() => setModalVisible(true)}
// //     //                                     className="h-6 w-6 bg-gray-300 rounded-full p-6 mr-4 left-5 bottom-8">
// //     //                                     {/* <Image source={{ uri: item.image }} className="w-full h-full" /> */}
// //     //                                 </TouchableOpacity>

// //     //                             ))}

// //     //                         </View>
// //     //                         <View className="border border-[#d9d9d9] mx-14 bottom-16"></View>
// //     //                         <TouchableOpacity onPress={() => setModalVisible(true)} className="ml-14 bottom-12">
// //     //                         <Text className="text-[#202244] font-[Mulish] font-[700] text-[14px] leading-[17px]">View My Orders</Text></TouchableOpacity>
// //     //                     </View>
// //     //                 </View>

// //     //                 <View>
// //     //                     <Text className="font-[Mulish] font-[900] text-[16px] leading-[17px] ml-10">Delivery Address</Text>
// //     //                     <Text className="font-[Mulish] font-[600] text-[13px] leading-[15px] ml-10 text-[#202244]/50 w-4/5">{orderData.deliveryAddress} </Text>
// //     //                 </View>

// //     //                 <TouchableOpacity className="bg-[#E3AA48] mt-4 mx-8 py-3 rounded-[13px] "><Text className="text-white text-center text-[21px] ">Continue Shopping</Text></TouchableOpacity>
// //     //                 <View className="border border-[#C5C4C4] mt-4 border-dashed"></View>

// //     //                 <ScrollView showsHorizontalScrollIndicator={false}>
// //     //                     <View className="flex-row flex-wrap justify-center">
// //     //                         {recommendedProducts.map((product) => (
// //     //                             <View key={product.id} className="bg-white rounded-lg p-2 shadow w-0.5/2 m-1 mx-2 mb-4">
// //     //                                 <Image
// //     //                                     source={{ uri: product.image }}
// //     //                                     className="w-full h-32 rounded mb-2 bg-gray-300"
// //     //                                 />
// //     //                                 {/* <View className="flex-row justify-between items-center bg-[#045180] rounded-[6px] mx-4 bottom-5">
// //     //                 <TouchableOpacity
// //     //                     onPress={() => removeFromCart(product.id)}
// //     //                     className="p-1"
// //     //                 >
// //     //                     <Ionicons name="remove" size={20} color="white" />
// //     //                 </TouchableOpacity>
// //     //                 <Text className="text-white font-[700]">{cart[product.id] || 0}</Text>
// //     //                 <TouchableOpacity
// //     //                     onPress={() => addToCart(product.id)}
// //     //                     className=" p-1"
// //     //                 >
// //     //                     <Ionicons name="add" size={20} color="white" />
// //     //                 </TouchableOpacity>
// //     //                 </View> */}
// //     //                                 <View className="flex-row justify-between items-center mx-4 bottom-5">
// //     //                                     {!cart[product.id] || cart[product.id] === 0 ? (
// //     //                                         <TouchableOpacity
// //     //                                             onPress={() => addToCart(product.id)}
// //     //                                             className="bg-[#F3FAFF] flex-row py-1 px-6 border border-[#045180] rounded-[6px] items-center left-1"
// //     //                                         >
// //     //                                             <Text className="font-[Jost] font-bold font-[700] text-[14px] text-[#202244] left-2">ADD</Text>
// //     //                                             <Ionicons name="add" size={16} color="black" style={{left:20,bottom:3}} />
// //     //                                         </TouchableOpacity>
// //     //                                     ) : (
// //     //                                         <View className="flex-row bg-[#045180] justify-between items-center rounded-[6px] px-4 left-0.5">
// //     //                                             <TouchableOpacity
// //     //                                                 onPress={() => removeFromCart(product.id)}
// //     //                                                 className="p-1"
// //     //                                             >
// //     //                                                 <Ionicons name="remove" size={20} color="white" style={{right:10}} />
// //     //                                             </TouchableOpacity>
// //     //                                             <Text className="text-white font-[700]">{cart[product.id]}</Text>
// //     //                                             <TouchableOpacity
// //     //                                                 onPress={() => addToCart(product.id)}
// //     //                                                 className="p-1"
// //     //                                             >
// //     //                                                 <Ionicons name="add" size={20} color="white" style={{left:10}} />
// //     //                                             </TouchableOpacity>
// //     //                                         </View>
// //     //                                     )}
// //     //                                 </View>

// //     //                                 <Text className="font-bold mt-2 mb-1">{product.name}</Text>
// //     //                                 <Text className="text-[#F4672A] mb-2">₹{product.price.toFixed(2)}</Text>
// //     //                                 <Text className="text-[#202244]"
// //     //                                 >Get it by Tomorrow,<Text className="font-semibold">{product.date}</Text>
// //     //                                 </Text>
// //     //                             </View>
// //     //                         ))}
// //     //                     </View>
// //     //                 </ScrollView>

// //     //                 <Modal
// //     //                     animationType="slide"
// //     //                     transparent={true}
// //     //                     visible={modalVisible}
// //     //                     onRequestClose={() => setModalVisible(false)}
// //     //                 >
// //     //                     <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
// //     //                         <View className="bg-white p-6 rounded-lg w-4/5">
// //     //                             <Text className="text-xl font-bold mb-4">Order Details</Text>
// //     //                             {orderData.orderItems.map((item) => (
// //     //                                 <View key={item.id} className="flex-row items-center mb-2">
// //     //                                     <Image source={{ uri: item.image }} className="w-12 h-12 rounded mr-2" />
// //     //                                     <Text>{item.name}</Text>
// //     //                                 </View>
// //     //                             ))}
// //     //                             <TouchableOpacity
// //     //                                 onPress={() => setModalVisible(false)}
// //     //                                 className="bg-blue-500 py-2 rounded mt-4"
// //     //                             >
// //     //                                 <Text className="text-white text-center">Close</Text>
// //     //                             </TouchableOpacity>
// //     //                         </View>
// //     //                     </View>
// //     //                 </Modal>
// //     //             </ScrollView>
// //     //         </View>
// //     //     );
// //     // };

// //     // export default OrderConfirmationScreen;


// //     import React, { useState, useEffect, useContext } from 'react';
// // import { View, Text, Image, ScrollView, TouchableOpacity, Modal, Alert } from 'react-native';
// // import { Ionicons } from '@expo/vector-icons';
// // import { SessionContext } from './login';

// // const OrderConfirmationScreen = ({ route }) => {
// //     const { sessionId, loadSessionData } = useContext(SessionContext);
// //     const [orderData, setOrderData] = useState(null);
// //     const [recommendedProducts, setRecommendedProducts] = useState([]);
// //     const [modalVisible, setModalVisible] = useState(false);
// //     const [loading, setLoading] = useState(true);
// //     const [cart, setCart] = useState({});

// //     // Get order ID from navigation params
// //     const orderId = route?.params?.orderId || 123; // fallback for testing

// //     useEffect(() => {
// //         fetchOrderData();
// //     }, []);

// //     const fetchOrderData = async () => {
// //         try {
// //             setLoading(true);
            
// //             // Test the simple endpoint first
// //             const testResponse = await fetch('https://gold.arihantai.com/api/v1/test', {
// //                 method: 'POST',
// //                 headers: {
// //                     'Content-Type': 'application/json',
// //                     'Cookie': `session_id=${sessionId}` // if you have sessionId
// //                 },
// //                 body: JSON.stringify({
// //                     params: { test: 'hello' }
// //                 })
// //             });

// //             const testResult = await testResponse.json();
// //             console.log('Test API Response:', testResult);

// //             // Now try the actual order confirmation
// //             const response = await fetch('https://gold.arihantai.com/api/v1/order/confirmation', {
// //                 method: 'POST',
// //                 headers: {
// //                     'Content-Type': 'application/json',
// //                     'Cookie': `session_id=${sessionId}` // if you have sessionId
// //                 },
// //                 body: JSON.stringify({
// //                     params: { 
// //                         order_id: orderId,
// //                         user_id: 1 // Add user_id for testing with auth='public'
// //                     }
// //                 })
// //             });

// //             if (!response.ok) {
// //                 throw new Error(`HTTP error! status: ${response.status}`);
// //             }

// //             const result = await response.json();
// //             console.log('Order API Response:', result);

// //             if (result.status === 'success') {
// //                 setOrderData({
// //                     orderId: result.data.order.name,
// //                     total: result.data.order.amount_total,
// //                     deliveryAddress: formatAddress(result.data.shipping_address),
// //                     orderItems: result.data.order.lines.map(line => ({
// //                         id: line.product_id,
// //                         name: line.name,
// //                         image: line.image_url ? `https://gold.arihantai.com${line.image_url}` : null,
// //                         quantity: line.quantity,
// //                         price: line.price_unit
// //                     })),
// //                     customer: result.data.customer,
// //                     payment: result.data.payment
// //                 });
// //             } else {
// //                 Alert.alert('Error', result.message || 'Failed to fetch order data');
// //                 // Fallback to mock data for development
// //                 setMockData();
// //             }
// //         } catch (error) {
// //             console.error('Error fetching order data:', error);
// //             Alert.alert('Error', 'Failed to fetch order data. Using mock data.');
// //             // Fallback to mock data
// //             setMockData();
// //         } finally {
// //             setLoading(false);
// //         }
// //     };

// //     const formatAddress = (address) => {
// //         if (!address) return 'Address not available';
        
// //         const parts = [
// //             address.street,
// //             address.street2,
// //             address.city,
// //             address.state,
// //             address.zip,
// //             address.country
// //         ].filter(Boolean);
        
// //         return parts.join(', ');
// //     };

// //     const setMockData = () => {
// //         // Your existing mock data
// //         setOrderData({
// //             orderId: '123456',
// //             total: 715.25,
// //             deliveryAddress: '2-566, New Arjun Commercial Complex, above Himalayan Fresh, Pratapgunj, Vadodara, Gujarat 390018',
// //             orderItems: [
// //                 { id: 1, name: 'Product 1', image: 'https://example.com/product1.jpg' },
// //                 { id: 2, name: 'Product 2', image: 'https://example.com/product2.jpg' },
// //             ],
// //         });

// //         setRecommendedProducts([
// //             { id: 1, name: 'SHARPE men Rio Tip', price: 499.0, date: '9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
// //             { id: 2, name: 'SHARPE men Rio Tip', price: 499.0, date: '9 August', image: 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,pg_1/max/5d027445c5e54e859945ac5a00e8bc3e_9366/Ultraboost_21_Shoes_White_GZ4966_01_standard.jpg' },
// //             { id: 3, name: 'SHARPE men Rio Tip', price: 499.0, date: '9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
// //             { id: 4, name: 'SHARPE men Rio Tip', price: 499.0, date: '9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
// //         ]);
// //     };

// //     const addToCart = (productId) => {
// //         setCart((prevCart) => ({
// //             ...prevCart,
// //             [productId]: (prevCart[productId] || 0) + 1,
// //         }));
// //     };

// //     const removeFromCart = (productId) => {
// //         setCart((prevCart) => {
// //             const newCount = (prevCart[productId] || 0) - 1;
// //             if (newCount <= 0) {
// //                 const { [productId]: removed, ...remainingCart } = prevCart;
// //                 return remainingCart;
// //             }
// //             return { ...prevCart, [productId]: newCount };
// //         });
// //     };

// //     if (loading) {
// //         return (
// //             <View className="flex-1 justify-center items-center">
// //                 <Text className="text-center p-4">Loading order details...</Text>
// //             </View>
// //         );
// //     }

// //     if (!orderData) {
// //         return (
// //             <View className="flex-1 justify-center items-center">
// //                 <Text className="text-center p-4">Failed to load order data</Text>
// //                 <TouchableOpacity onPress={fetchOrderData} className="bg-blue-500 px-4 py-2 rounded">
// //                     <Text className="text-white">Retry</Text>
// //                 </TouchableOpacity>
// //             </View>
// //         );
// //     }

// //     return (
// //         <View className="flex-1">
// //             <ScrollView>
// //                 {/* Your existing UI code remains the same */}
// //                 <View>
// //                     <View className="absolute top-0 left-0 right-0 bg-[#1FD271] opacity-30 h-80 rounded-b-[70px]"></View>
// //                     <Image source={{ uri: 'https://s3-alpha-sig.figma.com/img/9025/3854/bfb524c9413296e4a197a335e1632b5c?Expires=1735516800&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4&Signature=VA9eaML4RjdXZwLpNDfEa32vcueQ253SCZIk6t9bXKsoQpmUycz4FTbl5MdMK3jZuzLXiqUFCBzqMgaNMr8Z6wkdTpo0VKBzP-X4JhCEgAZiCspXIUTcjJowUuLkvRwPI1hlaaKdXV40RMp2YZxfKao29I1uXuAK5h~Y2Pxg~5oXeYXHMP50bhFeF9fi1kiuM2r~Ph63DX9R6KwJNRyKfs5AQ-nJrmwHpMwlGdE~jYZI8BnzNFL7oDd1EueGqQV3xSIPxd4RNh0rBQzMeCLYCm1PLiIlIS-s1oT1IP90TJx0OMTDVIqQsdC6K1ETaGLF9d5mu5RuLfEw-YvLE~cw8g__'}} resizeMode='cover' className="h-20 w-60 self-center mt-14" />
// //                     <View className="bg-[#44E436] w-8 h-8 rounded-full self-center bottom-10 left-1">
// //                         <Ionicons name="checkmark-outline" size={24} color="white" style={{ top: 4, left: 4 }} />
// //                     </View>
// //                     <Text className="font-[Mulish] font-[900] text-[21px] leading-[21px] self-center right-3">Wohoo! Order Confirmed</Text>
// //                     <View className="bg-white mx-10 rounded-[50px] mt-4 shadow shadow-[#202244] h-[26px]">
// //                         <Text className="text-[#29A41F] font-[Mulish] font-[700] text-[13px] leading-[13px] mt-2 left-3">
// //                             You saved ₹{orderData.total.toFixed(2)} in this order
// //                         </Text>
// //                     </View>
                    
// //                     {/* Rest of your existing UI code */}
// //                     <View>
// //                         <View className="bg-white shadow-xl py-14 rounded-[13px] mx-8 mt-4 flex-row">
// //                             {orderData.orderItems.map((item, index) => (
// //                                 <TouchableOpacity
// //                                     key={item.id}
// //                                     onPress={() => setModalVisible(true)}
// //                                     className="h-6 w-6 bg-gray-300 rounded-full p-6 mr-4 left-5 bottom-8"
// //                                 >
// //                                     {item.image && (
// //                                         <Image source={{ uri: item.image }} className="w-full h-full rounded-full" />
// //                                     )}
// //                                 </TouchableOpacity>
// //                             ))}
// //                         </View>
// //                         <View className="border border-[#d9d9d9] mx-14 bottom-16"></View>
// //                         <TouchableOpacity onPress={() => setModalVisible(true)} className="ml-14 bottom-12">
// //                             <Text className="text-[#202244] font-[Mulish] font-[700] text-[14px] leading-[17px]">View My Orders</Text>
// //                         </TouchableOpacity>
// //                     </View>
// //                 </View>

// //                 <View>
// //                     <Text className="font-[Mulish] font-[900] text-[16px] leading-[17px] ml-10">Delivery Address</Text>
// //                     <Text className="font-[Mulish] font-[600] text-[13px] leading-[15px] ml-10 text-[#202244]/50 w-4/5">
// //                         {orderData.deliveryAddress}
// //                     </Text>
// //                 </View>

// //                 {/* Rest of your existing code for recommended products, modal, etc. */}
// //                 <TouchableOpacity className="bg-[#E3AA48] mt-4 mx-8 py-3 rounded-[13px]">
// //                     <Text className="text-white text-center text-[21px]">Continue Shopping</Text>
// //                 </TouchableOpacity>
                
// //                 {/* Add your recommended products section here */}
                
// //             </ScrollView>
// //         </View>
// //     );
// // };

// // export default OrderConfirmationScreen;


// import React, { useState, useEffect } from 'react';
// import { View, Text, Image, ScrollView, TouchableOpacity, Modal } from 'react-native';
// import { Ionicons } from '@expo/vector-icons';

// const OrderConfirmationScreen = () => {
//     const [orderData, setOrderData] = useState(null);
//     const [recommendedProducts, setRecommendedProducts] = useState([]);
//     const [modalVisible, setModalVisible] = useState(false);
//     const [loading, setLoading] = useState(true);
//     const [error, setError] = useState(null);

//     useEffect(() => {
//         const fetchOrderData = async () => {
//             try {
//                 // In a real app, you would get the order ID from navigation params or context
//                 const orderId = 38; // This would typically come from navigation params
                
//                 // Simulating API call with your response data
//                 const response = {
//                     status: "success",
//                     message: "Payment initiated",
//                     data: {
//                         order_id: 38,
//                         order_name: "S00038",
//                         amount_total: 776.50,
//                         requires_payment: true,
//                         delivery_address: {
//                             name: "team",
//                             street: "C-508 Dev complex",
//                             city: "Surat",
//                             state: "Haryana",
//                             zip: "IN",
//                             country: "India"
//                         }
//                     }
//                 };

//                 if (response.status === "success") {
//                     const formattedAddress = `${response.data.delivery_address.street}, ${response.data.delivery_address.city}, ${response.data.delivery_address.state}, ${response.data.delivery_address.zip}, ${response.data.delivery_address.country}`;
                    
//                     setOrderData({
//                         orderId: response.data.order_id,
//                         orderName: response.data.order_name,
//                         total: response.data.amount_total,
//                         deliveryAddress: formattedAddress,
//                         orderItems: [
//                             // These would typically come from the API response
//                             { id: 1, name: 'Silver Coin - Plain', image: 'https://example.com/product1.jpg' },
//                             { id: 2, name: 'Gold Coin - Anniversary', image: 'https://example.com/product2.jpg' },
//                         ],
//                     });

//                     // Simulate recommended products fetch
//                     setRecommendedProducts([
//                         { id: 1, name: 'SHARPE men Rio Tip', price: 499.0, date: '9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
//                         { id: 2, name: 'SHARPE men Rio Tip', price: 499.0, date: '9 August', image: 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,pg_1/max/5d027445c5e54e859945ac5a00e8bc3e_9366/Ultraboost_21_Shoes_White_GZ4966_01_standard.jpg' },
//                         { id: 3, name: 'SHARPE men Rio Tip', price: 499.0, date: '9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
//                         { id: 4, name: 'SHARPE men Rio Tip', price: 499.0, date: '9 August', image: 'https://m.media-amazon.com/images/I/81gdP4cN6eL._AC_SL1500_.jpg' },
//                     ]);
//                 } else {
//                     setError(response.message || "Failed to fetch order details");
//                 }
//             } catch (err) {
//                 setError(err.message || "An error occurred");
//             } finally {
//                 setLoading(false);
//             }
//         };

//         fetchOrderData();
//     }, []);

//     const [cart, setCart] = useState({});

//     const addToCart = (productId) => {
//         setCart((prevCart) => ({
//             ...prevCart,
//             [productId]: (prevCart[productId] || 0) + 1,
//         }));
//     };

//     const removeFromCart = (productId) => {
//         setCart((prevCart) => {
//             const newCount = (prevCart[productId] || 0) - 1;
//             if (newCount <= 0) {
//                 const { [productId]: removed, ...remainingCart } = prevCart;
//                 return remainingCart;
//             }
//             return { ...prevCart, [productId]: newCount };
//         });
//     };

//     if (loading) {
//         return <Text className="text-center p-4">Loading...</Text>;
//     }

//     if (error) {
//         return <Text className="text-center p-4 text-red-500">{error}</Text>;
//     }

//     return (
//         <View className="flex-1">
//             <ScrollView>
//                 <View>
//                     <View className="absolute top-0 left-0 right-0 bg-[#1FD271] opacity-30 h-80 rounded-b-[70px]"></View>
//                     <Image source={{ uri: 'https://s3-alpha-sig.figma.com/img/9025/3854/bfb524c9413296e4a197a335e1632b5c?Expires=1735516800&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4&Signature=VA9eaML4RjdXZwLpNDfEa32vcueQ253SCZIk6t9bXKsoQpmUycz4FTbl5MdMK3jZuzLXiqUFCBzqMgaNMr8Z6wkdTpo0VKBzP-X4JhCEgAZiCspXIUTcjJowUuLkvRwPI1hlaaKdXV40RMp2YZxfKao29I1uXuAK5h~Y2Pxg~5oXeYXHMP50bhFeF9fi1kiuM2r~Ph63DX9R6KwJNRyKfs5AQ-nJrmwHpMwlGdE~jYZI8BnzNFL7oDd1EueGqQV3xSIPxd4RNh0rBQzMeCLYCm1PLiIlIS-s1oT1IP90TJx0OMTDVIqQsdC6K1ETaGLF9d5mu5RuLfEw-YvLE~cw8g__'}} resizeMode='cover' className="h-20 w-60 self-center mt-14" />
//                     <View className="bg-[#44E436] w-8 h-8 rounded-full self-center bottom-10 left-1">
//                         <Ionicons name="checkmark-outline" size={24} color="white" style={{ top: 4, left: 4 }} />
//                     </View>
//                     <Text className="font-[Mulish] font-[900] text-[21px] leading-[21px] self-center right-3">Wohoo! Order Confirmed</Text>
//                     <View className="bg-white mx-10 rounded-[50px] mt-4 shadow shadow-[#202244] h-[26px]">
//                         <Text className="text-[#29A41F] font-[Mulish] font-[700] text-[13px] leading-[13px] mt-2 left-3">
//                             You saved ₹{orderData.total.toFixed(2)} in this order
//                         </Text>
//                     </View>
//                     <View>
//                         <View className="bg-white shadow-xl py-14 rounded-[13px] mx-8 mt-4 flex-row">
//                             {orderData.orderItems.map((item, index) => (
//                                 <TouchableOpacity
//                                     key={index}
//                                     onPress={() => setModalVisible(true)}
//                                     className="h-6 w-6 bg-gray-300 rounded-full p-6 mr-4 left-5 bottom-8">
//                                 </TouchableOpacity>
//                             ))}
//                         </View>
//                         <View className="border border-[#d9d9d9] mx-14 bottom-16"></View>
//                         <TouchableOpacity onPress={() => setModalVisible(true)} className="ml-14 bottom-12">
//                             <Text className="text-[#202244] font-[Mulish] font-[700] text-[14px] leading-[17px]">View My Orders</Text>
//                         </TouchableOpacity>
//                     </View>
//                 </View>

//                 <View>
//                     <Text className="font-[Mulish] font-[900] text-[16px] leading-[17px] ml-10">Delivery Address</Text>
//                     <Text className="font-[Mulish] font-[600] text-[13px] leading-[15px] ml-10 text-[#202244]/50 w-4/5">
//                         {orderData.deliveryAddress}
//                     </Text>
//                 </View>

//                 <TouchableOpacity className="bg-[#E3AA48] mt-4 mx-8 py-3 rounded-[13px]">
//                     <Text className="text-white text-center text-[21px]">Continue Shopping</Text>
//                 </TouchableOpacity>
//                 <View className="border border-[#C5C4C4] mt-4 border-dashed"></View>

//                 <ScrollView showsHorizontalScrollIndicator={false}>
//                     <View className="flex-row flex-wrap justify-center">
//                         {recommendedProducts.map((product) => (
//                             <View key={product.id} className="bg-white rounded-lg p-2 shadow w-0.5/2 m-1 mx-2 mb-4">
//                                 <Image
//                                     source={{ uri: product.image }}
//                                     className="w-full h-32 rounded mb-2 bg-gray-300"
//                                 />
//                                 <View className="flex-row justify-between items-center mx-4 bottom-5">
//                                     {!cart[product.id] || cart[product.id] === 0 ? (
//                                         <TouchableOpacity
//                                             onPress={() => addToCart(product.id)}
//                                             className="bg-[#F3FAFF] flex-row py-1 px-6 border border-[#045180] rounded-[6px] items-center left-1"
//                                         >
//                                             <Text className="font-[Jost] font-bold font-[700] text-[14px] text-[#202244] left-2">ADD</Text>
//                                             <Ionicons name="add" size={16} color="black" style={{left:20,bottom:3}} />
//                                         </TouchableOpacity>
//                                     ) : (
//                                         <View className="flex-row bg-[#045180] justify-between items-center rounded-[6px] px-4 left-0.5">
//                                             <TouchableOpacity
//                                                 onPress={() => removeFromCart(product.id)}
//                                                 className="p-1"
//                                             >
//                                                 <Ionicons name="remove" size={20} color="white" style={{right:10}} />
//                                             </TouchableOpacity>
//                                             <Text className="text-white font-[700]">{cart[product.id]}</Text>
//                                             <TouchableOpacity
//                                                 onPress={() => addToCart(product.id)}
//                                                 className="p-1"
//                                             >
//                                                 <Ionicons name="add" size={20} color="white" style={{left:10}} />
//                                             </TouchableOpacity>
//                                         </View>
//                                     )}
//                                 </View>

//                                 <Text className="font-bold mt-2 mb-1">{product.name}</Text>
//                                 <Text className="text-[#F4672A] mb-2">₹{product.price.toFixed(2)}</Text>
//                                 <Text className="text-[#202244]">
//                                     Get it by Tomorrow,<Text className="font-semibold">{product.date}</Text>
//                                 </Text>
//                             </View>
//                         ))}
//                     </View>
//                 </ScrollView>

//                 <Modal
//                     animationType="slide"
//                     transparent={true}
//                     visible={modalVisible}
//                     onRequestClose={() => setModalVisible(false)}
//                 >
//                     <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
//                         <View className="bg-white p-6 rounded-lg w-4/5">
//                             <Text className="text-xl font-bold mb-4">Order Details - {orderData.orderName}</Text>
//                             {orderData.orderItems.map((item) => (
//                                 <View key={item.id} className="flex-row items-center mb-2">
//                                     <Image source={{ uri: item.image }} className="w-12 h-12 rounded mr-2" />
//                                     <View>
//                                         <Text>{item.name}</Text>
//                                         <Text className="text-gray-500">Order ID: {orderData.orderId}</Text>
//                                     </View>
//                                 </View>
//                             ))}
//                             <View className="mt-4">
//                                 <Text className="font-bold">Delivery Address:</Text>
//                                 <Text>{orderData.deliveryAddress}</Text>
//                             </View>
//                             <TouchableOpacity
//                                 onPress={() => setModalVisible(false)}
//                                 className="bg-blue-500 py-2 rounded mt-4"
//                             >
//                                 <Text className="text-white text-center">Close</Text>
//                             </TouchableOpacity>
//                         </View>
//                     </View>
//                 </Modal>
//             </ScrollView>
//         </View>
//     );
// };

// export default OrderConfirmationScreen;


// OrderConfirmationScreen.jsx

import React from 'react';
import { View, Text, TouchableOpacity, FlatList, Image, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';

const OrderConfirmationScreen = ({ route }) => {
    console.log("routes",route);
  const navigation = useNavigation();
//   const route = useRoute();
  const { delivery_address, order_id,order_name } = route.params;
  

  return (
    <ScrollView className="flex-1 bg-white">
      <View className="bg-green-100 p-4 rounded-b-3xl items-center">
        <Text className="text-lg font-semibold text-green-700">Wohoo! Order Confirmed</Text>
        <Text className="text-sm text-green-600 mt-1">
          {/* You saved ₹{savedAmount.toFixed(2)} in this order */}
          {/* You saved ₹{savedAmount.toFixed(2)} in this order */}
        </Text>

        <View className="mt-4 p-4 bg-white rounded-xl shadow-md w-11/12">
          
          <TouchableOpacity onPress={() => navigation.navigate('YourInvoice')}
          >
            <Text className="text-gray-800 font-medium">View My Orders</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View className="p-4">
        <Text className="font-semibold text-gray-700">Delivery Address</Text>
        <Text className="text-gray-600 mt-1">{delivery_address}</Text>
      </View>

      <TouchableOpacity
        className="bg-yellow-400 mx-4 rounded-lg p-3 items-center"
        onPress={() => navigation.navigate('Shop')}
      >
        <Text className="font-semibold text-white">Continue Shopping</Text>
      </TouchableOpacity>

      <Text className="font-bold text-lg mx-4 mt-6 mb-2">Recommended Products</Text>

      <FlatList
        horizontal
        // data={orderItems}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item }) => (
          <View className="bg-gray-100 m-2 p-3 rounded-lg w-36 items-center">
            <Image
              source={{ uri: item.image || 'https://via.placeholder.com/100' }}
              className="w-20 h-20 rounded mb-2"
            />
            <Text className="text-center text-sm font-medium">{item.name}</Text>
            <Text className="text-red-600 font-bold">₹{item.price}</Text>
            <TouchableOpacity className="bg-white px-4 py-1 mt-2 rounded-full border border-gray-400">
              <Text>Add</Text>
            </TouchableOpacity>
          </View>
        )}
      />
    </ScrollView>
  );
};

export default OrderConfirmationScreen;
