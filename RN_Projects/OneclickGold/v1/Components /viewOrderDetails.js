import React, { useState, useEffect,useContext } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, Modal, Alert } from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import AntDesign from '@expo/vector-icons/AntDesign';
import Ionicons from '@expo/vector-icons/Ionicons';
// import * as Print from 'expo-print';
// import * as Sharing from 'expo-sharing';
// import * as MediaLibrary from 'expo-media-library';

export default function OrderDetails({navigation, route}) {
  const [orderData, setOrderData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const [selectedOption, setSelectedOption] = useState("invoice");
  const [isDownloading, setIsDownloading] = useState(false);

  const toggleModal = () => {
    setIsVisible(!isVisible);
  };

  const selectOption = (option) => {
    setSelectedOption(option);
  };

  useEffect(() => {
    const fetchOrderData = async () => {
      try {
        const response = await fetch(`https://gold.arihantai.com/api/v1/invoices/${route.params.orderId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch order details");
        }
        const data = await response.json();
        setOrderData(data.data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchOrderData();
  }, []);

  // Generate PDF content for the invoice
  const generateInvoiceHtml = () => {
    if (!orderData) return '';
    
    const { amount_total, currency, invoice_date, name, lines, billing_address, product_summary } = orderData;
    
    return `
      <html>
        <head>
          <style>
            body { font-family: Arial; margin: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .invoice-title { font-size: 24px; font-weight: bold; }
            .details { margin: 20px 0; }
            table { width: 100%; border-collapse: collapse; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .total { font-weight: bold; text-align: right; margin-top: 20px; }
            .address { margin-top: 20px; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="invoice-title">INVOICE</div>
          </div>
          
          <div class="details">
            <div><strong>Invoice Number:</strong> ${name || "Not Available"}</div>
            <div><strong>Date:</strong> ${invoice_date || "Not Available"}</div>
          </div>

           <div class="address">
            <h3>Shipping Address:</h3>
            <p>${billing_address?.name || "Not Available"}</p>
            <p>${billing_address?.street || ""} ${billing_address?.street2 || ""}</p>
            <p>${billing_address?.city || ""}, ${billing_address?.zip || ""}</p>
            <p>${billing_address?.country || ""}</p>
          </div>

          
          <table>
            <thead>
              <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th>Price</th>
              </tr>
            </thead>
            <tbody>
              ${lines.map(line => `
                <tr>
                  <td>${line.product_name || "Not Available"}</td>
                  <td>1</td>
                  <td>₹${line.total || "0.00"}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          
          <div class="total">
            Total: ₹${amount_total || "0.00"} ${currency || ""}
          </div>
          
        </body>
      </html>
    `;
  };

  const downloadInvoice = async () => {
    try {
      setIsDownloading(true);
      
      // Generate the PDF
      const { uri } = await Print.printToFileAsync({
        html: generateInvoiceHtml(),
      });
      
      // Check if sharing is available
      const isSharingAvailable = await Sharing.isAvailableAsync();
      
      if (!isSharingAvailable) {
        Alert.alert('Sharing not available on this platform');
        return;
      }
      
      // Share the PDF
      await Sharing.shareAsync(uri, {
        mimeType: 'application/pdf',
        dialogTitle: 'Share Invoice',
        UTI: 'com.adobe.pdf'
      });
      
    } catch (error) {
      console.error('Error:', error);
      Alert.alert('Error', 'Failed to generate invoice');
    } finally {
      setIsDownloading(false);
      setIsVisible(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#000000" />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-red-500">{error}</Text>
      </View>
    );
  }

  // Destructure data from the orderData object safely
  const { amount_total, currency, invoice_date, name, lines, billing_address, product_summary } = orderData || {};

  // Handle cases where properties might be undefined or null
  const invoiceDate = invoice_date ? invoice_date : "Not Available";
  const orderName = name ? name : "Not Available";
  const totalAmount = amount_total ? `${amount_total} ${currency}` : "Not Available";
  
  const lineItems = lines.map((line, index) => ({
    productName: line.product_name || "Not Available",
    total: line.total || "Not Available",
  }));

  const data = {
    tracking: {
      estimatedDelivery: "7-8 days",
      status: [
        { name: "Order Confirmed", completed: "completed" },
        { name: "Order Shipped", completed: "completed" },
        { name: "In transit", completed: "working" },
        { name: "Order Delivered", completed: "incomplete" },
      ],
    },
    shipment: {
      date: "08-08-2024",
      item: "Gold Coin - 5 grams",
      price: "₹400.00"
    },
  };

  return (
    <View className="flex-1 bg-white">
      <ScrollView>
        {/* Header */}
        <View className="flex-row items-center p-4">
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back-sharp" size={24} color="black" />
          </TouchableOpacity>
          <Text className="text-lg font-semibold ml-4">Order</Text>
        </View>

        {/* Order Details */}
        <View className="border-[#D9D9D9] border w-[350px] h-[120px] p-4 mt-2 rounded-lg mx-4">
          <View className="flex-row justify-between ">
            <Text className="text-[#20224480] text-[13px]">Order date</Text>
            <Text className="text-[#202244] text-[13px]">{invoiceDate}</Text>
          </View>
          <View className="flex-row justify-between ">
            <Text className="text-[#20224480] text-[13px]">Order #</Text>
            <Text className="text-[#202244] text-[13px]">{orderName}</Text>
          </View>
          <View className="flex-row justify-between ">
            <Text className="text-[#20224480] text-[13px]">Order Total</Text>
            <Text className="text-[#202244] text-[13px]">{totalAmount}({product_summary.total_count} Items)</Text>
          </View>
          <TouchableOpacity 
            className="top-2 right-4 border w-[349px] h-[41px] justify-between flex-row border-[#013443] rounded items-center" 
            onPress={toggleModal}
          >
            <Text className="text-[#013443] ml-2">Download Invoice</Text>
            <View className="right-3">
              <MaterialCommunityIcons name="greater-than" size={20} color="#013443" />
            </View>
          </TouchableOpacity>
        </View>

        {/* Download Modal */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={isVisible}
          onRequestClose={toggleModal}
        >
          <View className="flex-1 justify-end bg-black/50">
            <TouchableOpacity
              onPress={toggleModal}
              className="absolute top-28 mb-2 left-[50%] bg-[#202244CC] rounded-full p-2"
            >
              <Ionicons name="close" size={24} color="white" />
            </TouchableOpacity>
            <View className="bg-white rounded-t-2xl p-4 shadow-lg h-[75%]">
              <ScrollView showsVerticalScrollIndicator={false}>
                {/* Radio Button Section */}
                <View className="border border-[#D9D9D9] top-3 ml-3 rounded-md w-[349px] h-[36px] left-4">
                  <TouchableOpacity
                    onPress={() => selectOption("invoice")}
                    className="flex-row items-center p-2 mb-4"
                  >
                    <View
                      className={`w-5 h-5 border-2 rounded-full items-center justify-center ${
                        selectedOption === "invoice" ? "border-[#013443]" : "border-[#013443]"
                      }`}
                    >
                      {selectedOption === "invoice" && (
                        <View className="w-3 h-3 bg-[#013443] rounded-full" />
                      )}
                    </View>
                    <Text className="text-black ml-2 font-medium ml-3">Invoice</Text>
                  </TouchableOpacity>
                </View>
                <Text className="text-gray-500 mb-8 text-sm mt-3">
                  The warranty document contains IMEI number for wireless products.
                </Text>
                <TouchableOpacity 
                  className="left-10 bg-[#013443] w-[325px] h-[45px] rounded-md py-3 px-4 flex-row items-center" 
                  onPress={downloadInvoice}
                  disabled={isDownloading}
                >
                  <Text className="text-white ml-4 text-base mr-2 font-semibold">
                    {isDownloading ? 'Generating...' : 'Download'}
                  </Text>
                  <View className="left-52">
                    <Ionicons name="arrow-forward" size={20} color="white" />
                  </View>
                </TouchableOpacity>
              </ScrollView>
            </View>
          </View>
        </Modal>

        {/* Rest of your existing UI components */}
        {/* Tracking Section */}
        <View className="mt-6 mx-4">
          <Text className="text-[18px] text-[#202244] font-semibold mt-2">Track your order</Text>
          <Text className="text-[14px] text-[#202244] font-semibold mt-2">Estimated delivery</Text>
          <Text className="font-medium text-[13px] mt-2">{data.tracking.estimatedDelivery} (working days)</Text>

          {/* Tracking Status */}
          <View className="relative ml-4 top-3">
            {data.tracking.status.map((step, index) => (
              <View key={index} className="flex-row items-start">
                {/* Vertical Line */}
                {index !== data.tracking.status.length - 1 && (
                  <View
                    className={`absolute top-4 left-2 h-5 w-0.5 ${
                      step.completed === "completed"
                        ? "bg-green-500"
                        : step.completed === "working"
                        ? "bg-yellow-400"
                        : "bg-gray-300"
                    }`}
                  />
                )}
              </View>
            ))}
          </View>
        </View>

        {/* Shipment Details */}
        <Text className="text-[18px] text-[#202244] font-semibold mt-2 ml-2">Shipment Details</Text>
        <View className="mt-6 mb-4 border border-[#D9D9D9] rounded-lg p-1 ml-4 mr-4"> 
          {lineItems.map((line, index) => (
            <View key={index} className="border-[#D9D9D9] border w-[335px] h-[60px] p-1 mt-2 mb-2 rounded-lg mx-2">
              <View className="flex-row justify-between">
                <Text className="text-[#202244] text-[11px] font-bold">{line.productName}</Text>
              </View>
              <View className="flex-row justify-between">
                <TouchableOpacity className="mt-2 ml-4 mb-2 flex-row w-[144px] h-[32px] border shadow-md border-[#04518026] rounded p-2">
                  <AntDesign name="download" size={16} color="#202244" />
                  <Text className="text-[#202244] ml-2">Share this Item</Text>
                </TouchableOpacity>
                <Text className="text-[#202244] text-[13px]">₹{line.total}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Billing Address */}
        <View className="mt-6 mx-4 p-4 rounded-lg">
          <Text className="text-lg font-bold">Shipping Address</Text>
          <View className="border-[#D9D9D9] border rounded-md w-[349px] h-[94px] mt-3">
            <Text className="text-[#202244] text-[13px] mt-2 ml-3">{billing_address?.name || "Not Available"}</Text>
            <Text className="text-[#20224480] text-[13px] ml-3">
              {billing_address?.street}, {billing_address?.street2}, {billing_address?.city}, {billing_address?.zip}, {billing_address?.country}
            </Text>
          </View>
        </View>

        {/* Payment Information */}
        <View className="mt-6 mx-4">
          <Text className="text-lg font-bold ml-4">Payment information</Text>
          <View className="border-[#D9D9D9] border rounded-md w-[349px] h-[54px] mt-3 ml-3">
            <Text className="text-[#202244] text-[13px] mt-2 ml-3">Payment State</Text>
            <Text className="text-[#20224480] text-[13px] ml-3">{orderData?.payment_state || "Not Available"}</Text>
          </View>
        </View>

        {/* Order Summary */}
        <View className="mb-4 mt-4">
          <Text className="text-lg font-bold mb-4 ml-8">Order Summary</Text>
          <View className="border border-[#D9D9D9] ml-8 mr-4 w-[349px] h-[100px] rounded-lg p shadow-sm">
            <View className="flex-row justify-between mt-2">
              <Text className="text-[#202244] text-[11.5px] ml-3">Items: </Text>
              <Text className="text-[#202244] text-[11.5px] mr-3">{product_summary.total_count}</Text>
            </View>
            <View className="flex-row justify-between">
              <Text className="text-[#202244] text-[11.5px] ml-3">Total before tax: </Text>
              <Text className="text-[#202244] text-[11.5px] mr-3">{product_summary.total_subtotal}</Text>
            </View>
            <View className="flex-row justify-between">
              <Text className="text-[#202244] text-[11.5px] ml-3">Total: </Text>
              <Text className="text-[#202244] text-[11.5px] mr-3">{product_summary.total_amount}</Text>
            </View>
            <View className="flex-row justify-between pt-2">
              <Text className="text-black font-bold ml-3">Order Total:</Text>
              <Text className="text-[#F4672A] font-bold mr-3">{product_summary.total_amount}</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}