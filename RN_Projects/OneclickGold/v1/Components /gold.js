// import React, { useState, useEffect, useRef,useContext } from "react";
// import { View, Text, TouchableOpacity, ScrollView, Image, FlatList, TextInput } from "react-native";
// import { Ionicons } from "@expo/vector-icons";
// import { SafeAreaView } from "react-native-safe-area-context";
// import FontAwesome from '@expo/vector-icons/FontAwesome';
// import { SessionContext } from './login';


// export default function Gold({ route, navigation }) {
//   const { sessionId, loadSessionData } = useContext(SessionContext);
//   // const routeUserName = route.params?.userName;


//   // Existing state variables
//   const [selectedAmount, setSelectedAmount] = useState(null);
//   const [liveGoldPrice, setLiveGoldPrice] = useState('');
//   const [vaultOptions, setVaultOptions] = useState([]);
//   const [userVaultAccount, setUserVaultAccount] = useState([]);
//   const [featuredProducts, setFeaturedProducts] = useState([]);
//   const [productsdata, setProductsdata] = useState([]);
//   const [goldCategory, setGoldCategory] = useState([]);

//   const [hassleFreeSolutions, setHassleFreeSolutions] = useState([]);
//   const [userName, setUserName] = useState('Test');
//   const [totalWalletAmount, setTotalWalletAmount] = useState('');

//   // New state variables for calculations
//   const [customAmount, setCustomAmount] = useState('');
//   const [customGram, setCustomGram] = useState('');
//   const [selectedMetal, setSelectedMetal] = useState('gold'); // 'gold' or 'silver'
//   const [metalId, setMetalId] = useState(null);
//   const [isInputDebouncing, setIsInputDebouncing] = useState(false);

//   // Use refs to store timeout IDs for debounce
//   const debounceTimeout = useRef(null);
//   const user = route.params.userName || 'Test';
//   const Location = () => {
//     navigation.navigate('LocationsScreen');
//   }
//   const sellMetal = (item) => {
//     navigation.navigate('SellMetalForm', {
//       item: item,
//       metalId: item.metal_id, // Explicitly pass the metal ID
//       metal: item.metal,
//       quantity: item.quantity,
//       sellingPrice: item.sellingPrice
//     });
//   }

//   const productdesc = (product) => {
//     navigation.navigate('ProductPage', {
//       prodId: product.id,
//       productData: product // Pass the entire product object if needed
//     });
//   }


//   const requestDelivery = (item) => {
//     // console.log('Requesting delivery for:', item);
//     navigation.navigate('RequestDelivery', {
//       item: item,
//       metalId: item.metal_id,
//       metalType: item.metal,
//       quantity: item.quantity
//     });
//   }

//   // Debounce function (like the one in the JS snippet)
//   const debounce = (func, delay) => {
//     return (...args) => {
//       if (debounceTimeout.current) {
//         clearTimeout(debounceTimeout.current);
//       }

//       setIsInputDebouncing(true);
//       debounceTimeout.current = setTimeout(() => {
//         func(...args);
//         setIsInputDebouncing(false);
//       }, delay);
//     };
//   };


//   const ProductList = () => {
//     fetch('https://gold.arihantai.com/api/v3/shop', {
//       method: 'GET',
//       headers: {
//         'Cookie': 'frontend_lang=en_US; session_id=e450fe6ac3d54828e613b08213cee050ff67d14c',
//       },
//     })
//       .then(response => response.json())
//       .then(data => {
//         console.log('Product list:', data);
//       })
//       .catch(error => {
//         console.error('Error fetching product list:', error);
//       });
//   }

//   // Function to update grams based on amount
//   const updateGramsFromAmount = (amount, metalId) => {
//     // console.log('Converting amount to grams:', amount, 'Metal ID:', metalId);

//     // Call the API to get grams value
//     fetch(`https://gold.arihantai.com/sip/get_grams/${metalId}?get_price=${amount / 1.03}`, {
//       method: 'GET',
//       headers: {
//         'Cookie': 'frontend_lang=en_US; session_id=e450fe6ac3d54828e613b08213cee050ff67d14c',
//       },
//     })
//       .then(response => {
//         if (!response.ok) {
//           throw new Error('Network response was not ok');
//         }
//         return response.json();
//       })
//       .then(result => {
//         // console.log('Grams received:', result.grams);
//         if (result.grams) {
//           setCustomGram(result.grams.toFixed(6));
//         }
//       })
//       .catch(error => {
//         console.error('Error fetching gram conversion:', error);
//       });
//   };

//   // Function to update amount based on grams
//   const updateAmountFromGrams = (grams, metalId) => {
//     // console.log('Converting grams to amount:', grams, 'Metal ID:', metalId);

//     // Call the API to get amount value
//     fetch(`https://gold.arihantai.com/sip/get_amount/${metalId}?grams=${grams}`, {
//       method: 'GET',
//       headers: {
//         'Cookie': 'frontend_lang=en_US; session_id=e450fe6ac3d54828e613b08213cee050ff67d14c',
//       },
//     })
//       .then(response => {
//         if (!response.ok) {
//           throw new Error('Network response was not ok');
//         }
//         return response.json();
//       })
//       .then(result => {
//         // console.log('Amount received:', result.amount);
//         if (result.amount) {
//           const amountWithGST = result.amount * 1.03; // Add 3% GST
//           setCustomAmount(amountWithGST.toFixed(2));
//         }
//       })
//       .catch(error => {
//         console.error('Error fetching amount conversion:', error);
//       });
//   };

//   // Create debounced versions of update functions
//   const debouncedUpdateGrams = useRef(debounce(updateGramsFromAmount, 500)).current;
//   const debouncedUpdateAmount = useRef(debounce(updateAmountFromGrams, 500)).current;

//   // Handle custom amount input change
//   const handleAmountChange = (text) => {
//     setCustomAmount(text);

//     // Only proceed if text is a valid number
//     const numValue = parseFloat(text);
//     if (!isNaN(numValue) && numValue > 0 && metalId) {
//       debouncedUpdateGrams(numValue, metalId);
//     }
//   };

//   // Handle custom gram input change
//   const handleGramChange = (text) => {
//     setCustomGram(text);

//     // Only proceed if text is a valid number
//     const numValue = parseFloat(text);
//     if (!isNaN(numValue) && numValue > 0 && metalId) {
//       debouncedUpdateAmount(numValue, metalId);
//     }
//   };

//   // Handle preset amount selection
//   const handleAmountSelect = (amount) => {
//     setSelectedAmount(amount);
//     setCustomAmount(amount.toString());

//     if (metalId) {
//       debouncedUpdateGrams(amount, metalId);
//     }
//   };

//   // Toggle between gold and silver
//   const toggleMetal = (metal) => {
//     setSelectedMetal(metal);
//     const newMetalId = metal === 'gold' ? 1 : 2; // Example IDs
//     setMetalId(newMetalId);

//     // Reset inputs
//     setCustomAmount('');
//     setCustomGram('');
//     setSelectedAmount(null);
//   };

//   // Add to vault function
//   const addToVault = () => {
//     // Implement add to vault logic here
//     console.log(`Adding ${customGram}g of ${selectedMetal} to vault`);

//   };

//   useEffect(() => {
//     fetchHomepageData();
//   }, []);

//   const fetchHomepageData = async(sessionId) => {
//     fetch('https://gold.arihantai.com/api/v3/homepage', {
//       method: 'GET',
//       headers: {
//         'Cookie': `session_id=${sessionId}`,
//       },
//     })
//       .then((response) => {
//         if (!response.ok) {
//           throw new Error('Network response was not ok');
//         }
//         return response.text();
//       })
//       .then((text) => {
//         // console.log("Raw response text:", text);
//         return JSON.parse(text);
//       })
//       .then((data) => {
//         const responseData = data.data;
//         // console.log("data", responseData);

//         // Set the state with the API response data
//         setLiveGoldPrice(`₹ ${responseData.metals[0].current_price}/gm`);
//         setVaultOptions([10, 25, 50, 100]);
//         setUserVaultAccount(responseData.account.map((item) => ({
//           // id: item.id,
//           metal_id: item.metal_id,
//           metal: item.metal_type,
//           quantity: `${item.quantity} g`,
//           sellingPrice: item.selling_price,
//           walletAmount: item.wallet_amount
//         })));

//         // Set total wallet amount after setting user vault data
//         const totalWallet = responseData.account.reduce((acc, item) => acc + parseFloat(item.wallet_amount), 0);
//         setTotalWalletAmount(totalWallet.toFixed(2));

//         // setTotalWalletAmount(totalWalletAmount.toFixed(2)); 
//         setFeaturedProducts(responseData.products.main_categories.data);
//         setProductsdata(responseData.product_data);
//         setGoldCategory(responseData.products.goldJewellery.data);

//         setHassleFreeSolutions(responseData.warehouses.map((warehouse) => ({
//           id: warehouse.id,
//           name: warehouse.name,
//           description: "Description not available",
//           image: `https://gold.arihantai.com${warehouse.image}`
//         })));

//         const goldJewelery = responseData.products.main_categories.data.find(category => category.name.toLowerCase().includes('Gold Jewellery'));
//         const goldMetal = responseData.metals.find(metal => metal.name.toLowerCase().includes('gold'));
//         const silverMetal = responseData.metals.find(metal => metal.name.toLowerCase().includes('silver'));

//         if (goldMetal && selectedMetal === 'gold') {
//           setMetalId(goldMetal.id);
//         } else if (silverMetal && selectedMetal === 'silver') {
//           setMetalId(silverMetal.id);
//         }

//         setUserName(responseData.user?.name || 'Kritika');
//       })
//       .catch((error) => {
//         console.error('Error fetching data:', error);
//       });
//   };

//   return (
//     <ScrollView className="bg-white">

//       <View className="p-4 bg-[#fae6b2] h-[120px] ">

//         <View className="flex-row ">
//           <TouchableOpacity className="mr-4"
//             onPress={() => navigation.navigate('Shop')}>
//             <Ionicons name="menu-outline" size={24} color="#045180" />
//             <Text className="text-blue-500">Shop</Text>
//           </TouchableOpacity>

//           <View className="flex-row left-56">
//             <TouchableOpacity className="mr-4"
//               onPress={() => navigation.navigate('AccountScreen')}>
//               <FontAwesome name="user-circle-o" size={24} color="#045180" />
//             </TouchableOpacity>
//             <TouchableOpacity onPress={() => navigation.navigate('MyCart')}>
//               <Ionicons name="cart-outline" size={24} color="#045180" />
//             </TouchableOpacity>
//           </View>
//         </View>
//       </View>

//       {/* Live Gold Price */}
//       <View className="p-4 mt-2 bg-white">
//         <Text className="text-center text-gray-800 font-semibold text-lg mb-2">
//           Live Gold Price <Text className="text-green-600">{liveGoldPrice}</Text>
//         </Text>
//         <Text className="text-center mt-1 text-gray-700 text-lg font-semibold">
//           Hey {user}! Let's explore         </Text>
//         <Text className="font-bold text-center"> OneClick
//           <Text className="text-yellow-500 font-bold text-center"> GOLD</Text></Text>
//       </View>

//       {/* Gold and Silver Switch */}
//       <View className="border border-[#D9D9D9] rounded-md ml-4 mr-4">
//         <View className="flex-row justify-center mt-4 ml-8 border rounded-full w-[180px] h-[36px] border-[#D9D9D9DD]">
//           <TouchableOpacity
//             className={`w-[83px] h-[27px] rounded-full top-1 ${selectedMetal === 'gold' ? 'bg-[#E3AA48]' : ''}`}
//             onPress={() => toggleMetal('gold')}
//           >
//             <Text className={`font-bold text-center top-1 ${selectedMetal === 'gold' ? 'text-white' : 'text-black'}`}>Gold</Text>
//           </TouchableOpacity>
//           <TouchableOpacity
//             className={`w-[83px] h-[27px] rounded-full top-1 ${selectedMetal === 'silver' ? 'bg-[#817e7b]' : ''}`}
//             onPress={() => toggleMetal('silver')}
//           >
//             <Text className={`font-bold text-center top-1 ${selectedMetal === 'silver' ? 'text-white' : 'text-black'}`}>Silver</Text>
//           </TouchableOpacity>
//         </View>

//         {/* Choose Amount */}
//         <View className="p-4">
//           <Text className="text-lg font-bold text-gray-800 mb-2">Choose Amount</Text>
//           <View className="flex-row items-center justify-between mb-4">
//             <TextInput className=" border-b border-b-black w-[125px] mr-2"
//               onChangeText={handleAmountChange}
//               keyboardType="numeric"
//             // value={customAmount}
//             > {customAmount} </TextInput>

//             <Text className="text-black">/</Text>
//             <TextInput
//               // placeholder="Custom Gram"
//               // value={customGram}
//               onChangeText={handleGramChange}
//               keyboardType="numeric"
//               // style={{color:'black',}}
//               className=" border-b border-b-black w-[125px] mr-2"
//             // className="border-b w-[125px] h-[19px] font-bold border-b-black rounded-b-md p-2 flex-1 ml-3 text-black-200"
//             >  {customGram}
//               {/* <Text className="text-black"> {customGram} </Text> */}
//             </TextInput >
//           </View>
//           <View className="flex-row justify-between">
//             <View className="flex-row justify-evenly mt-4">
//               {vaultOptions.map((amount) => (
//                 <TouchableOpacity
//                   key={amount}
//                   onPress={() => handleAmountSelect(amount)}
//                   className={`p-3 ml-2 mr-1 rounded-xl border-2 ${selectedAmount === amount ? 'border-yellow-500' : 'border-gray-300'
//                     }`}
//                 >
//                   <Text className="text-center text-black font-semibold">{amount} gm</Text>
//                 </TouchableOpacity>
//               ))}
//             </View>
//           </View>
//           <TouchableOpacity
//             className="mt-4 bg-[#013443] p-3 rounded-md flex-row items-center justify-between"
//             onPress={addToVault}
//           >
//             <Text className="text-white font-bold mr-2 ml-4">Add to vault</Text>
//             <Ionicons name="arrow-forward-sharp" size={24} color="white" />
//           </TouchableOpacity>
//         </View>
//       </View>

//       {/* User Vault Account */}
//       <View className="p-4 mt-6">
//         <Text className="text-center text-gray-800 font-semibold text-lg mt-2 underline">Your Vault Account</Text>
//         <View className="flex-row  border-b border-[#D9D9D9] py-2 px-2 space-x-2">
//           <Text className="text-gray-600 font-medium flex-1 ">Metal Type</Text>
//           <Text className="text-gray-600 font-medium mr-2">Quantity</Text>
//           <Text className="text-gray-600 font-medium flex-1 ">Selling Price</Text>
//           <Text className="text-gray-600 font-medium flex-1 ">Wallet Amount</Text>
//           <Text className="text-gray-600 font-medium flex-1 ">Sell</Text>
//           <Text className="text-gray-600 font-medium flex-1 ">Request Delivery</Text>
//         </View>
//         <FlatList
//           data={userVaultAccount}
//           keyExtractor={(item) => item.account}
//           renderItem={({ item }) => (
//             <View className="flex-row p-2 border-b border-gray-300">
//               {/* <Text className="text-gray-700 font-semibold mr-4">{item.id}</Text> */}
//               {/* <Text className="text-gray-700 font-semibold mr-4">{item.metal_id}</Text> */}
//               <Text className="text-gray-700 font-semibold flex-1">{item.metal}</Text>
//               {/* <Text className="text-gray-700 mr-8">{item.quantity}</Text> */}
//               <Text className="text-gray-700 flex-1">{item.quantity}</Text>
//               {/* <Text className="text-gray-700 mr-8">{item.sellingPrice}</Text> */}
//               <Text className="text-gray-700 flex-1">{item.sellingPrice}</Text>
//               {/* <Text className="text-gray-700 mr-8">{item.walletAmount}</Text> */}
//               <Text className="text-gray-700 flex-1">{item.walletAmount}</Text>
//               {/* <TouchableOpacity className="bg-[#013443] p-2 rounded-md mr-6"  */}
//               <TouchableOpacity className="bg-[#013443] p-2 rounded-md ml-2 mr-2"
//                 // onPress={sellMetal}
//                 onPress={() => sellMetal(item)} // Pass the item to sellMetal function

//               >
//                 <Text className="text-white text-center">Sell</Text>
//               </TouchableOpacity>
//               {/* <TouchableOpacity className="bg-[#013443] p-2 rounded-md mr-4"  */}
//               <TouchableOpacity className="bg-[#013443] p-2 rounded-md "
//                 // onPress={sellMetal}
//                 onPress={() => requestDelivery(item)} // Pass the item to sellMetal function

//               >
//                 <Text className="text-white text-right flex-1">Delivery</Text>
//               </TouchableOpacity>
//             </View>
//           )}
//         />
//         {/* <Text> Total </Text> */}
//         <Text className="text-gray-700 font-semibold  mt-2 ml-3  text-center">
//           Total Wallet Amount:
//           <Text className="font-bold  ml-5 mr-5"> ₹{totalWalletAmount} </Text>
//         </Text>
//       </View>


//       <View className="p-4">
//         <Text className="text-xl font-bold text-gray-800 mb-4">Explore your hassle-free solutions</Text>
//         <TouchableOpacity onPress={Location}>
//           <Text className="text-right mb-4 mt-2 pr-2 ml-4 underline">See All</Text>
//         </TouchableOpacity>
//         {hassleFreeSolutions.map((solution) => (
//           <View
//             key={solution.id}
//             className="p-4 border w-[346px] h-[293px] border-[#D9D9D9] rounded-md mb-4  items-center"
//           >
//             <Image source={{ uri: solution.image }} className="w-[147px] h-[147px] bottom-1 rounded-md mr-4" />
//             <View className="flex-col ml-2 mt- ">
//               <Text className="text-black font-bold mb-1">{solution.name}</Text>
//               <Text className="text-gray-500 text-[10px]  w-[316px] h-[45px]	">{solution.description}</Text>
//               <TouchableOpacity className="mt-1 mb-1 border-[#013443] flex-row justify-between  border p-2 rounded-lg" onPress={Location}>
//                 <Text className="text-[#013443]  text-xs font-semibold text-center mt-1 ml-2">View Location </Text>
//                 <Ionicons name="arrow-forward-sharp" size={24} color="#013443" />
//               </TouchableOpacity>
//             </View>
//           </View>
//         ))}
//       </View>

//       <View className="p-4">
//         <Text className="text-black font-bold text-xl mb-2 ml-2">
//           Indulge in <Text className="text-yellow-500">OneClick Gold's</Text> Collection
//         </Text>
//         <View className="flex-row flex-wrap justify-between">
//           {goldCategory.map((item) => (
//             <View
//               key={item.id}
//               // className="w-[48%] bg-[#FCF2E6BF] p-3 mb-3 rounded-md shadow-md"
//               className="w-[48%] bg-[#FCF2E6BF] p-3 mb-3 rounded-md"
//             >
//               <Image
//                 // source={{ uri: item.image_url }}
//                 source={{ uri: `https://gold.arihantai.com${item.image_url}` }}

//                 className="h-36 w-full rounded-md mb-2"
//               />
//               <Text className="text-black text-[16px] text-center">{item.name}</Text>
//               {/* <Text className="text-black font-semibold text-[13px] text-center">Prices starts at {item.price}</Text> */}
//             </View>
//           ))}

//         </View>
//         {/* <Ionicons name="arrow-forward-sharp" size={10} color="#013443" /> */}

//         <TouchableOpacity className=" border border-[#013443] rounded-full py-2 px-4 m-4" onPress={() => navigation.navigate('Shop')}>
//           {/* <Ionicons name="arrow" size={10} color="#045180" /> */}

//           <Text className="text-center text-black font-medium">View All Products</Text>
//         </TouchableOpacity>
//         {/* <Ionicons name="arrow-forward-sharp" size={10} color="#013443" /> */}

//       </View>


//       <View className="p-4 mt-5">
//         {/* <Text className="text-center text-gray-800 font-bold text-lg mb-4">Featured Products</Text> */}
//         <Text className="text-center text-gray-800 font-bold text-lg mb-4">Exclusive Bars and Coins Collection</Text>
//         <FlatList
//           data={featuredProducts}
//           keyExtractor={(item) => item.id.toString()}
//           horizontal
//           showsHorizontalScrollIndicator={false}
//           renderItem={({ item }) => (
//             <View className="mr-4">
//               <Image
//                 source={{ uri: `https://gold.arihantai.com${item.image_url}` }}
//                 style={{ width: 120, height: 120, borderRadius: 8 }}
//               />
//               <Text className="text-center text-gray-700 mt-2">{item.name}</Text>
//               <Text className="text-center text-gray-600">{item.price}</Text>
//             </View>
//           )}
//         />
//       </View>

//       <View className="p-4">
//         <Text className="text-black font-bold text-lg mb-3">Featured Products</Text>

//         {/* ScrollView wraps the map function */}
//         <ScrollView horizontal showsHorizontalScrollIndicator={false}>
//           <View className="flex-row space-x-4 ml-2 ">
//             {productsdata.map((item) => (
//               <View
//                 key={item.id}
//                 className="w-[240px] bg-[#EDFAFE] h-[121px] p-3 rounded-md items-center flex-row mr-2 ml-2"
//               >
//                 <Image
//                   source={{ uri: `https://gold.arihantai.com${item.img_url}` }}
//                   className="h-16 w-16 rounded-full mr-2"
//                 />
//                 <View className="flex-col ml-1">
//                   <Text className="text-[10px] mb-1 font-bold">{item.name}</Text>
//                   <Text className="mb-1">{item.price}</Text>
//                   {/* <TouchableOpacity className="bg-[#013443] w-[80px] h-[20px] rounded-md" onPress={() => navigation.navigate('ProductPage', { id: item.id })}> */}
//                   <TouchableOpacity className="bg-[#013443] w-[80px] h-[20px] rounded-md"
//                     //  onPress={() => Product(item)}
//                     onPress={() => productdesc(item)}
//                   // onPress={() => productdesc(item.id)}
//                   >
//                     <Text className="text-white text-center">View</Text>
//                   </TouchableOpacity>
//                 </View>
//               </View>
//             ))}
//           </View>
//         </ScrollView>
//       </View>

//       <View className="p-4 mt-6 bg-[#FCF2E6BF] rounded-lg">
//         <Text className="text-center text-gray-800 font-semibold text-lg">Your Golden Future , Secure Today</Text>
//         <Text className="text-center mt-2 text-gray-600">
//           Your golden future starts with easy and transparent investments.
//         </Text>
//         <Image className="w-96 h-80 mt-10" source={require('../assets/securebank.png')} />
//       </View>
//     </ScrollView>
//   );
// }


import React, { useState, useEffect, useRef, useContext } from "react";
import { View, Text, TouchableOpacity, ScrollView, Image, FlatList, TextInput } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { SessionContext } from './login';

export default function Gold({ route, navigation }) {
  const { sessionId, loadSessionData } = useContext(SessionContext);
  
  // Existing state variables
  const [selectedAmount, setSelectedAmount] = useState(null);
  const [liveGoldPrice, setLiveGoldPrice] = useState('');
  const [vaultOptions, setVaultOptions] = useState([10, 25, 50, 100]);
  const [userVaultAccount, setUserVaultAccount] = useState([]);
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [productsdata, setProductsdata] = useState([]);
  const [goldCategory, setGoldCategory] = useState([]);
  const [hassleFreeSolutions, setHassleFreeSolutions] = useState([]);
  const [userName, setUserName] = useState('');
  const [totalWalletAmount, setTotalWalletAmount] = useState('0.00');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // New state variables for calculations
  const [customAmount, setCustomAmount] = useState('');
  const [customGram, setCustomGram] = useState('');
  const [selectedMetal, setSelectedMetal] = useState('gold'); // 'gold' or 'silver'
  const [metalId, setMetalId] = useState(null);
  const [isInputDebouncing, setIsInputDebouncing] = useState(false);

  // Use refs to store timeout IDs for debounce
  const debounceTimeout = useRef(null);
  const user = route.params?.userName || 'User';

  const Location = () => {
    navigation.navigate('LocationsScreen');
  }
  
  const sellMetal = (item) => {
    navigation.navigate('SellMetalForm', {
      item: item,
      metalId: item.metal_id, // Explicitly pass the metal ID
      metal: item.metal,
      quantity: item.quantity,
      sellingPrice: item.sellingPrice
    });
  }

  const productdesc = (product) => {
    navigation.navigate('ProductPage', {
      prodId: product.id,
      productData: product // Pass the entire product object if needed
    });
  }

  const requestDelivery = (item) => {
    navigation.navigate('RequestDelivery', {
      item: item,
      metalId: item.metal_id,
      metalType: item.metal,
      quantity: item.quantity
    });
  }

  // Debounce function (like the one in the JS snippet)
  const debounce = (func, delay) => {
    return (...args) => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      setIsInputDebouncing(true);
      debounceTimeout.current = setTimeout(() => {
        func(...args);
        setIsInputDebouncing(false);
      }, delay);
    };
  };

  // Function to update grams based on amount
  const updateGramsFromAmount = (amount, metalId) => {
    if (!metalId || !amount) return;
    
    // Call the API to get grams value
    fetch(`https://gold.arihantai.com/sip/get_grams/${metalId}?get_price=${amount / 1.03}`, {
      method: 'GET',
      headers: {
        'Cookie': `session_id=${sessionId}`,
      },
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(result => {
        if (result.grams) {
          setCustomGram(result.grams.toFixed(6));
        }
      })
      .catch(error => {
        console.error('Error fetching gram conversion:', error);
      });
  };

  // Function to update amount based on grams
  const updateAmountFromGrams = (grams, metalId) => {
    if (!metalId || !grams) return;
    
    // Call the API to get amount value
    fetch(`https://gold.arihantai.com/sip/get_amount/${metalId}?grams=${grams}`, {
      method: 'GET',
      headers: {
        'Cookie': `session_id=${sessionId}`,
      },
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(result => {
        if (result.amount) {
          const amountWithGST = result.amount * 1.03; // Add 3% GST
          setCustomAmount(amountWithGST.toFixed(2));
        }
      })
      .catch(error => {
        console.error('Error fetching amount conversion:', error);
      });
  };

  // Create debounced versions of update functions
  const debouncedUpdateGrams = useRef(debounce(updateGramsFromAmount, 500)).current;
  const debouncedUpdateAmount = useRef(debounce(updateAmountFromGrams, 500)).current;

  // Handle custom amount input change
  const handleAmountChange = (text) => {
    setCustomAmount(text);

    // Only proceed if text is a valid number
    const numValue = parseFloat(text);
    if (!isNaN(numValue) && numValue > 0 && metalId) {
      debouncedUpdateGrams(numValue, metalId);
    }
  };

  // Handle custom gram input change
  const handleGramChange = (text) => {
    setCustomGram(text);

    // Only proceed if text is a valid number
    const numValue = parseFloat(text);
    if (!isNaN(numValue) && numValue > 0 && metalId) {
      debouncedUpdateAmount(numValue, metalId);
    }
  };

  // Handle preset amount selection
  const handleAmountSelect = (amount) => {
    setSelectedAmount(amount);
    setCustomAmount(amount.toString());

    if (metalId) {
      debouncedUpdateGrams(amount, metalId);
    }
  };

  // Toggle between gold and silver
  const toggleMetal = (metal) => {
    setSelectedMetal(metal);
    const newMetalId = metal === 'gold' ? 1 : 2; // Example IDs
    setMetalId(newMetalId);

    // Reset inputs
    setCustomAmount('');
    setCustomGram('');
    setSelectedAmount(null);
  };

  // Add to vault function
  const addToVault = () => {
    // Implement add to vault logic here
    console.log(`Adding ${customGram}g of ${selectedMetal} to vault`);
    // TODO: Implement API call to add to vault
  };

  // Load session data and fetch homepage data when component mounts
  useEffect(() => {
    const initialize = async () => {
      try {
        setIsLoading(true);
        
        // First, ensure we have a session ID
        let currentSessionId = sessionId;
        
        if (!currentSessionId) {
          const sessionData = await loadSessionData();
          currentSessionId = sessionData.sessionId;
          
          if (!currentSessionId) {
            setError('No session found. Please login again.');
            setIsLoading(false);
            return;
          }
        }
        
        // Now fetch homepage data with the session ID
        await fetchHomepageData(currentSessionId);
      } catch (err) {
        console.error('Error initializing Gold screen:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    initialize();
  }, []);

  const fetchHomepageData = async (currentSessionId) => {
    try {
      if (!currentSessionId) {
        throw new Error('No session ID available');
      }
      
      const response = await fetch('https://gold.arihantai.com/api/v3/homepage', {
        method: 'GET',
        headers: {
          'Cookie': `session_id=${currentSessionId}`,
        },
      });
      
      if (!response.ok) {
        throw new Error(`Network response was not ok: ${response.status}`);
      }
      
      // Get the response as text first for debugging
      const textResponse = await response.text();
      
      // Try to parse the JSON
      let data;
      try {
        data = JSON.parse(textResponse);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        console.log('Raw response:', textResponse);
        throw new Error('Failed to parse API response');
      }
      
      if (!data || !data.data) {
        console.error('Invalid data structure:', data);
        throw new Error('Invalid API response format');
      }
      
      const responseData = data.data;
      
      // Set the state with the API response data
      if (responseData.metals && responseData.metals.length > 0) {
        setLiveGoldPrice(`₹ ${responseData.metals[0].current_price}/gm`);
      }
      
      // Set vault options as a fallback if not available
      if (!vaultOptions.length) {
        setVaultOptions([10, 25, 50, 100]);
      }
      
      // Handle the account data safely
      if (responseData.account && Array.isArray(responseData.account)) {
        // Transform account data
        const accountData = responseData.account.map((item) => ({
          metal_id: item.metal_id,
          metal: item.metal_type,
          quantity: `${item.quantity} g`,
          sellingPrice: item.selling_price,
          walletAmount: item.wallet_amount
        }));
        
        setUserVaultAccount(accountData);
        
        // Set total wallet amount
        const totalWallet = responseData.account.reduce(
          (acc, item) => acc + parseFloat(item.wallet_amount || 0), 
          0
        );
        setTotalWalletAmount(totalWallet.toFixed(2));
      } else {
        // If account is not an array or is undefined, set empty array
        console.log('No account data found or invalid format');
        setUserVaultAccount([]);
        setTotalWalletAmount('0.00');
      }
      
      // Handle products safely
      if (responseData.products) {
        if (responseData.products.main_categories && responseData.products.main_categories.data) {
          setFeaturedProducts(responseData.products.main_categories.data);
        }
        
        if (responseData.products.goldJewellery && responseData.products.goldJewellery.data) {
          setGoldCategory(responseData.products.goldJewellery.data);
        }
      }
      
      // Handle product data
      if (responseData.product_data) {
        setProductsdata(responseData.product_data);
      }
      
      // Handle warehouses data for hassle-free solutions
      if (responseData.warehouses && Array.isArray(responseData.warehouses)) {
        const warehousesData = responseData.warehouses.map((warehouse) => ({
          id: warehouse.id,
          name: warehouse.name,
          description: warehouse.description || "Description not available",
          image: `https://gold.arihantai.com${warehouse.image}`
        }));
        
        setHassleFreeSolutions(warehousesData);
      }
      
      // Set metal IDs
      if (responseData.metals && Array.isArray(responseData.metals)) {
        const goldMetal = responseData.metals.find(metal => 
          metal.name && metal.name.toLowerCase().includes('gold')
        );
        
        const silverMetal = responseData.metals.find(metal => 
          metal.name && metal.name.toLowerCase().includes('silver')
        );
        
        if (goldMetal && selectedMetal === 'gold') {
          setMetalId(goldMetal.id);
        } else if (silverMetal && selectedMetal === 'silver') {
          setMetalId(silverMetal.id);
        }
      }
      
      // Set user name
      if (responseData.user && responseData.user.name) {
        setUserName(responseData.user.name);
      } else {
        setUserName(user);
      }
      
    } catch (error) {
      console.error('Error fetching homepage data:', error);
      throw error; // Re-throw to be caught by the calling function
    }
  };

  // Display loading state
  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <Text>Loading your golden experience...</Text>
      </View>
    );
  }

  // Display error state
  if (error) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <Text className="text-red-500">{error}</Text>
        <TouchableOpacity 
          className="mt-4 bg-[#013443] p-3 rounded-md"
          onPress={() => navigation.navigate('Login')}
        >
          <Text className="text-white">Back to Login</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Render the main UI
  return (
    // Use a SafeAreaView instead of ScrollView as the root component
    <SafeAreaView className="flex-1 bg-white">
      {/* Content ScrollView */}
      <ScrollView>
        {/* Header Section */}
        <View className="p-4 bg-[#fae6b2] h-[120px]">
          <View className="flex-row">
            <TouchableOpacity 
              className="mr-4"
              onPress={() => navigation.navigate('Shop')}
            >
              <Ionicons name="menu-outline" size={24} color="#045180" />
              <Text className="text-blue-500">Shop</Text>
            </TouchableOpacity>

            <View className="flex-row left-56">
              <TouchableOpacity 
                className="mr-4"
                onPress={() => navigation.navigate('AccountScreen')}
              >
                <FontAwesome name="user-circle-o" size={24} color="#045180" />
              </TouchableOpacity>
              <TouchableOpacity onPress={() => navigation.navigate('MyCart')}>
                <Ionicons name="cart-outline" size={24} color="#045180" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Live Gold Price Section */}
        <View className="p-4 mt-2 bg-white">
          <Text className="text-center text-gray-800 font-semibold text-lg mb-2">
            Live Gold Price <Text className="text-green-600">{liveGoldPrice}</Text>
          </Text>
          <Text className="text-center mt-1 text-gray-700 text-lg font-semibold">
            Hey {userName || user}! Let's explore
          </Text>
          <Text className="font-bold text-center">
            OneClick <Text className="text-yellow-500 font-bold text-center">GOLD</Text>
          </Text>
        </View>

        {/* Gold and Silver Switch Section */}
        <View className="border border-[#D9D9D9] rounded-md mx-4">
          <View className="flex-row justify-center mt-4 ml-8 border rounded-full w-[180px] h-[36px] border-[#D9D9D9DD]">
            <TouchableOpacity
              className={`w-[83px] h-[27px] rounded-full top-1 ${selectedMetal === 'gold' ? 'bg-[#E3AA48]' : ''}`}
              onPress={() => toggleMetal('gold')}
            >
              <Text className={`font-bold text-center top-1 ${selectedMetal === 'gold' ? 'text-white' : 'text-black'}`}>Gold</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`w-[83px] h-[27px] rounded-full top-1 ${selectedMetal === 'silver' ? 'bg-[#817e7b]' : ''}`}
              onPress={() => toggleMetal('silver')}
            >
              <Text className={`font-bold text-center top-1 ${selectedMetal === 'silver' ? 'text-white' : 'text-black'}`}>Silver</Text>
            </TouchableOpacity>
          </View>

          {/* Choose Amount Section */}
          <View className="p-4">
            <Text className="text-lg font-bold text-gray-800 mb-2">Choose Amount</Text>
            <View className="flex-row items-center justify-between mb-4">
              <TextInput 
                className="border-b border-b-black w-[125px] mr-2"
                value={customAmount}
                onChangeText={handleAmountChange}
                keyboardType="numeric"
                placeholder="Enter amount"
              />
              <Text className="text-black">/</Text>
              <TextInput
                className="border-b border-b-black w-[125px] mr-2"
                value={customGram}
                onChangeText={handleGramChange}
                keyboardType="numeric"
                placeholder="Enter grams"
              />
            </View>
            
            {/* Preset Amounts */}
            <View className="flex-row justify-between">
              <ScrollView horizontal showsHorizontalScrollIndicator={false} className="flex-row">
                {vaultOptions.map((amount) => (
                  <TouchableOpacity
                    key={amount}
                    onPress={() => handleAmountSelect(amount)}
                    className={`p-3 mr-2 rounded-xl border-2 ${selectedAmount === amount ? 'border-yellow-500' : 'border-gray-300'}`}
                  >
                    <Text className="text-center text-black font-semibold">{amount} gm</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
            
            {/* Add to Vault Button */}
            <TouchableOpacity
              className="mt-4 bg-[#013443] p-3 rounded-md flex-row items-center justify-between"
              onPress={addToVault}
            >
              <Text className="text-white font-bold mr-2 ml-4">Add to vault</Text>
              <Ionicons name="arrow-forward-sharp" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        {/* User Vault Account Section */}
        <View className="p-4 mt-6">
          <Text className="text-center text-gray-800 font-semibold text-lg mt-2 underline">Your Vault Account</Text>
          
          {userVaultAccount.length > 0 ? (
            <>
              {/* Table Header */}
              <View className="flex-row border-b border-[#D9D9D9] py-2 px-2 space-x-2">
                <Text className="text-gray-600 font-medium flex-1">Metal Type</Text>
                <Text className="text-gray-600 font-medium mr-2">Quantity</Text>
                <Text className="text-gray-600 font-medium flex-1">Selling Price</Text>
                <Text className="text-gray-600 font-medium flex-1">Wallet Amount</Text>
                <Text className="text-gray-600 font-medium flex-1">Actions</Text>
              </View>
              
              {/* Table Content - Render directly instead of using FlatList */}
              {userVaultAccount.map((item, index) => (
                <View key={index} className="flex-row p-2 border-b border-gray-300 items-center">
                  <Text className="text-gray-700 font-semibold flex-1">{item.metal}</Text>
                  <Text className="text-gray-700 mr-2">{item.quantity}</Text>
                  <Text className="text-gray-700 flex-1">{item.sellingPrice}</Text>
                  <Text className="text-gray-700 flex-1">{item.walletAmount}</Text>
                  <View className="flex-row flex-1">
                    <TouchableOpacity 
                      className="bg-[#013443] p-2 rounded-md mr-1"
                      onPress={() => sellMetal(item)}
                    >
                      <Text className="text-white text-center text-xs">Sell</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      className="bg-[#013443] p-2 rounded-md"
                      onPress={() => requestDelivery(item)}
                    >
                      <Text className="text-white text-center text-xs">Delivery</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
              
              {/* Total Wallet Amount */}
              <Text className="text-gray-700 font-semibold mt-2 text-center">
                Total Wallet Amount:
                <Text className="font-bold ml-2"> ₹{totalWalletAmount} </Text>
              </Text>
            </>
          ) : (
            <Text className="text-center text-gray-500 my-4">
              Your vault is empty. Start adding gold or silver to build your wealth!
            </Text>
          )}
        </View>

        {/* Hassle-Free Solutions Section */}
        <View className="p-4">
          <Text className="text-xl font-bold text-gray-800 mb-4">Explore your hassle-free solutions</Text>
          <TouchableOpacity onPress={Location}>
            <Text className="text-right mb-4 mt-2 pr-2 ml-4 underline">See All</Text>
          </TouchableOpacity>
          
          {/* Render solutions directly rather than using map inside ScrollView */}
          {hassleFreeSolutions.slice(0, 3).map((solution) => (
            <View
              key={solution.id}
              className="p-4 border border-[#D9D9D9] rounded-md mb-4 items-center"
            >
              <Image 
                source={{ uri: solution.image }} 
                className="w-[147px] h-[147px] rounded-md mb-4" 
              />
              <View className="w-full">
                <Text className="text-black font-bold mb-1 text-center">{solution.name}</Text>
                <Text className="text-gray-500 text-xs mb-2 text-center">{solution.description}</Text>
                <TouchableOpacity 
                  className="mt-1 mb-1 border-[#013443] flex-row justify-between border p-2 rounded-lg" 
                  onPress={Location}
                >
                  <Text className="text-[#013443] text-xs font-semibold text-center mt-1 ml-2">View Location </Text>
                  <Ionicons name="arrow-forward-sharp" size={24} color="#013443" />
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        {/* Gold Collection Section */}
        <View className="p-4">
          <Text className="text-black font-bold text-xl mb-2 ml-2">
            Indulge in <Text className="text-yellow-500">OneClick Gold's</Text> Collection
          </Text>
          
          <View className="flex-row flex-wrap justify-between">
            {goldCategory.slice(0, 4).map((item) => (
              <View
                key={item.id}
                className="w-[48%] bg-[#FCF2E6BF] p-3 mb-3 rounded-md"
              >
                <Image
                  source={{ uri: `https://gold.arihantai.com${item.image_url}` }}
                  className="h-36 w-full rounded-md mb-2"
                />
                <Text className="text-black text-[16px] text-center">{item.name}</Text>
              </View>
            ))}
          </View>

          <TouchableOpacity 
            className="border border-[#013443] rounded-full py-2 px-4 m-4" 
            onPress={() => navigation.navigate('Shop')}
          >
            <Text className="text-center text-black font-medium">View All Products</Text>
          </TouchableOpacity>
        </View>

        {/* Featured Products Section */}
        <View className="p-4 mt-5">
          <Text className="text-center text-gray-800 font-bold text-lg mb-4">Exclusive Bars and Coins Collection</Text>
          
          {/* Replace FlatList with ScrollView for this horizontal list */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {featuredProducts.map((item) => (
              <View key={item.id} className="mr-4 items-center">
                <Image
                  source={{ uri: `https://gold.arihantai.com${item.image_url}` }}
                  style={{ width: 120, height: 120, borderRadius: 8 }}
                />
                <Text className="text-center text-gray-700 mt-2">{item.name}</Text>
                <Text className="text-center text-gray-600">{item.price}</Text>
              </View>
            ))}
          </ScrollView>
        </View>

        {/* Products Section */}
        <View className="p-4">
          <Text className="text-black font-bold text-lg mb-3">Featured Products</Text>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {productsdata.map((item) => (
              <View
                key={item.id}
                className="w-[240px] bg-[#EDFAFE] h-[121px] p-3 rounded-md items-center flex-row mr-2"
              >
                <Image
                  source={{ uri: `https://gold.arihantai.com${item.img_url}` }}
                  className="h-16 w-16 rounded-full mr-2"
                />
                <View className="flex-col ml-1">
                  <Text className="text-[10px] mb-1 font-bold">{item.name}</Text>
                  <Text className="mb-1">{item.price}</Text>
                  <TouchableOpacity 
                    className="bg-[#013443] w-[80px] h-[20px] rounded-md"
                    onPress={() => productdesc(item)}
                  >
                    <Text className="text-white text-center">View</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </ScrollView>
        </View>

        {/* Bottom Banner */}
        <View className="p-4 mt-6 bg-[#FCF2E6BF] rounded-lg">
          <Text className="text-center text-gray-800 font-semibold text-lg">Your Golden Future, Secure Today</Text>
          <Text className="text-center mt-2 text-gray-600">
            Your golden future starts with easy and transparent investments.
          </Text>
          <Image className="w-full h-80 mt-10" source={require('../assets/securebank.png')} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
