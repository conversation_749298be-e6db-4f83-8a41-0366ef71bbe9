import React, { useState } from "react";
import { View, Text, TextInput, TouchableOpacity } from "react-native";
import Ionicons from '@expo/vector-icons/Ionicons';
import { useNavigation } from "@react-navigation/native";

const LoginOtp = () => {
  const navigation = useNavigation();
  const [email, setEmail] = useState("");

//   const handleSubmit = () => {
//     console.log({ email });
//     navigation.navigate('OTPVerification');
//   };

// const handleSubmit = async () => {
//     try {
//       const response = await fetch('https://gold.arihantai.com/app/otp/login', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//           'Cookie': 'frontend_lang=en_US; session_id=8f2f439b8f98d729c29741e1777f57f5f8983b32',
//         },
//         body: JSON.stringify({
//           jsonrpc: 2.0,
//           params: {
//             login: email,
//           },
//         }),
//       });
  
//       const data = await response.json();
  
//       if (response.ok && !data.error) {
//         console.log('OTP Sent:', data);
//         navigation.navigate('OTPVerification');
//       } else {
//         console.log('Error:', data.error);
//         alert(data.error?.message || 'Something went wrong');
//       }
//     } catch (error) {
//       console.error('Fetch Error:', error);
//       alert('Network error. Please try again.');
//     }
//   };
  
const handleSubmit = async () => {
    try {
      const response = await fetch('https://gold.arihantai.com/app/otp/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'frontend_lang=en_US; session_id=6e7c97b903c197dfbebadcd1cdb2fa9511079d0a', // You can make this dynamic if needed
        },
        body: JSON.stringify({
          jsonrpc: 2.0,
          params: {
            login: email,
          },
        }),
      });
  
      const data = await response.json();
  
      if (response.ok && !data.error) {
        console.log('OTP Sent:', data);
        navigation.navigate('OTPVerification', {
          email: email,
          sessionId: '6e7c97b903c197dfbebadcd1cdb2fa9511079d0a', // Pass session
        });
      } else {
        alert(data.error?.message || 'Failed to send OTP');
      }
    } catch (error) {
      console.error('Fetch Error:', error);
      alert('Network error. Please try again.');
    }
  };
  

  return (
    <View className="flex-1 bg-white p-4">
      {/* Back Arrow */}
      <TouchableOpacity className="left-2 top-10">
      {/* <Ionicons name="arrow-back" size={24} color="black" /> */}
      </TouchableOpacity>

      {/* Title */}
      <View className="top-10 p-5">
      <Text className="text-xl font-bold mt-6 ">Login with OTP</Text>
      {/* Form Section */}
      <View className="mb-4 flex-col mt-4">
            <Text style={{color:'#202244',fontSize:17}} className="mb-3">Email id </Text>
            <View className="border border-gray-300 rounded-lg p-4 h-12"> 
                <View className="bottom-2">
            <Ionicons name="mail-outline" size={18} color="#EAEAEA" />
          <TextInput
            onChangeText={setEmail}
            value={email}
            keyboardType="email-address"
          />
          </View>
          </View>
        </View>

      {/* Submit Button */}
      <TouchableOpacity
        className="bg-[#013443] rounded-lg mt-6 py-3"
        onPress={handleSubmit}
      >
        <Text className="text-white text-center  text-lg">Send Otp</Text>
      </TouchableOpacity>
    </View>
    </View>
  );
};

export default LoginOtp;
