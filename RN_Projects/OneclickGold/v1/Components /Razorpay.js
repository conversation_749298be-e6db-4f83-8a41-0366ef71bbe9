// // // // import React, { useEffect, useRef } from 'react';
// // // // import { View, ActivityIndicator, StyleSheet } from 'react-native';
// // // // import { WebView } from 'react-native-webview';

// // // // const RazorpayWeb = ({ orderId, amount, email, contact, name, onPaymentResponse }) => {
// // // //   const webViewRef = useRef(null);

// // // //   // Generate a valid order ID if not provided
// // // //   const validOrderId = orderId || `order_${Math.random().toString(36).substring(2, 15)}`;

// // // //   const htmlContent = `
// // // //     <!DOCTYPE html>
// // // //     <html>
// // // //       <head>
// // // //         <meta name="viewport" content="width=device-width, initial-scale=1.0">
// // // //         <title>Razorpay Checkout</title>
// // // //         <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
// // // //         <style>
// // // //           body {
// // // //             margin: 0;
// // // //             padding: 20px;
// // // //             font-family: Arial, sans-serif;
// // // //             display: flex;
// // // //             justify-content: center;
// // // //             align-items: center;
// // // //             min-height: 100vh;
// // // //             background-color: #f5f5f5;
// // // //           }
// // // //           .loader {
// // // //             text-align: center;
// // // //           }
// // // //           .message {
// // // //             margin-top: 20px;
// // // //             font-size: 16px;
// // // //             color: #333;
// // // //           }
// // // //         </style>
// // // //       </head>
// // // //       <body>
// // // //         <div class="loader">
// // // //           <div class="message">Initializing payment gateway...</div>
// // // //         </div>
        
// // // //         <script>
// // // //           document.addEventListener('DOMContentLoaded', function() {
// // // //             setTimeout(function() {
// // // //               try {
// // // //                 var options = {
// // // //                   "key": "rzp_test_k5keKQkrwdoylQ", // Replace with your actual test key
// // // //                   "amount": ${amount}, // Amount should be a number, not a string
// // // //                   "currency": "INR",
// // // //                   "name": "Arihant Gold",
// // // //                   "description": "Purchase",
// // // //                   "image": "https://via.placeholder.com/128", // Optional: Your logo
// // // //                   "order_id": "${validOrderId}", // This might need to come from your backend
// // // //                   "handler": function (response) {
// // // //                     window.ReactNativeWebView.postMessage(JSON.stringify({
// // // //                       status: 'success', 
// // // //                       response: response
// // // //                     }));
// // // //                   },
// // // //                   "prefill": {
// // // //                     "name": "${name || ''}",
// // // //                     "email": "${email || ''}",
// // // //                     "contact": "${contact || ''}"
// // // //                   },
// // // //                   "notes": {
// // // //                     "address": "Customer Address"
// // // //                   },
// // // //                   "theme": {
// // // //                     "color": "#045180"
// // // //                   },
// // // //                   "modal": {
// // // //                     "ondismiss": function() {
// // // //                       window.ReactNativeWebView.postMessage(JSON.stringify({
// // // //                         status: 'dismissed'
// // // //                       }));
// // // //                     }
// // // //                   }
// // // //                 };
                
// // // //                 console.log("Razorpay options:", JSON.stringify(options));
// // // //                 var rzp = new Razorpay(options);
                
// // // //                 rzp.on('payment.failed', function (response) {
// // // //                   console.log("Payment failed:", response);
// // // //                   window.ReactNativeWebView.postMessage(JSON.stringify({
// // // //                     status: 'error',
// // // //                     error: response.error
// // // //                   }));
// // // //                 });
                
// // // //                 // Open the payment modal
// // // //                 rzp.open();
// // // //               } catch (error) {
// // // //                 console.error("Razorpay initialization error:", error);
// // // //                 window.ReactNativeWebView.postMessage(JSON.stringify({
// // // //                   status: 'error',
// // // //                   error: {
// // // //                     description: "Failed to initialize Razorpay: " + error.message
// // // //                   }
// // // //                 }));
// // // //               }
// // // //             }, 1000); // Short delay to ensure everything is loaded
// // // //           });
// // // //         </script>
// // // //       </body>
// // // //     </html>
// // // //   `;

// // // //   const handleMessage = (event) => {
// // // //     if (onPaymentResponse) {
// // // //       onPaymentResponse(event);
// // // //     }
// // // //   };

// // // //   return (
// // // //     <View style={styles.container}>
// // // //       <WebView
// // // //         ref={webViewRef}
// // // //         originWhitelist={['*']}
// // // //         source={{ html: htmlContent }}
// // // //         onMessage={handleMessage}
// // // //         javaScriptEnabled={true}
// // // //         domStorageEnabled={true}
// // // //         startInLoadingState={true}
// // // //         renderLoading={() => (
// // // //           <View style={styles.loaderContainer}>
// // // //             <ActivityIndicator size="large" color="#045180" />
// // // //           </View>
// // // //         )}
// // // //         onError={(syntheticEvent) => {
// // // //           const { nativeEvent } = syntheticEvent;
// // // //           console.error('WebView error:', nativeEvent);
// // // //         }}
// // // //       />
// // // //     </View>
// // // //   );
// // // // };

// // // // const styles = StyleSheet.create({
// // // //   container: {
// // // //     flex: 1,
// // // //     backgroundColor: '#fff'
// // // //   },
// // // //   loaderContainer: {
// // // //     position: 'absolute',
// // // //     top: 0,
// // // //     left: 0,
// // // //     right: 0,
// // // //     bottom: 0,
// // // //     alignItems: 'center',
// // // //     justifyContent: 'center',
// // // //     backgroundColor: '#f5f5f5'
// // // //   }
// // // // });

// // // // export default RazorpayWeb;


// // // import React, { useEffect } from 'react';
// // // import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
// // // import { WebView } from 'react-native-webview';

// // // const RazorpayWeb = ({ orderId, amount, email, contact, name, onPaymentResponse }) => {
// // //   // Format the amount properly (ensure it's a number and convert to paisa)
// // //   const formattedAmount = parseInt(amount, 10).toString();
  
// // //   // Create HTML with dynamic values from props
// // //   const htmlContent = `
// // // <!DOCTYPE html>
// // // <html>
// // //   <head>
// // //     <meta name="viewport" content="width=device-width, initial-scale=1.0">
// // //     <title>Razorpay Checkout</title>
// // //     <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
// // //     <style>
// // //       body, html {
// // //         margin: 0;
// // //         padding: 0;
// // //         width: 100%;
// // //         height: 100%;
// // //         display: flex;
// // //         justify-content: center;
// // //         align-items: center;
// // //         background-color: #f5f5f5;
// // //         font-family: Arial, sans-serif;
// // //       }
      
// // //       #paymentBox {
// // //         background-color: white;
// // //         border-radius: 10px;
// // //         padding: 20px;
// // //         box-shadow: 0 4px 8px rgba(0,0,0,0.1);
// // //         text-align: center;
// // //         max-width: 90%;
// // //       }
      
// // //       h2 {
// // //         color: #045180;
// // //         margin-bottom: 10px;
// // //       }
      
// // //       p {
// // //         color: #555;
// // //         margin-bottom: 20px;
// // //       }
      
// // //       #amountDisplay {
// // //         font-size: 24px;
// // //         font-weight: bold;
// // //         color: #F4672A;
// // //         margin: 15px 0;
// // //       }
      
// // //       #payBtn {
// // //         background-color: #045180;
// // //         color: white;
// // //         border: none;
// // //         border-radius: 5px;
// // //         padding: 12px 30px;
// // //         font-size: 16px;
// // //         cursor: pointer;
// // //         transition: background-color 0.3s;
// // //       }
      
// // //       #payBtn:hover {
// // //         background-color: #033a5e;
// // //       }
// // //     </style>
// // //   </head>
// // //   <body>
// // //     <div id="paymentBox">
// // //       <h2>Complete Your Payment</h2>
// // //       <p>Please proceed to pay the following amount:</p>
// // //       <div id="amountDisplay">₹${(parseInt(formattedAmount, 10)/100).toFixed(2)}</div>
// // //       <button id="payBtn">Pay Now</button>
// // //     </div>

// // //     <script>
// // //       // Auto open Razorpay on page load
// // //       document.addEventListener('DOMContentLoaded', function() {
// // //         // Wait a moment to ensure everything is loaded
// // //         setTimeout(function() {
// // //           document.getElementById('payBtn').click();
// // //         }, 500);
// // //       });
      
// // //       document.getElementById('payBtn').onclick = function(e) {
// // //         var options = {
// // //           "key": "rzp_test_k5keKQkrwdoylQ",
// // //           "amount": "${formattedAmount}",
// // //           "currency": "INR",
// // //           "name": "Arihant Gold",
// // //           "description": "Purchase Payment",
// // //           "order_id": "${orderId || ''}",
// // //           "handler": function(response) {
// // //             window.ReactNativeWebView.postMessage(JSON.stringify({
// // //               status: "success", 
// // //               response: response
// // //             }));
// // //           },
// // //           "prefill": {
// // //             "name": "${name || 'Customer'}",
// // //             "email": "${email || '<EMAIL>'}",
// // //             "contact": "${contact || ''}"
// // //           },
// // //           "theme": {
// // //             "color": "#045180"
// // //           },
// // //           "modal": {
// // //             "ondismiss": function() {
// // //               window.ReactNativeWebView.postMessage(JSON.stringify({
// // //                 status: "error",
// // //                 error: {
// // //                   description: "Payment cancelled by user"
// // //                 }
// // //               }));
// // //             }
// // //           }
// // //         };
        
// // //         var rzp = new Razorpay(options);
// // //         rzp.on('payment.failed', function(response) {
// // //           window.ReactNativeWebView.postMessage(JSON.stringify({
// // //             status: "error",
// // //             error: {
// // //               code: response.error.code,
// // //               description: response.error.description,
// // //               source: response.error.source,
// // //               step: response.error.step,
// // //               reason: response.error.reason,
// // //               metadata: response.error.metadata
// // //             }
// // //           }));
// // //         });
        
// // //         rzp.open();
// // //         e.preventDefault();
// // //       }
// // //     </script>
// // //   </body>
// // // </html>
// // //   `;

// // //   return (
// // //     <View style={styles.container}>
// // //       <WebView
// // //         source={{ html: htmlContent }}
// // //         onMessage={onPaymentResponse}
// // //         javaScriptEnabled={true}
// // //         domStorageEnabled={true}
// // //         scalesPageToFit={true}
// // //         startInLoadingState={true}
// // //         renderLoading={() => (
// // //           <View style={styles.loadingContainer}>
// // //             <ActivityIndicator size="large" color="#045180" />
// // //             <Text style={styles.loadingText}>Loading payment gateway...</Text>
// // //           </View>
// // //         )}
// // //       />
// // //     </View>
// // //   );
// // // };

// // // const styles = StyleSheet.create({
// // //   container: {
// // //     flex: 1,
// // //     backgroundColor: 'white'
// // //   },
// // //   loadingContainer: {
// // //     position: 'absolute',
// // //     left: 0,
// // //     right: 0,
// // //     top: 0,
// // //     bottom: 0,
// // //     alignItems: 'center',
// // //     justifyContent: 'center',
// // //     backgroundColor: 'rgba(255, 255, 255, 0.9)'
// // //   },
// // //   loadingText: {
// // //     marginTop: 10,
// // //     fontSize: 16,
// // //     color: '#555'
// // //   }
// // // });

// // // export default RazorpayWeb;

// // import React from 'react';
// // import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
// // import { WebView } from 'react-native-webview';

// // const RazorpayWeb = ({ orderId, amount, email, contact, name, onPaymentResponse }) => {
// //   // Ensure amount is properly formatted as an integer (Razorpay expects amount in paise)
// //   // Convert to number, round to ensure whole number, then convert to string
// //   const formattedAmount = Math.round(parseFloat(amount)).toString();
  
// //   // Ensure we have default values for all required fields
// //   const safeEmail = email || '<EMAIL>';
// //   const safeContact = contact || '';
// //   const safeName = name || 'Customer';
  
// //   // Add console logging to help with debugging
// //   const injectedJavaScript = `
// //     console.log = function(message) {
// //       window.ReactNativeWebView.postMessage(JSON.stringify({type: 'console', message}));
// //     };
// //     console.error = function(message) {
// //       window.ReactNativeWebView.postMessage(JSON.stringify({type: 'error', message}));
// //     };
// //   `;
  
// //   // Create HTML with dynamic values from props
// // //   const htmlContent = `
// // // <!DOCTYPE html>
// // // <html>
// // //   <head>
// // //     <meta name="viewport" content="width=device-width, initial-scale=1.0">
// // //     <title>Razorpay Checkout</title>
// // //     <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
// // //     <style>
// // //       body, html {
// // //         margin: 0;
// // //         padding: 0;
// // //         width: 100%;
// // //         height: 100%;
// // //         display: flex;
// // //         justify-content: center;
// // //         align-items: center;
// // //         background-color: #f5f5f5;
// // //         font-family: Arial, sans-serif;
// // //       }
      
// // //       #paymentBox {
// // //         background-color: white;
// // //         border-radius: 10px;
// // //         padding: 20px;
// // //         box-shadow: 0 4px 8px rgba(0,0,0,0.1);
// // //         text-align: center;
// // //         max-width: 90%;
// // //       }
      
// // //       h2 {
// // //         color: #045180;
// // //         margin-bottom: 10px;
// // //       }
      
// // //       p {
// // //         color: #555;
// // //         margin-bottom: 20px;
// // //       }
      
// // //       #amountDisplay {
// // //         font-size: 24px;
// // //         font-weight: bold;
// // //         color: #F4672A;
// // //         margin: 15px 0;
// // //       }
      
// // //       #payBtn {
// // //         background-color: #045180;
// // //         color: white;
// // //         border: none;
// // //         border-radius: 5px;
// // //         padding: 12px 30px;
// // //         font-size: 16px;
// // //         cursor: pointer;
// // //         transition: background-color 0.3s;
// // //       }
      
// // //       #payBtn:hover {
// // //         background-color: #033a5e;
// // //       }
      
// // //       #errorMessage {
// // //         color: red;
// // //         margin-top: 10px;
// // //         display: none;
// // //       }
// // //     </style>
// // //   </head>
// // //   <body>
// // //     <div id="paymentBox">
// // //       <h2>Complete Your Payment</h2>
// // //       <p>Please proceed to pay the following amount:</p>
// // //       <div id="amountDisplay">₹${(parseInt(formattedAmount, 10)/100).toFixed(2)}</div>
// // //       <button id="payBtn">Pay Now</button>
// // //       <div id="errorMessage"></div>
// // //     </div>

// // //     <script>
// // //       // Helper function to display errors
// // //       function showError(message) {
// // //         const errorElement = document.getElementById('errorMessage');
// // //         errorElement.style.display = 'block';
// // //         errorElement.textContent = message;
// // //         console.error(message);
// // //       }
      
// // //       // Try-catch wrapper for Razorpay initialization
// // //       function initializeRazorpay() {
// // //         try {
// // //           // Auto open Razorpay on page load
// // //           document.addEventListener('DOMContentLoaded', function() {
// // //             // Wait a moment to ensure everything is loaded
// // //             setTimeout(function() {
// // //               document.getElementById('payBtn').click();
// // //             }, 1000);
// // //           });
          
// // //           document.getElementById('payBtn').onclick = function(e) {
// // //             e.preventDefault();
            
// // //             try {
// // //               var options = {
// // //                 "key": "rzp_test_k5keKQkrwdoylQ", // Your test key
// // //                 "amount": "${formattedAmount}",
// // //                 "currency": "INR",
// // //                 "name": "Arihant Gold",
// // //                 "description": "Purchase Payment",
// // //                 "order_id": "${orderId || ''}",
// // //                 "handler": function(response) {
// // //                   window.ReactNativeWebView.postMessage(JSON.stringify({
// // //                     type: 'payment',
// // //                     status: "success", 
// // //                     response: response
// // //                   }));
// // //                 },
// // //                 "prefill": {
// // //                   "name": "${safeName}",
// // //                   "email": "${safeEmail}",
// // //                   "contact": "${safeContact}"
// // //                 },
// // //                 "theme": {
// // //                   "color": "#045180"
// // //                 },
// // //                 "modal": {
// // //                   "ondismiss": function() {
// // //                     window.ReactNativeWebView.postMessage(JSON.stringify({
// // //                       type: 'payment',
// // //                       status: "error",
// // //                       error: {
// // //                         description: "Payment cancelled by user"
// // //                       }
// // //                     }));
// // //                   }
// // //                 }
// // //               };
              
// // //               console.log("Initializing Razorpay with options:", JSON.stringify(options));
// // //               var rzp = new Razorpay(options);
              
// // //               rzp.on('payment.failed', function(response) {
// // //                 console.error("Payment failed:", JSON.stringify(response.error));
// // //                 window.ReactNativeWebView.postMessage(JSON.stringify({
// // //                   type: 'payment',
// // //                   status: "error",
// // //                   error: {
// // //                     code: response.error.code,
// // //                     description: response.error.description,
// // //                     source: response.error.source,
// // //                     step: response.error.step,
// // //                     reason: response.error.reason,
// // //                     metadata: response.error.metadata
// // //                   }
// // //                 }));
// // //               });
              
// // //               rzp.open();
// // //             } catch (err) {
// // //               showError("Error opening Razorpay: " + err.message);
// // //               window.ReactNativeWebView.postMessage(JSON.stringify({
// // //                 type: 'payment',
// // //                 status: "error",
// // //                 error: {
// // //                   description: "Failed to open Razorpay: " + err.message
// // //                 }
// // //               }));
// // //             }
// // //           };
// // //         } catch (err) {
// // //           showError("Razorpay initialization failed: " + err.message);
// // //           window.ReactNativeWebView.postMessage(JSON.stringify({
// // //             type: 'payment',
// // //             status: "error",
// // //             error: {
// // //               description: "Razorpay initialization failed: " + err.message
// // //             }
// // //           }));
// // //         }
// // //       }
      
// // //       // Initialize with try-catch
// // //       initializeRazorpay();
// // //     </script>
// // //   </body>
// // // </html>
// // //   `;


// // const htmlContent = `
// // <html>
// //   <head>
// //     <meta name="viewport" content="width=device-width, initial-scale=1.0">
// //     <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
// //   </head>
// //   <body>
// //     <script>
// //       var options = {
// //         key: "rzp_test_k5keKQkrwdoylQ", // Your test key
// //         amount: "${amount}",
// //         currency: "INR",
// //         name: "Arihant Gold",
// //         order_id: "${orderId}",
// //         prefill: {
// //           name: "${name}",
// //           email: "${email}",
// //           contact: "${contact}"
// //         },
// //         handler: function(response) {
// //           window.ReactNativeWebView.postMessage(JSON.stringify({
// //             status: 'success',
// //             response: response
// //           }));
// //         },
// //         theme: {
// //           color: "#045180"
// //         }
// //       };
      
// //       var rzp = new Razorpay(options);
// //       rzp.open();
      
// //       rzp.on('payment.failed', function(response) {
// //         window.ReactNativeWebView.postMessage(JSON.stringify({
// //           status: 'error',
// //           error: response.error
// //         }));
// //       });
// //     </script>
// //   </body>
// // </html>
// // `;

// //   // Handle messages from WebView
// //   const handleMessage = (event) => {
// //     try {
// //       const data = JSON.parse(event.nativeEvent.data);
      
// //       // Handle different message types
// //       if (data.type === 'console' || data.type === 'error') {
// //         console.log('WebView:', data.message);
// //       } else if (data.type === 'payment') {
// //         // Forward payment responses to the parent component
// //         onPaymentResponse({
// //           nativeEvent: { 
// //             data: JSON.stringify({
// //               status: data.status,
// //               response: data.response,
// //               error: data.error
// //             }) 
// //           }
// //         });
// //       } else {
// //         // Backward compatibility
// //         onPaymentResponse(event);
// //       }
// //     } catch (error) {
// //       console.error('Error parsing WebView message:', error);
// //       onPaymentResponse(event); // Forward the original event as fallback
// //     }
// //   };

// //   return (
// //     <View style={styles.container}>
// //       <WebView
// //         source={{ html: htmlContent }}
// //         onMessage={handleMessage}
// //         javaScriptEnabled={true}
// //         domStorageEnabled={true}
// //         scalesPageToFit={true}
// //         startInLoadingState={true}
// //         injectedJavaScript={injectedJavaScript}
// //         onError={(syntheticEvent) => {
// //           const { nativeEvent } = syntheticEvent;
// //           console.error('WebView error: ', nativeEvent);
// //         }}
// //         onHttpError={(syntheticEvent) => {
// //           const { nativeEvent } = syntheticEvent;
// //           console.error('WebView HTTP error: ', nativeEvent);
// //         }}
// //         renderLoading={() => (
// //           <View style={styles.loadingContainer}>
// //             <ActivityIndicator size="large" color="#045180" />
// //             <Text style={styles.loadingText}>Loading payment gateway...</Text>
// //           </View>
// //         )}
// //       />
// //     </View>
// //   );
// // };

// // const styles = StyleSheet.create({
// //   container: {
// //     flex: 1,
// //     backgroundColor: 'white'
// //   },
// //   loadingContainer: {
// //     position: 'absolute',
// //     left: 0,
// //     right: 0,
// //     top: 0,
// //     bottom: 0,
// //     alignItems: 'center',
// //     justifyContent: 'center',
// //     backgroundColor: 'rgba(255, 255, 255, 0.9)'
// //   },
// //   loadingText: {
// //     marginTop: 10,
// //     fontSize: 16,
// //     color: '#555'
// //   }
// // });

// // export default RazorpayWeb;


// /////bkp code : 

// import React, { useRef } from 'react';
// import { View, Text } from 'react-native';
// import { WebView } from 'react-native-webview';

// const RazorpayWeb = ({ orderId, amount, email, contact, name }) => {
//   const webViewRef = useRef(null);

//   const injectedJavaScript = `
//   var options = {
//   "key": "rzp_test_k5keKQkrwdoylQ",
//   "amount": "50000",
//   "currency": "INR",
//   "name": "Test",
//   "description": "No order_id test",
//   "handler": function (response){
//     window.ReactNativeWebView.postMessage(JSON.stringify({ status: 'success', response: response }));
//   },
//   "prefill": {
//     "name": "Manali",
//     "email": "<EMAIL>",
//     "contact": "7875455113"
//   },
//   "theme": { "color": "#3399cc" }
// };`;

//   const htmlContent = `
// <!DOCTYPE html>
// <html>
//   <head>
//     <title>Razorpay Checkout</title>
//     <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
//   </head>
//   <body>
//     <button id="payBtn">Pay Now</button>
//     <script>
//       document.getElementById('payBtn').onclick = function(e){
//         var options = {
//           "key": "rzp_test_k5keKQkrwdoylQ",
//           "amount": "10000",
//           "currency": "INR",
//           "name": "Test Company",
//           "description": "Test Transaction",
//           "handler": function (response){
//             window.ReactNativeWebView.postMessage(JSON.stringify({status: "success", response: response}));
//           },
//           "prefill": {
//             "name": "Manali",
//             "email": "<EMAIL>",
//             "contact": "7227829979"
//           },
//           "theme": {
//             "color": "#3399cc"
//           }
//         };
//         var rzp = new Razorpay(options);
//         rzp.open();
//         e.preventDefault();
//       }
//     </script>
//   </body>
// </html>
// `;

//   const onMessage = (event) => {
//     const data = JSON.parse(event.nativeEvent.data);
//     console.log('Payment success response:', data);
//     // Send to backend for verification
//   };

//   return (
//     <View style={{ flex: 1, justifyContent: 'center', padding: 40 }}>
//       <Text> view </Text>
//       <WebView
//         ref={webViewRef}
//         originWhitelist={['*']}
//         source={{ html: htmlContent }}
//         onMessage={onMessage}
//         javaScriptEnabled
//         domStorageEnabled
//         startInLoadingState
//       />
//     </View>
//   );
// };

// export default RazorpayWeb;


// import React from 'react';
// import { View } from 'react-native';
// import { WebView } from 'react-native-webview';

// const RazorpayWeb = ({ orderId, amount, email, contact, name, onPaymentResponse }) => {
//   // Create a proper HTML page with the exact Razorpay configuration
//   const htmlContent = `
//     <!DOCTYPE html>
//     <html>
//       <head>
//         <meta name="viewport" content="width=device-width, initial-scale=1.0">
//         <title>Razorpay Payment</title>
//         <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
//         <style>
//           body { font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #f5f5f5; }
//           .container { text-align: center; padding: 20px; }
//           button { background-color: #045180; color: white; border: none; padding: 12px 20px; border-radius: 4px; font-size: 16px; cursor: pointer; }
//         </style>
//       </head>
//       <body>
//         <div class="container">
//           <h2>Complete Your Payment</h2>
//           <p>Amount: ₹1000</p>
//           <button id="payBtn">Pay Now</button>
//         </div>

//         <script>
//           document.addEventListener('DOMContentLoaded', function() {
//             // Auto-trigger payment on page load
//             setTimeout(function() {
//               document.getElementById('payBtn').click();
//             }, 1000);
//           });
          
//           document.getElementById('payBtn').onclick = function() {
//             var options = {
//               "key": "rzp_test_k5keKQkrwdoylQ", 
//               "amount": "1000",
//               "currency": "INR",
//               "name": "Arihant Gold",
//               "description": "Order "
//               "order_id": " 1", 
//                "handler": function (response){
//             window.ReactNativeWebView.postMessage(JSON.stringify({status: "success", response: response}));
//           },
//           "prefill": {
//             "name": "Manali",
//             "email": "<EMAIL>",
//             "contact": "7227829979"
//           },
//               "theme": {
//                 "color": "#045180"
//               },
//               "modal": {
//                 "ondismiss": function() {
//                   window.ReactNativeWebView.postMessage(JSON.stringify({
//                     status: "error",
//                     error: {
//                       description: "Payment cancelled by user"
//                     }
//                   }));
//                 }
//               }
//             };
            
//             var rzp = new Razorpay(options);
//             rzp.open();
//           };
//         </script>
//       </body>
//     </html>
//   `;

  
//   const onMessage = (event) => {
//     const data = JSON.parse(event.nativeEvent.data);
//     console.log('Payment success response:', data);
//     // Send to backend for verification
//   };

//   return (
//     <View style={{ flex: 1 }}>
//       <WebView
//         source={{ html: htmlContent }}
//         onMessage={onMessage}
//         javaScriptEnabled={true}
//         domStorageEnabled={true}
//         startInLoadingState={true}
//       />
//     </View>
//   );
// };

// export default RazorpayWeb;

//////////////////16 may 

import React from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';

const RazorpayWeb = ({ orderId, amount, email, contact, name, onPaymentResponse }) => {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Razorpay Payment</title>
        <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            display: flex; 
            justify-content: center; 
            align-items: center; 
            height: 100vh; 
            margin: 0; 
            background-color: #f5f5f5; 
          }
          .container { 
            text-align: center; 
            padding: 20px; 
          }
          button { 
            background-color: #045180; 
            color: white; 
            border: none; 
            padding: 12px 20px; 
            border-radius: 4px; 
            font-size: 16px; 
            cursor: pointer; 
          }
          .loading {
            margin-top: 20px;
            color: #666;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h2>Complete Your Payment</h2>
          <p>Amount: ₹${(amount/100).toFixed(2)}</p>
          <button id="payBtn">Pay Now</button>
          <p id="loadingText" class="loading">Loading payment gateway...</p>
        </div>

        <script>
          document.addEventListener('DOMContentLoaded', function() {
            // Hide loading text when button is clicked
            document.getElementById('payBtn').addEventListener('click', function() {
              document.getElementById('loadingText').style.display = 'none';
            });
            
            // Auto-click after 1 second if user hasn't clicked
            setTimeout(function() {
              if (!window.paymentTriggered) {
                document.getElementById('payBtn').click();
              }
            }, 1000);
          });
          
          document.getElementById('payBtn').onclick = function() {
            window.paymentTriggered = true;
            
            var options = {
              "key": "rzp_test_k5keKQkrwdoylQ",
              "amount": "500",
              "currency": "INR",
              "name": "Arihant Gold",
              "description": "Order ID: ${orderId}",
              "handler": function(response) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  status: "success",
                  response: response
                }));
              },
              "prefill": {
                "name": "${name}",
                "email": "${email}",
                "contact": "${contact}"
              },
              "theme": {
                "color": "#045180"
              },
              "modal": {
                "ondismiss": function() {
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    status: "error",
                    error: {
                      description: "Payment cancelled by user"
                    }
                  }));
                }
              }
            };
            
            var rzp = new Razorpay(options);
            rzp.open();
          };
        </script>
      </body>
    </html>
  `;

  return (
    <View style={{ flex: 1 }}>
      <WebView
        source={{ html: htmlContent }}
        // onMessage={onPaymentResponse}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        mixedContentMode="always"
        injectedJavaScript={`
          window.ReactNativeWebView = window.ReactNativeWebView || WebView;
          true;
        `}
      />
    </View>
  );
};

export default RazorpayWeb;
