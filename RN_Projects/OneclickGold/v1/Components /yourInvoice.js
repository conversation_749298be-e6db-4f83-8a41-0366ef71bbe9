import React, { useState, useEffect } from "react";
import { 
  View, 
  Text, 
  TouchableOpacity, 
  FlatList, 
  ActivityIndicator, 
  Modal, 
  ScrollView,
  StyleSheet,
  RefreshControl,
  TextInput
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import Slider from "@react-native-community/slider";

const YourInvoice = ({ navigation }) => {
  // State management
  const [ordersData, setOrdersData] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [priceRange, setPriceRange] = useState([20, 5000]);
  const [refreshing, setRefreshing] = useState(false);
  
  // Filter options
  const filterData = {
    categories: ["Last 30 days", "2025", "2024", "2023", "Older"],
  };

  // Filter states
  const [selectedFilters, setSelectedFilters] = useState({
    metal: "Gold",
    category: [],
  });

  const [appliedFilters, setAppliedFilters] = useState({
    priceRange: [20, 5000],
    metal: "",
    categories: [],
    paymentStatus: ""
  });

  // Fetch orders from API
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch("https://gold.arihantai.com/api/v1/invoices");
      
      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }
      
      const data = await response.json();
      
      // Transform API data to ensure consistent structure
      const formattedData = data.data.map(order => ({
        ...order,
        amount_residual: parseFloat(order.amount_residual) || 0,
        invoice_date: order.invoice_date ? order.invoice_date.split('T')[0] : '',
        payment_state: order.payment_state || 'unknown'
      }));
      
      setOrdersData(formattedData);
      setFilteredOrders(formattedData);
      setError(null);
    } catch (err) {
      setError(err.message);
      console.error("API Error:", err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchOrders();
  }, []);

  // Pull to refresh
  const onRefresh = () => {
    setRefreshing(true);
    fetchOrders();
  };

  // Apply filters function
  const applyFilters = () => {
    let filteredResults = [...ordersData];

    // Price Range Filter (including 0 amounts)
    filteredResults = filteredResults.filter(order => {
      const amount = order.amount_residual;
      return (amount >= appliedFilters.priceRange[0] && 
              amount <= appliedFilters.priceRange[1]) || 
             amount === 0;
    });

    // Metal Filter
    if (appliedFilters.metal) {
      filteredResults = filteredResults.filter(order => 
        order.metal && order.metal.toLowerCase() === appliedFilters.metal.toLowerCase()
      );
    }

    // Categories Filter (Year/Date)
    if (appliedFilters.categories.length > 0) {
      filteredResults = filteredResults.filter(order => {
        if (!order.invoice_date) return false;
        
        const orderDate = new Date(order.invoice_date);
        const orderYear = orderDate.getFullYear().toString();
        
        return appliedFilters.categories.some(category => {
          switch(category) {
            case "Last 30 days":
              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
              return orderDate >= thirtyDaysAgo;
            case "2025":
            case "2024":
            case "2023":
              return orderYear === category;
            case "Older":
              return orderYear < "2023";
            default:
              return false;
          }
        });
      });
    }

    // Payment Status Filter
    if (appliedFilters.paymentStatus) {
      filteredResults = filteredResults.filter(order => {
        const statusMap = {
          "paid": ["paid"],
          "not paid": ["not_paid", "not paid"],
          "partial": ["partial", "in_payment"]
        };
        
        const validStatuses = statusMap[appliedFilters.paymentStatus.toLowerCase()] || 
                            [appliedFilters.paymentStatus.toLowerCase()];
        
        return validStatuses.includes(order.payment_state.toLowerCase());
      });
    }

    setFilteredOrders(filteredResults);
    setModalVisible(false);
  };

  // Clear all filters
  const clearAllFilters = () => {
    setAppliedFilters({
      priceRange: [20, 5000],
      metal: "",
      categories: [],
      paymentStatus: ""
    });
    setSelectedFilters({
      metal: "Gold",
      category: [],
    });
    setPriceRange([20, 2000]);
    setFilteredOrders(ordersData);
  };

  // Handle search filtering
  const handleSearch = (text) => {
    setSearch(text);
    if (text === "") {
      setFilteredOrders(ordersData);
    } else {
      const filtered = ordersData.filter((order) =>
        order.name?.toLowerCase().includes(text.toLowerCase()) ||
        order.id?.toString().includes(text) ||
        order.invoice_number?.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredOrders(filtered);
    }
  };

  // Render each order item
  const renderOrderItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.orderItem}
      onPress={() => navigation.navigate('OrderDetails', { orderId: item.id })}
    >
      <View style={styles.orderHeader}>
      <View className="flex-row justify-between items-center mb-2">
      <Text className="text-[#20224480] text-[15px] font-semibold">Invoice</Text>
        <Text className="text-black text-[14.5px] font-medium ml-10">{item.name}</Text>
      </View>
      </View>

      <View className="flex-row justify-between items-center mb-2">
        <Text className="text-[#20224480] font-semibold">Invoice Date</Text>
        <Text className="text-black text-[14.5px] font-medium">
          {item.invoice_date ? item.invoice_date : "Not Available"}
        </Text>
      </View>

      <View className="flex-row justify-between items-center mb-2">
        <Text className="text-[#20224480] text-[15px] font-semibold">Due Date</Text> 
        <Text className="text-black text-[14.5px] font-medium">{item.invoice_due_date}</Text> {/* Due Date not available in the data : api ma j nai  */}
      </View>

      <View className="flex-row justify-between items-center mb-2">
        <Text className="text-[#20224480] text-[15px] font-semibold">Status</Text>
          <View style={[
          styles.statusBadge,
          item.payment_state === 'paid' && styles.paidStatus,
          (item.payment_state === 'not_paid' || item.payment_state === 'not paid') && styles.notPaidStatus,
          (item.payment_state === 'partial' || item.payment_state === 'in_payment') && styles.partialStatus
        ]}>
          <Text style={styles.statusText}>
            {item.payment_state.replace('_', ' ').toUpperCase()}
          </Text>
        </View>
      </View>

      <View className="flex-row justify-between items-center">
        <Text className="text-[#20224480] text-[15px] font-semibold">Amount Due</Text>
        <Text className="text-black text-[14.5px] font-medium">
          {/* {item.currency} */}
          ₹ {item.amount_residual}
        </Text>
      </View>
      
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View className="flex-row px-3 py-2">
        <View className="flex-row items-center w-[308px] h-[33px] bg-white border border-[#D9D9D9] rounded-md px-3 py-2 mb-6">
          <Ionicons name="search" size={20} color="#C0C0C0" />
          <TextInput
            placeholder="Search all orders"
            value={search}
            onChangeText={handleSearch}
            className="flex-1 ml-2 text-[14px] text-black"
          />
        </View>
        <TouchableOpacity className="ml-2 bg-[#000000] w-[34px] h-[34px] rounded-md" 
        // onPress={() => navigation.navigate('Filters')}
        onPress={() => setModalVisible(true)}
        >
          <View className="ml-1 mt-1">
            <Ionicons name="filter" size={24} color="white" />
          </View>
        </TouchableOpacity>
      </View>

      {/* Error Message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={fetchOrders}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Orders List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#013443" />
        </View>
      ) : (
        <FlatList
          data={filteredOrders}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderOrderItem}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#013443']}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="document-text-outline" size={60} color="#ccc" />
              <Text style={styles.emptyText}>No invoices found</Text>
              <Text style={styles.emptySubtext}>
                {search ? "Try a different search" : "Try adjusting your filters"}
              </Text>
            </View>
          }
          contentContainerStyle={styles.listContent}
        />
      )}

      {/* Filter Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filters</Text>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>
            </View>
            
            <ScrollView 
              style={styles.modalContent}
              showsVerticalScrollIndicator={false}
            >
              {/* Price Range Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterTitle}>Price Range</Text>
                <View style={styles.priceRangeContainer}>
                  <Text style={styles.priceRangeText}>
                    ₹{appliedFilters.priceRange[0]} - ₹{appliedFilters.priceRange[1]}
                  </Text>
                  <Slider
                    style={styles.slider}
                    minimumValue={20}
                    maximumValue={5000}
                    step={50}
                    minimumTrackTintColor="#013443"
                    maximumTrackTintColor="#D1D5DB"
                    thumbTintColor="#013443"
                    value={appliedFilters.priceRange[1]}
                    onValueChange={(value) => {
                      setAppliedFilters(prev => ({
                        ...prev,
                        priceRange: [prev.priceRange[0], Math.round(value)]
                      }));
                    }}
                  />
                </View>
              </View>
              
              {/* Date/Year Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterTitle}>Date/Year</Text>
                <View style={styles.optionsContainer}>
                  {filterData.categories.map((category, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => {
                        const newCategories = appliedFilters.categories.includes(category)
                          ? appliedFilters.categories.filter(c => c !== category)
                          : [...appliedFilters.categories, category];
                        
                        setAppliedFilters(prev => ({
                          ...prev,
                          categories: newCategories
                        }));
                      }}
                      style={styles.optionItem}
                    >
                      <View style={[
                        styles.optionCheckbox,
                        appliedFilters.categories.includes(category) && styles.optionCheckboxSelected
                      ]}>
                        {appliedFilters.categories.includes(category) && (
                          <Ionicons name="checkmark" size={16} color="white" />
                        )}
                      </View>
                      <Text style={styles.optionText}>{category}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              {/* Payment Status Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterTitle}>Payment Status</Text>
                <View style={styles.statusOptionsContainer}>
                  {[
                    { id: 1, label: "Paid", value: "paid" },
                    { id: 2, label: "Not Paid", value: "not paid" },
                    { id: 3, label: "Partial", value: "partial" }
                  ].map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      onPress={() => {
                        setAppliedFilters(prev => ({
                          ...prev,
                          paymentStatus: prev.paymentStatus === option.value ? "" : option.value
                        }));
                      }}
                      style={[
                        styles.statusOption,
                        appliedFilters.paymentStatus === option.value && styles.statusOptionSelected
                      ]}
                    >
                      <Text style={[
                        styles.statusOptionText,
                        appliedFilters.paymentStatus === option.value && styles.statusOptionTextSelected
                      ]}>
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>
            
            {/* Filter Actions */}
            <View style={styles.filterActions}>
              <TouchableOpacity 
                onPress={clearAllFilters}
                style={styles.clearButton}
              >
                <Text style={styles.clearButtonText}>Clear All</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={applyFilters}
                style={styles.applyButton}
              >
                <Text style={styles.applyButtonText}>Apply Filters</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

// Styles (same as previous implementation)
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#013443',
    marginBottom: 12,
  },
  searchContainer: {
    // position: 'relative',
    // marginBottom: 12,
  },
  searchInput: {
    backgroundColor: '#f1f5f9',
    borderRadius: 8,
    padding: 10,
    paddingLeft: 40,
    fontSize: 16,
  },
  searchIcon: {
    position: 'absolute',
    left: 12,
    top: 12,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#013443',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
  },
  filterButtonText: {
    color: 'white',
    marginLeft: 8,
    fontWeight: '500',
  },
  errorContainer: {
    backgroundColor: '#fee2e2',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  errorText: {
    color: '#b91c1c',
    marginBottom: 8,
  },
  retryText: {
    color: '#013443',
    fontWeight: 'bold',
  },
  orderItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#013443',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 5,
    color:'white',
    borderRadius: 8,
  },
  paidStatus: {
    backgroundColor: '#0F7754',
    // color:'white',
  },
  notPaidStatus: {
    backgroundColor: '#F4A300',
    // color:'white',
  },
  partialStatus: {
    backgroundColor: '#F4A300',
    // color:'white',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color:'white',
  },
  customerName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  orderDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#013443',
  },
  actionText: {
    color: '#013443',
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    height: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#013443',
  },
  closeButton: {
    position: 'absolute',
    right: 0,
    backgroundColor: '#013443',
    borderRadius: 20,
    padding: 4,
  },
  modalContent: {
    flex: 1,
  },
  filterSection: {
    marginBottom: 24,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  priceRangeContainer: {
    backgroundColor: '#f8fafc',
    padding: 16,
    borderRadius: 8,
  },
  priceRangeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#013443',
    marginBottom: 8,
  },
  slider: {
    width: '100%',
    height: 20,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  optionItem: {
    width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  optionCheckbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#013443',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionCheckboxSelected: {
    backgroundColor: '#013443',
  },
  optionText: {
    color: '#333',
  },
  statusOptionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusOption: {
    width: '30%',
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 6,
    alignItems: 'center',
  },
  statusOptionSelected: {
    backgroundColor: '#013443',
    borderColor: '#013443',
  },
  statusOptionText: {
    color: '#64748b',
  },
  statusOptionTextSelected: {
    color: 'white',
  },
  filterActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  clearButton: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#013443',
    borderRadius: 8,
    flex: 1,
    marginRight: 12,
    alignItems: 'center',
  },
  clearButtonText: {
    color: '#013443',
    fontWeight: '600',
  },
  applyButton: {
    padding: 12,
    backgroundColor: '#013443',
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  applyButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  listContent: {
    paddingBottom: 20,
  },
});

export default YourInvoice;