import React, { useState, useEffect,useContext } from "react";
import { View, Text, Image, TouchableOpacity, ScrollView, Alert } from "react-native";
import { SessionContext } from './login';

const SipDeliveries = () => {
  
  const { sessionId, loadSessionData } = useContext(SessionContext);
  const [loading, setLoading] = useState(false);
  const [deliveries, setDeliveries] = useState([]);

  useEffect(() => {
    const fetchDeliveryData = async () => {
      setLoading(true);
      try {
        const response = await fetch('https://gold.arihantai.com/api/v1/metal/deliveries', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            // Add any necessary headers, like session or authorization
            // 'Authorization': 'Bearer <your-token>', // Replace with actual token if needed
            'Cookie': `session_id=${sessionId}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch delivery data');
        }

        const data = await response.json();
        console.log('Delivery Data:', data); // Log for debugging

        // Assuming the API response has a structure like:
        // { data: [ { id, metal_name, grams, date, address, status }, ... ], status: 'success' }
        if (data.status === 'success' && data.data) {
          setDeliveries(data.data); // Set the fetched deliveries data into state
        } else {
          Alert.alert("Error", "No delivery data found.");
        }
      } catch (error) {
        console.error(error);
        Alert.alert("Error", "Failed to load delivery data");
      } finally {
        setLoading(false);
      }
    };

    fetchDeliveryData();
  }, []);

  // Show a loading spinner or message if the data is still being fetched
  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg font-semibold">Loading delivery data...</Text>
      </View>
    );
  }

  // If no delivery data, show a message indicating no data
  if (deliveries.length === 0) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg font-semibold">No delivery data available</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      <ScrollView>
        {/* Header Section */}
        <View className="items-center mt-6 border-b-[#F1F1F1] border-b-4">
          <Text className="text-lg font-bold text-center text-gray-800">SIP Deliveries</Text>
          <Image
            source={{
              uri: "https://gold.arihantai.com/web/image/product.template/13/image_1024/Happy%20Birthday%20Gold%20Coin%205%20gm%2024kt%20%28995%29?unique=08f1ed8",
            }}
            className="h-[110px] w-[110px] mt-4"
          />
        </View>

        {/* Delivery Info Section */}
        <Text className="text-[#202244] text-[16px] font-semibold mb-2 ml-4 mt-4">Delivery Info</Text>

        {deliveries.map((delivery) => (
          <View key={delivery.id} className="mt-6 mx-4 border border-[#D9D9D9] rounded-md  bg-white ">
            <View className="space-y-3">
              {/* Type */}
              <View className="flex-row justify-between items-center border p-2  border-[#D9D9D9]">
                <Text className="text-gray-500">Metal Type</Text>
                <Text className="text-black font-medium" numberOfLines={1}>
                  {delivery.metal_name}
                </Text>
              </View>

              {/* Grams */}
              <View className="flex-row justify-between items-center border border-[#D9D9D9] p-2">
                <Text className="text-gray-500 mr-4">Grams</Text>
                <Text className="text-black font-medium" numberOfLines={1}>
                  {delivery.grams} g
                </Text>
              </View>

              {/* Date */}
              <View className="flex-row justify-between items-center border border-[#D9D9D9] p-2">
                <Text className="text-gray-500">Date</Text>
                <Text className="text-black font-medium mr-3" numberOfLines={1}>
                  {delivery.date}
                </Text>
              </View>

              {/* Address */}
              <View className="flex-row justify-between border-b border-b-[#D9D9D9] ml-2">
                <Text className="text-[#00000080]">Address</Text>
                <Text className="text-black font-medium w-[140px] h-[90px]">{delivery.address}</Text>
              </View>

              {/* Status */}
              <View className="flex-row justify-between p-2">
                <Text className="text-[#00000080]">Status</Text>
                <Text className="text-black font-medium">{delivery.status}</Text>
              </View>
            </View>
          </View>
        ))}

        {/* Cancel Note */}
        <View className="mt-4 mx-4 mb-24">
          <Text className="text-[#F4672A] font-[500] text-center text-[9px]">
            You can only cancel delivery within 24-36 hours of request
          </Text>
        </View>
-
        {/* Cancel Button */}
        <View className="mx-4 mt-4 mb-4">
          <TouchableOpacity className="bg-[#013443] py-3 rounded-md">
            <Text className="text-white text-center font-semibold text-lg">Cancel</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

export default SipDeliveries;
