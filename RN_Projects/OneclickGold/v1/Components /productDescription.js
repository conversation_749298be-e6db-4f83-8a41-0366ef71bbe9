import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
} from "react-native";
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SessionContext } from "./login";

export default function ProductPage({ route, navigation }) {
  const { sessionId, loadSessionData } = useContext(SessionContext);
  
  // console.log("route",route)
  const { prodId } = route.params;
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDeliveryOption, setSelectedDeliveryOption] = useState('Ambaji Trust - Ambaji (4 Day(s) Delivery)');
  const [selectedShippingMethod, setSelectedShippingMethod] = useState('Home Delivery - 4 Days Delivery to your doorstep');
  const [quantity, setQuantity] = useState(1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedTrust, setSelectedTrust] = useState('Ambaji');
  const [selectedWeight, setSelectedWeight] = useState(null);
  const [currentPrice, setCurrentPrice] = useState(0);
  const [currentVariant, setCurrentVariant] = useState(null);
  const [addingToCart, setAddingToCart] = useState(false);

  const placeholderImage = "https://via.placeholder.com/150";
  const trustOptions = ["Ambaji", "Other Trust"];

  useEffect(() => {
    fetchProductData();
  }, [prodId]);

  const fetchProductData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`https://gold.arihantai.com/api/v1/shop_product/${prodId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          // "Cookie": "frontend_lang=en_US; session_id=2d7b094da847ae1b6993f7516e9407a3a17d18af",
          'Cookie': `session_id=${sessionId}`,
        },
      });

      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      
      const responseData = await response.json();
      setProduct(responseData);
      
      
      // Set initial weight and price
      if (responseData.data?.product?.weight_variants) {
        const weights = Object.keys(responseData.data.product.weight_variants);
        if (weights.length > 0) {
          const firstWeight = weights[0];
          setSelectedWeight(firstWeight);
          updatePriceForWeight(firstWeight, responseData.data.product.weight_variants);
        }
      }
      
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
      console.error("Error fetching product data:", err);
    }
  };

  const updatePriceForWeight = (weight, weightVariants) => {
    if (weightVariants && weightVariants[weight]) {
      const variantInfo = weightVariants[weight];
      setCurrentPrice(variantInfo.price);
      setCurrentVariant(variantInfo.variant_id);
    }
  };

  const handleWeightChange = (weight) => {
    setSelectedWeight(weight);
    if (product?.data?.product?.weight_variants) {
      updatePriceForWeight(weight, product.data.product.weight_variants);
    }
  };

  const handleQuantityChange = (type) => {
    if (type === 'increment') {
      setQuantity(quantity + 1);
    } else if (type === 'decrement' && quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const addProductToCart = async (productData) => {
    try {
      // Get current cart items
      const cartItems = await AsyncStorage.getItem('cartItems');
      let cartArray = cartItems ? JSON.parse(cartItems) : [];
      
      const existingProductIndex = cartArray.findIndex(
        item => item.variantId === productData.variantId
      );
      
      if (existingProductIndex >= 0) {
        cartArray[existingProductIndex].quantity += productData.quantity;
        cartArray[existingProductIndex].totalPrice = 
          cartArray[existingProductIndex].price * cartArray[existingProductIndex].quantity;
      } else {
        cartArray.push(productData);
      }
      
      await AsyncStorage.setItem('cartItems', JSON.stringify(cartArray));
      
      return true;
    } catch (error) {
      console.error('Failed to save product to cart:', error);
      return false;
    }
  };
  
  const handleAddToCart = async () => {
    if (addingToCart) return;
  
    if (!currentVariant || !prodId) {
      Alert.alert("Error", "Please select a product variant");
      return;
    }
  
    setAddingToCart(true);
  
    try {
      const response = await fetch('https://gold.arihantai.com/api/v1/cart/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: 2.0,
          params: {
            product_id: currentVariant,
            quantity: quantity,
          }
        }),
      });
  
      const data = await response.json();

      // console.log(data,"data")
  
      if (response.ok && data?.result) {
        Alert.alert(
          "Success",
          "Product added to cart successfully!",
          [{ text: "OK", onPress: () => navigation.navigate('MyCart') }]
        );
      } else {
        throw new Error(data?.error?.message || 'Unexpected error');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      Alert.alert("Error", "Failed to add product to cart. Please try again.");
    } finally {
      setAddingToCart(false);
    }
  };
  

  const RadioButton = ({ isSelected, onPress, label }) => (
    <TouchableOpacity
      onPress={onPress}
      className="flex-row items-center mb-2"
    >
      <View className={`w-4 h-4 rounded-full border-2 border-[#045180] items-center justify-center mr-3 ${isSelected ? 'border-[#045180]' : ''}`}>
        {isSelected && <View className="w-2 h-2 rounded-full bg-[#045180]" />}
      </View>
      <Text className="text-black text-sm">{label}</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#013443" />
        <Text className="mt-2">Loading product...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-4">
        <Text className="text-red-500 text-lg">Error: {error}</Text>
        <TouchableOpacity 
          className="mt-4 bg-[#013443] px-4 py-2 rounded-md"
          onPress={fetchProductData}
        >
          <Text className="text-white">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!product || !product.data || !product.data.product) {
    return (
      <View className="flex-1 justify-center items-center p-4">
        <Text className="text-lg">No product data available</Text>
      </View>
    );
  }

  const productData = product.data.product;
  const warehouse = product.data.warehouses[0] || {};
  const alternative_products = product.data.alternative_products || [];
  const trending_products = product.data.trending_products || [];
  const weightVariants = productData.weight_variants || {};
  const weightOptions = Object.keys(weightVariants);
  const totalPrice = currentPrice * quantity;

  // Improved image handling - ensure full URL
  const getFullImageUrl = (imagePath) => {
    if (!imagePath) return placeholderImage;
    
    // Check if it's already a full URL
    if (imagePath.startsWith('http')) return imagePath;
    
    // Add the base URL if it's a relative path
    return `https://gold.arihantai.com${imagePath}`;
  };

  const cleanedBase64Image = productData.image_1920.slice(2, -1);
  const productImageUrl = `data:image/jpeg;base64,${cleanedBase64Image}`;
  const similar_products = alternative_products.map(product => ({
    id: product.id,
    image: getFullImageUrl(product.image_1920),
    title: product.name || "Product Name",
    price: `₹${product.list_price?.toFixed(2) || '0.00'}/gm`,
  }));

  // Prepare trending products data
  const trending_products_data = trending_products.map(product => ({
    id: product.id,
    image: getFullImageUrl(product.image_1920),
    title: product.name || "Product Name",
    price: `₹${product.list_price?.toFixed(2) || '0.00'}/gm`,
  }));

  return (
    <View className="flex-1 bg-white p-2">
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="bg-white">
          <Text className="mt-2 text-lg font-bold ml-4" style={{color:'#202244'}}>
            {productData.display_name}
          </Text>
          
          <View className="w-full h-64 justify-center items-center">
            <Image
              source={{ uri: productImageUrl }}
              style={{ width: '100%', height: '100%' }}
              resizeMode="contain"
              onError={(e) => {
                // console.log("Image load error:", e.nativeEvent.error);
                // You can set a state here to use fallback image if needed
              }}
            />
          </View>
        </View>
        <View className="p-4 bg-white">
          <Text className="text-gray-600 font-bold text-base mb-4" style={{color:'#F4672A'}}>
            ₹{currentPrice.toFixed(2)}/gm
          </Text>
          
          {/* Weight Selection */}
          {weightOptions.length > 0 && (
            <>
              <Text className="text-lg font-bold mt-2" style={{color:'#202244'}}>Choose weight</Text>
              <View className="flex flex-row flex-wrap w-full mb-4 border-b border-[#D9D9D9] border-dashed">
                {weightOptions.map((weight, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleWeightChange(weight)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      width: '33%',
                      marginBottom: 18,
                    }}
                  >
                    <View
                      style={{
                        width: 16,
                        height: 16,
                        borderRadius: 8,
                        borderWidth: 1,
                        borderColor: selectedWeight === weight ? "#013443" : "#CCCCCC",
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: 5
                      }}
                    >
                      {selectedWeight === weight && (
                        <View
                          style={{
                            width: 8,
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: "#013443"
                          }}
                        />
                      )}
                    </View>
                    <Text style={{ color: selectedWeight === weight ? "#013443" : "#000" }}>
                      {weight}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </>
          )}

          {/* Product Properties */}
          {productData.attributes && productData.attributes.length > 0 && (
            <View className="border-b mr-2 border-dashed border-[#D9D9D9] pb-4 mb-4">
              <Text className="text-lg font-medium">Product Properties</Text>
              {productData.attributes.map((attribute, index) => (
                <View key={index} className="flex-row justify-between mt-2">
                  <Text className="text-black text-base font-medium text-center">
                    {attribute.attribute_name}
                  </Text>
                  <View className="pr-4">
                    <Text className="text-black text-base font-medium text-center">
                      {attribute.values.map(val => val.value_name).join(', ')}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          )}

          {/* Delivery Options */}
          <View className="mb-2 mt-2">
            <Text className="text-lg font-medium mb-2">Delivery Options</Text>
            
            <TouchableOpacity 
              onPress={() => setSelectedDeliveryOption(warehouse.name)}
              className="flex-row items-center my-2"
            >
              <View className={`w-4 h-4 rounded-full border justify-center items-center mr-2.5 ${
                selectedDeliveryOption === warehouse.name ? "border-[#013443]" : "border-[#CCCCCC]"
              }`}>
                {selectedDeliveryOption === warehouse.name && (
                  <View className="w-2 h-2 rounded-full bg-[#013443]" />
                )}
              </View>
              
              <TouchableOpacity 
                onPress={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex-1 flex-row items-center justify-between p-2 rounded"
              >
                <Text className={selectedDeliveryOption === warehouse.name ? "text-[#013443]" : "text-black"}>
                  {selectedTrust} Trust - {warehouse.name} (4 Day(s) Delivery)
                </Text>
                <MaterialIcons 
                  name={isDropdownOpen ? "keyboard-arrow-up" : "keyboard-arrow-down"} 
                  size={24} 
                  color="black" 
                />
              </TouchableOpacity>
            </TouchableOpacity>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <View className="ml-6 bg-white rounded border border-gray-200 -mt-1">
                {trustOptions.map((trust, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      setSelectedTrust(trust);
                      setIsDropdownOpen(false);
                    }}
                    className={`p-2.5 ${
                      index !== trustOptions.length - 1 ? "border-b border-gray-200" : ""
                    }`}
                  >
                    <Text>{trust}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Shipping Methods */}
            <Text className="text-lg font-medium mb-2">Shipping Method</Text>
            {warehouse.delivery_options?.map((option, index) => (
              <RadioButton
                key={index}
                label={`${option.name} - ${option.description}`}
                isSelected={selectedShippingMethod === `${option.name} - ${option.description}`}
                onPress={() => setSelectedShippingMethod(`${option.name} - ${option.description}`)}
              />
            ))}
          </View>

          {/* Quantity and Add to Cart */}
          <View className="flex-row mt-5 mb-2 border-b border-[#D9D9D9] ">
            <View className="flex-row items-center w-[90px] h-[36px] border border-[#013443] rounded-md mb-4">
              <TouchableOpacity
                className="w-10 h-10 ml-2 mt-2 rounded-lg"
                onPress={() => handleQuantityChange('decrement')}
              >
                <Text className="text-xl font-bold">-</Text>
              </TouchableOpacity>
              <Text className="text-lg font-bold right-2">{quantity}</Text>
              <TouchableOpacity
                className="w-10 h-9 ml-3 mt-2"
                onPress={() => handleQuantityChange('increment')}
              >
                <Text className="text-xl font-bold">+</Text>
              </TouchableOpacity>
            </View>

            {/* Add to Cart Button with AsyncStorage logic */}
            <TouchableOpacity 
              className="bg-[#013443] w-[220px] h-[40px] left-4 rounded-md py-3 items-center"
              onPress={handleAddToCart}
              disabled={addingToCart}
            >
              {addingToCart ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text className="text-white text-md font-medium bottom-1">Add to Cart</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Total Price */}
          <View className="flex-row justify-between border-b h-18 mb-2 mt-2 border-[#D9D9D9] bg-white">
            <Text className="text-lg font-bold ml-4 mb-2">₹{totalPrice.toFixed(2)}</Text>
            <Text className="text-lg font-bold mr-8 mb-2">Product Price Breakup</Text>
          </View>

          {/* Similar Products */}
          {similar_products.length > 0 && (
            <View className="mb-6 bg-white border-b border-[#D9D9D9]" >
              <Text className="text-xl font-bold mb-4 ml-4">Similar Products</Text>
              
              <FlatList
                data={similar_products}
                horizontal
                showsHorizontalScrollIndicator={false}
                renderItem={({ item }) => (
                  <TouchableOpacity 
                    className="ml-4 mb-4 w-40"
                    onPress={() => navigation.push('ProductPage', { prodId: item.id })}
                  >
                    <Image
                      source={{ uri: item.image }}
                      className="w-full h-32 rounded-lg"
                      // defaultSource={require('../assets/placeholder.png')} // Replace with your local placeholder
                    />
                    <Text 
                      numberOfLines={2} 
                      className="text-sm font-bold mt-2"
                      style={{color:'#202244'}}
                    >
                      {item.title}
                    </Text>
                    <Text 
                      className="text-xs font-bold mt-1 mb-1"
                      style={{color:'#F4672A'}}
                    >
                      {item.price}
                    </Text>
                    <TouchableOpacity 
                      className="bg-[#013443] w-[100px] h-[30px] p-2 rounded-md"
                      onPress={() => {
                        navigation.push('ProductPage', { prodId: item.id });
                      }}
                    >
                      <Text className="text-white bottom-1">Add to Cart</Text>
                    </TouchableOpacity>
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item.id.toString()}
              />
            </View>
          )}

          <View className="mb-6 bg-white border-b border-[#D9D9D9]" style={{borderBottomColor:'#D9D9D9'}}>
            <Text className="text-xl ml-4 font-bold mb-4">About Us</Text>
            <Text className="text-black text-base text-justify ml-3 mr-3 mb-7">
              {productData.description_sale || "No description available"}
            </Text>
          </View>

          {trending_products_data.length > 0 && (
            <View className="mb-6 ml-5 bg-white border-b border-[#D9D9D9]">
              <Text className="text-xl font-bold mb-4">Trending Products</Text>
              <FlatList
                data={trending_products_data}
                horizontal
                showsHorizontalScrollIndicator={false}
                renderItem={({ item }) => (
                  <TouchableOpacity 
                    className="mb-6 mr-4 w-36"
                    onPress={() => navigation.push('ProductPage', { prodId: item.id })}
                  >
                    <View className="bg-white border border-[#00000033] rounded-lg p-3 flex-col justify-between h-52">
                      <Image
                        source={{ uri: item.image }}
                        className="w-full h-24 rounded-lg"
                        // defaultSource={require('../assets/placeholder.png')} // Replace with your local placeholder
                      />
                      <Text 
                        className="text-xs text-center font-bold mt-1"
                        numberOfLines={2}
                      >
                        {item.title}
                      </Text>
                      <Text className="text-[#F4672A] text-center mt-1 font-bold text-xs">
                        {item.price}
                      </Text>
                      <TouchableOpacity
                        className="bg-[#013443] mt-2 w-full py-1 rounded-lg items-center justify-center"
                        onPress={() => navigation.push('ProductPage', { prodId: item.id })}
                      >
                        <Text className="text-white text-xs font-medium">View</Text>
                      </TouchableOpacity>
                    </View>
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item.id.toString()}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}