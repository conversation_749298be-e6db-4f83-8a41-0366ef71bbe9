import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Ionicons } from "@expo/vector-icons";

const jsonData = {
  nominee_name: '',
  nominee_email: '',
  nominee_phone: '',
  relation: '',
  labels: {
    nominee_name: 'Nominee Name',
    nominee_email: 'Nominee Email',
    nominee_phone: 'Nominee Phone',
    relation: 'Relation',
  },
};

export default function NomineeContacts() {
  const [formData, setFormData] = useState(jsonData);

  const handleChange = (name, value) => {
    setFormData({ ...formData, [name]: value });
  };

  // Function to handle the API call when the Save button is pressed
  const handleSave = async () => {
    const data = {
      jsonrpc: "2.0",
      params: {
        name: formData.nominee_name,
        email: formData.nominee_email,
        phone: formData.nominee_phone,
        relation: formData.relation,
      }
    };

    try {
      // Send the POST request to the API
      const response = await fetch('https://gold.arihantai.com/create_nominee_contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'frontend_lang=en_US; session_id=d37013ba2566577ea41657f0200686ad4a3e0ab3', // Ensure the session_id is valid
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      if (result.error) {
        // Handle error in API response
        Alert.alert("Error", result.error.message || "An error occurred");
      } else {
        // If successful, show success message
        Alert.alert("Success", "Nominee Contact has been saved successfully!");
        console.log("Nominee Contact Data:", result);
        navigation.navigate('NomineeList');
      }
    } catch (error) {
      // Handle any fetch or network errors
      Alert.alert("Error", "Something went wrong. Please try again.");
      console.error(error);
    }
    navigation.navigate('NomineeList');

  };

  return (
    <View className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 px-4 py-6">
        <View>
          {/* Header */}
          <View className="flex-row items-center px-4 py-3 mb-8">
            <TouchableOpacity>
              <Ionicons name="arrow-back" size={24} color="#000000" />
            </TouchableOpacity>
            <Text className="text-lg font-bold text-[#000000] ml-4">Nominee Contacts</Text>
          </View>

          {/* Input Fields */}
          {Object.entries(formData.labels).map(([key, label]) => (
            <View key={key} className="mb-4">
              <Text className="text-[#000000] text-[16px] ml-1 mb-2 font-bold">{label}</Text>
              <TextInput
                className="border border-[#D9D9D9] rounded-lg px-3 py-2 text-[#000000]"
                // editable={key !== 'nominee_email'} // Non-editable for Email
                value={formData[key]}
                onChangeText={(value) => handleChange(key, value)}
              />
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Save Button */}
      <View className="px-4 py-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className="bg-[#002E3D] border border-[#002E3D] rounded-md px-4 py-2"
          onPress={handleSave} // Handle Save button press
        >
          <Text className="text-white font-semibold text-center">Save</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

