import React, { useState, useEffect,useContext } from "react";
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";

const BankAccountList = () => {

  const { sessionId, loadSessionData } = useContext(SessionContext);

  const [bankAccounts, setBankAccounts] = useState([]); // State to store bank accounts
  const [searchText, setSearchText] = useState(""); // State for search text
  const navigation = useNavigation(); // Hook to navigate between screens

  // Fetch bank account data from the API when the component mounts
  useEffect(() => {
    const fetchBankAccounts = async () => {
      try {
        const response = await fetch('https://gold.arihantai.com/api/bank_account/list', {
          method: 'GET',
          headers: {
            // 'Cookie': 'frontend_lang=en_US; session_id=e8af7d119f50004b70ae445617a4ae66ca190141',
            'Cookie': `session_id=${sessionId}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch bank accounts");
        }

        const data = await response.json();
        setBankAccounts(data.bank_accounts); // Correctly set bank accounts from the response
      } catch (error) {
        console.error(error);
        Alert.alert("Error", "Failed to load bank accounts");
      }
    };

    fetchBankAccounts();
  }, []); // Empty dependency array to run the effect only once when the component mounts

  const CreateBank = () => {
    navigation.navigate('CreateBankAccount')
  }

  // Filtered bank accounts based on search text
  const filteredAccounts = bankAccounts.filter((account) =>
    account.acc_holder_name.toLowerCase().includes(searchText.toLowerCase())
  );

  // Function to navigate to Bank Accounts page with selected account's id
  const handleNavigateToAccountDetails = (accountId) => {
    navigation.navigate('BankAccounts', { accountId }); // Pass accountId as a parameter to the next page
  };

  return (
    <View className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center top-4 justify-between px-4 py-3">
        <TouchableOpacity>
          <Ionicons name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
        <Text className="text-lg font-bold text-[#000000]">Bank Accounts</Text>
        <View className="flex-row items-center space-x-4">
          <TouchableOpacity>
            <Ionicons name="person-circle-outline" size={24} color="#000000" />
          </TouchableOpacity>
          <TouchableOpacity>
            <Ionicons name="cart-outline" size={24} color="#000000" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Input */}
      <View className="px-4 flex-row mt-6">
        <View className="flex-row w-[311px] h-[33px] items-center border border-[#********] rounded-md px-3 py-2">
          <Ionicons name="search-outline" size={20} color="#AEAEAE" />
          <TextInput
            placeholder="Search bank"
            className="ml-2 flex-1 text-[#000]"
            value={searchText}
            onChangeText={setSearchText}
          />
        </View>
        {/* <TouchableOpacity className="bg-black ml-2 mr-2 w-[34px] h-[34px] rounded-md items-center justify-center" onPress={CreateBank}>
          <Ionicons name="add" size={20} color="white" />
        </TouchableOpacity> */}
      </View>

      {/* Bank Accounts List */}
      <ScrollView className="mt-4 px-4">
        {filteredAccounts.length > 0 ? (
          filteredAccounts.map((account) => (
            <TouchableOpacity
              key={account.id}
              onPress={() => handleNavigateToAccountDetails(account.id)} // On click, navigate to details page with id
              className="border-2 border-[#013443] w-[358px] h-[59px] rounded-md p-4 mb-2 flex-row justify-between items-center"
            >
              <View>
                <Text className="text-black font-semibold text-[12.5px]">{account.acc_holder_name}</Text>
                <Text className="text-[#********] text-[12px] mt-1">Bank: {account.bank_name}</Text>
                <Text className="text-[#********] text-[12px] mt-1">Branch: {account.branch}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="black" />
            </TouchableOpacity>
          ))
        ) : (
          <Text className="text-center text-gray-500">No bank accounts found</Text>
        )}
      </ScrollView>
    </View>
  );
};

export default BankAccountList;
