import React, { useState, useEffect,useContext } from "react";
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Picker } from "@react-native-picker/picker";

const EditInformation = () => {
  const { sessionId, loadSessionData } = useContext(SessionContext);
  
  const initialData = {
    name: "",
    email: "",
    l10n_in_pan: "",
    phone: "",
    mobile: "",
    street: "",
    city: "",
    zip: "",
    country_id: '104',  // Default to India
    state_id: null,
  };

  const [formData, setFormData] = useState(initialData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // const sessionId = 'a1880618fb896635271588ae3309675acc3abd95';

  // Fetch user profile
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch user profile
        const profileResponse = await fetch('https://gold.arihantai.com/api/v1/user/profile', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': `session_id=${sessionId}`,
          },
        });

        const profileResult = await profileResponse.json();

        if (profileResponse.ok && profileResult.status === "success") {
          const filteredData = {
            name: profileResult.data.name || '',
            email: profileResult.data.email || '',
            l10n_in_pan: profileResult.data.l10n_in_pan || '',
            phone: profileResult.data.phone || '',
            mobile: profileResult.data.mobile || '',
            street: profileResult.data.street || '',
            city: profileResult.data.city || '',
            zip: profileResult.data.zip || '',
            country_id: profileResult.data.country_id?.toString() || '104',
            state_id: profileResult.data.state_id?.toString() || null,
          };
          
          setFormData(filteredData);
        } else {
          setError("Failed to load user profile.");
        }

        // Fetch countries
        const countriesResponse = await fetch("https://gold.arihantai.com/api/v1/countries");
        const countriesResult = await countriesResponse.json();
        if (countriesResponse.ok && countriesResult.status === "success") {
          setCountries(countriesResult.data);
        }

      } catch (err) {
        setError("An error occurred while fetching data.");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [sessionId]);

  // Fetch states when country_id changes
  useEffect(() => {
    if (formData.country_id && formData.country_id !== 'null') {
      const fetchStates = async () => {
        try {
          const response = await fetch(`https://gold.arihantai.com/api/v1/states/${formData.country_id}`);
          const result = await response.json();
          if (response.ok && result.status === "success") {
            setStates(result.data);
          }
        } catch (err) {
          console.error("Error fetching states", err);
        setStates([]);
        setFormData(prev => ({ ...prev, state_id: null }));
        Alert.alert("Error", "Failed to load states for selected country");
        }
      };

      fetchStates();
    }
  }, [formData.country_id]);

  const handleChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSave = async () => {
    setIsSubmitting(true);
    
    // Prepare the data exactly as the API expects it
    const requestData = {
      jsonrpc: "2.0",
      params: {
        name: formData.name,
        email: formData.email,
        l10n_in_pan: formData.l10n_in_pan,
        phone: formData.phone,
        mobile: formData.mobile,
        street: formData.street,
        city: formData.city,
        zip: formData.zip,
        country_id: parseInt(formData.country_id) || null,
        state_id: parseInt(formData.state_id) || null,
      }
    };

    try {
      const response = await fetch('https://gold.arihantai.com/api/v1/user/profile/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `session_id=${sessionId}`,
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      console.log("API Response:", result);
      
      if (result.status === "success") {
        Alert.alert("Success", result.message);
        
        setFormData(prev => ({
          ...prev,
          country_id: result.data.country_id || prev.country_id,
          state_id: result.data.state_id || prev.state_id,
        }));
      } else {
        Alert.alert("Error", result.message || "Failed to update profile");
      }
    } catch (error) {
      console.error("Error occurred:", error);
      Alert.alert("Error", "An error occurred while updating the profile.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDiscard = () => {
    setFormData(initialData);
    console.log("Changes Discarded");
  };

  if (loading) {
    return <Text>Loading...</Text>;
  }

  if (error) {
    return <Text>{error}</Text>;
  }

  return (
    <View className="flex-1 bg-white">
      <ScrollView className="px-4 py-4">
        <View className="flex-row items-center mb-4">
          <TouchableOpacity>
            <Ionicons name="arrow-back" size={24} color="black" />
          </TouchableOpacity>
          <Text className="text-lg font-bold ml-4 text-black">Details</Text>
        </View>

        {/* Form Fields */}
        {Object.entries(formData).map(([key, value]) => {
          if (key === "state_id" || key === "country_id") {
            return null;
          }

          return (
            <View key={key} className="mb-4">
              <Text className="text-[#000000] ml-1 mb-2 font-bold capitalize">
                {key.replace(/_/g, ' ').replace(/(?:^|\s)\S/g, a => a.toUpperCase())}
              </Text>
              <TextInput
                className="border border-gray-300 text-[15px] rounded-md px-3 py-2 text-[#121111]"
                value={value}
                onChangeText={(text) => handleChange(key, text)}
                placeholder={`Enter ${key.replace(/_/g, ' ')}`}
              />
            </View>
          );
        })}

        {/* Country Dropdown */}
        <View className="mb-4">
          <Text className="text-[#000000] ml-1 mb-2 font-bold">Country</Text>
          <View className="border border-gray-300 rounded-md">
            <Picker
              selectedValue={formData.country_id}
              onValueChange={(itemValue) => {
                handleChange("country_id", itemValue);
                handleChange("state_id", null); // Reset state when country changes
              }}
            >
              {countries.map((country) => (
                <Picker.Item 
                  key={country.id} 
                  label={country.name} 
                  value={country.id.toString()} 
                />
              ))}
            </Picker>
          </View>
        </View>

        {/* State Dropdown */}
        <View className="mb-4">
          <Text className="text-[#000000] ml-1 mb-2 font-bold">State</Text>
          <View className="border border-gray-300 rounded-md">
            <Picker
              selectedValue={formData.state_id}
              onValueChange={(itemValue) => handleChange("state_id", itemValue)}
              enabled={states.length > 0 && formData.country_id}
            >
              <Picker.Item label={states.length ? "Select State" : "Select Country First"} value={null} />
              {states.map((state) => (
                <Picker.Item 
                  key={state.id} 
                  label={state.name} 
                  value={state.id.toString()} 
                />
              ))}
            </Picker>
          </View>
        </View>

        {/* Buttons */}
        <View className="flex-row mt-6">
          <TouchableOpacity
            className="border border-gray-400 rounded-l-md px-4 py-2 flex-1"
            onPress={handleDiscard}
            disabled={isSubmitting}
          >
            <Text className="text-gray-700 font-semibold text-center">Discard</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="bg-[#002E3D] border border-[#002E3D] rounded-r-md px-4 py-2 flex-1"
            onPress={handleSave}
            disabled={isSubmitting}
          >
            <Text className="text-white font-semibold text-center">
              {isSubmitting ? "Saving..." : "Save"}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

export default EditInformation;


