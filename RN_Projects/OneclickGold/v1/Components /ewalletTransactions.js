import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';

const WalletTransactionScreen = () => {
  const [transactionData, setTransactionData] = useState({
    reference: '',
    date: '',
    partner: '',
    bankName: '',
    amount: '',
    status: '',
  });

  // Simulated JSON data (this can be fetched from an API)
  const fetchData = async () => {
    const data = {
      reference: 'New',
      date: '11/07/2024 16:09:54',
      partner: 'Team',
      bankName: 'SBI',
      amount: '₹ 16,937.60',
      status: 'Draft',
    };
    setTransactionData(data);
  };

  useEffect(() => {
    fetchData(); // Fetch data when the component mounts
  }, []);

  const handleTransfer = () => {
    // Add transfer functionality here
    // alert('Transferring E-wallet balance to bank account...');
    Navigation.navigate('Payments');
  };

  return (
    <ScrollView className="flex-1 bg-white px-4 py-8">
      {/* Header */}
      <View className="flex-row items-center mb-6">
        <TouchableOpacity className="ml-3">
        <Ionicons name="arrow-back-sharp" size={24} color="black" />
        </TouchableOpacity>
        <Text className="text-lg font-bold ml-4 flex-1 text-center">E-Wallet Transactions</Text>
      </View>

      {/* Transaction Details */}
      <View className=" rounded-lg px-4 py-4">
        <View className="mb-4 flex-row  justify-between border-b border-dashed border-b-[#D9D9D9]">
          <Text className="text-black font-bold text-sm mb-2">Reference</Text>
          <Text className="text-black mb-2">{transactionData.reference}</Text>
        </View>

        <View className="mb-4 flex-row justify-between border-b border-dashed border-b-[#D9D9D9]">
          <Text className="text-black mb-2 font-bold text-sm">Date</Text>
          <Text className="text-black mb-2 ">{transactionData.date}</Text>
        </View>

        <View className="mb-4 flex-row justify-between  border-b border-dashed border-b-[#D9D9D9]">
          <Text className="text-black  mb-2 font-bold text-sm">Partner</Text>
          <Text className="text-black mb-2  ">{transactionData.partner}</Text>
        </View>

        <View className="mb-4 flex-row justify-between  border-b border-dashed border-b-[#D9D9D9]">
          <Text className="text-black mb-2 font-bold text-sm">Bank Name</Text>
          <Text className="text-black mb-2 ">{transactionData.bankName}</Text>
        </View>

        <View className="mb-4 flex-row justify-between  border-b border-dashed border-b-[#D9D9D9]">
          <Text className="text-black mb-2 font-bold text-sm">Amount</Text>
          <Text className="text-black mb-2 ">{transactionData.amount}</Text>
        </View>

        <View className="mb-4 flex-row justify-between ">
          <Text className="text-black mb-2 font-bold text-sm">Status</Text>
          <View className="bg-[#013443] px-2 mb-2 py-1 rounded-md w-16">
            <Text className="text-white  text-center">
              {transactionData.status}
            </Text>
          </View>
        </View>
      </View>

      {/* Transfer Button */}
      <TouchableOpacity
        className="bg-[#013443] rounded-lg mt-6 py-3 flex-row items-center justify-center"
        onPress={handleTransfer}
      >
        <Text className="text-white font-semibold mr-2">
          Transfer E-wallet to Bank Account
        </Text>
        <Ionicons name="arrow-forward-sharp" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </ScrollView>
  );
};

export default WalletTransactionScreen;
// import { NativeWindStyleSheet } from "nativewind";
// NativeWindStyleSheet.setOutput({
//   default: "native",
// });