import React, { useState, useEffect ,useContext} from "react";
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRoute, useNavigation } from "@react-navigation/native"; // Hook to access route params

const BankAccounts = () => {

  const { sessionId, loadSessionData } = useContext(SessionContext);
  
  const [bankDetails, setBankDetails] = useState({
    acc_number: "",
    acc_holder_name: "",
    bank_name: "",
    branch: "",
    ifsc_code: "",
  });

  const route = useRoute(); // Hook to access the route and params
  const { accountId } = route.params; // Get the accountId from the route params
  const navigation = useNavigation(); // Navigation hook for redirecting after actions

  // Fetch bank account data using the accountId
  useEffect(() => {
    const fetchBankAccountDetails = async () => {
      try {
        const response = await fetch(`https://gold.arihantai.com/api/bank_account/edit/${accountId}`, {
          method: 'GET',
          headers: {
            // 'Cookie': 'frontend_lang=en_US; session_id=e8af7d119f50004b70ae445617a4ae66ca190141',
            'Cookie': `session_id=${sessionId}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch bank account details");
        }

        const data = await response.json();
        setBankDetails(data.bank_account); // Set the bank details from the API response
      } catch (error) {
        console.error(error);
        Alert.alert("Error", "Failed to load bank account details");
      }
    };

    fetchBankAccountDetails();
  }, [accountId]); // Re-run the effect when accountId changes

  const handleChange = (name, value) => {
    setBankDetails({ ...bankDetails, [name]: value });
  };

  // Handle the Edit action (Update bank details)
  const handleEdit = async () => {
    try {
      const response = await fetch(`https://gold.arihantai.com/api/bank_account/update/${accountId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          // 'Cookie': 'frontend_lang=en_US; session_id=d37013ba2566577ea41657f0200686ad4a3e0ab3',
          'Cookie': `session_id=${sessionId}`,

        },
        body: JSON.stringify({
          jsonrpc: 2.0,
          params: {
            acc_number: bankDetails.accountNumber,
            acc_holder_name: bankDetails.acc_holder_name,
            branch: bankDetails.branch,
            ifsc_code: bankDetails.ifsc_code,
            bank_name: bankDetails.bank_name,
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update bank account details");
      }

      const data = await response.json();
      console.log("Bank account updated:", data);
      Alert.alert("Success", "Bank account details updated successfully");
      navigation.goBack(); // Navigate back to the previous screen after success
    } catch (error) {
      console.error(error);
      Alert.alert("Error", "Failed to update bank account details");
    }
  };

  // Handle the Delete action (Delete the bank account)
  const handleDelete = async () => {
    try {
      const response = await fetch(`https://gold.arihantai.com/api/bank_account/delete/${accountId}`, {
        method: 'DELETE',
        headers: {
          // 'Cookie': 'frontend_lang=en_US; session_id=d37013ba2566577ea41657f0200686ad4a3e0ab3',
          'Cookie': `session_id=${sessionId}`,
          
        },
      });

      if (!response.ok) {
        throw new Error("Failed to delete bank account");
      }

      const data = await response.json();
      console.log("Bank account deleted:", data);
      Alert.alert("Success", "Bank account deleted successfully");
      navigation.goBack(); // Navigate back to the previous screen after success
    } catch (error) {
      console.error(error);
      Alert.alert("Error", "Failed to delete bank account");
    }
  };

  const handleSubmit = () => {
    console.log("New Bank Account Data:", bankDetails);
  };

  return (
    <View className="flex-1 top-1 p-1 bg-white">
      <ScrollView className="px-4">
        {/* Header */}
        <View className="flex-row items-center py-3 top-4">
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="black" />
          </TouchableOpacity>
          <Text className="text-lg font-bold ml-4 text-black">
            Edit Bank Account
          </Text>
        </View>

        {/* Form Fields */}
        <View className="mt-4">
          {/* Account Number */}
          <View className="mb-4">
            <Text className="text-black ml-2 mb-2 font-medium">
              Account Number
            </Text>
            <TextInput
              className="border border-[#013443] rounded-md px-3 py-2 text-[#090909]"
              value={bankDetails.acc_number}
              onChangeText={(value) => handleChange("acc_number", value)}
            />
          </View>

          {/* Account Holder Name */}
          <View className="mb-4">
            <Text className="text-black ml-1 mb-2 font-medium">
              Account Holder Name
            </Text>
            <TextInput
              className="border border-[#013443] rounded-md px-3 py-2 text-[#090909]"
              value={bankDetails.acc_holder_name}
              onChangeText={(value) => handleChange("acc_holder_name", value)}
            />
          </View>

          {/* Bank Name */}
          <View className="mb-4">
            <Text className="text-black mb-2 ml-2 font-medium">Bank Name</Text>
            <TextInput
              className="border border-[#013443] rounded-md px-3 py-2 text-[#090909]"
              value={bankDetails.bank_name}
              onChangeText={(value) => handleChange("bank_name", value)}
            />
          </View>

          {/* Branch */}
          <View className="mb-4">
            <Text className="text-black ml-1 mb-2 font-medium">Branch</Text>
            <TextInput
              className="border border-[#013443] rounded-md px-3 py-2 text-[#090909]"
              value={bankDetails.branch}
              onChangeText={(value) => handleChange("branch", value)}
            />
          </View>

          {/* IFSC Code */}
          <View className="mb-6">
            <Text className="text-black ml-1 mb-2 font-medium">IFSC Code</Text>
            <TextInput
              className="border border-[#013443] rounded-md px-3 py-2 text-[#090909]"
              value={bankDetails.ifsc_code}
              onChangeText={(value) => handleChange("ifsc_code", value)}
            />
          </View>

          {/* Actions */}
          {/* <Text className="text-black mb-2 font-bold ">Actions</Text> */}
          <View className="flex-row mb-6 mt-8">
            <TouchableOpacity
              className="bg-[#002E3D] rounded-l-md border border-[#002E3D] px-4 py-2 flex-1"
              onPress={handleEdit}
            >
              <Text className="text-white font-semibold text-center">Update</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="border border-gray-400 rounded-r-md px-4 py-2 flex-1"
              onPress={handleDelete}
            >
              <Text className="text-gray-700 font-semibold text-center">Delete</Text>
            </TouchableOpacity>
          </View>

        </View>
      </ScrollView>
    </View>
  );
};

export default BankAccounts;
