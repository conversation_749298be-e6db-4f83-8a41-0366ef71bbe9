import React, { useState, useEffect } from "react";
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";

const NomineeList = ({ navigation }) => {
  // State variables
  const [nominees, setNominees] = useState([]); // To store fetched nominees data
  const [searchText, setSearchText] = useState(""); // For search functionality
  const [loading, setLoading] = useState(true); // To track loading state

  useEffect(() => {
    // Fetch nominees data from API
    const fetchNominees = async () => {
      try {
        const response = await fetch("https://gold.arihantai.com/api/v1/nominees", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            // Add authorization header if needed
            "Authorization": "Bearer <your-token>", // Replace with actual token if needed
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch nominees");
        }

        const data = await response.json();
        if (data.status === "success" && data.data) {
          setNominees(data.data); // Set the fetched nominees data into state
        } else {
          Alert.alert("Error", "No nominees data available.");
        }
      } catch (error) {
        console.error(error);
        Alert.alert("Error", "Failed to load nominee data");
      } finally {
        setLoading(false); // Set loading to false once the data is fetched or error occurs
      }
    };

    fetchNominees();
  }, []);

  // Filtered nominees based on search text
  const filteredNominees = nominees.filter((nominee) =>
    nominee.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const NomineeForm = () => {
    navigation.navigate('NomineeForm');
  }

  // Loading state
  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg font-semibold">Loading nominees...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      {/* Header */}
      {/* <View className="flex-row items-center top-4 justify-between px-4 py-3">
        <TouchableOpacity>
          <Ionicons name="arrow-back" size={24} color="#000000" />
        </TouchableOpacity>
        <Text className="text-lg font-bold text-[#000000]">Nominees List</Text>
        <View className="flex-row items-center space-x-4">
          <TouchableOpacity>
            <Ionicons name="person-circle-outline" size={24} color="#000000" />
          </TouchableOpacity>
          <TouchableOpacity>
            <Ionicons name="cart-outline" size={24} color="#000000" />
          </TouchableOpacity>
        </View>
      </View> */}

      {/* Search Input */}
      <View className="px-4 flex-row mt-6">
        <View className="flex-row w-[311px] h-[33px] items-center border border-[#04518026] rounded-md px-3 py-2">
          <Ionicons name="search-outline" size={20} color="#AEAEAE" />
          <TextInput
            placeholder="Search nominee’s / number"
            className="ml-2 flex-1 text-[#AEAEAE]"
            value={searchText}
            onChangeText={setSearchText}
          />
        </View>
        <TouchableOpacity className="bg-black ml-2 mr-2 w-[34px] h-[34px] rounded-md items-center justify-center" onPress={NomineeForm}>
          <Ionicons name="add" size={20} color="white" />
        </TouchableOpacity>
      </View>

      {/* Nominee List */}
      <ScrollView className="mt-4 px-4">
        {filteredNominees.map((nominee) => (
          <TouchableOpacity
            key={nominee.id}
            className="border-2 border-[#013443] w-[358px] h-[59px] rounded-md p-4 mb-2 flex-row justify-between items-center"
            onPress={() => {
              // Navigate to NomineeForm and pass nominee id as parameter
              navigation.navigate("NomineeDetails", { nomineeId: nominee.id });
            }}
          >
            <View>
              <Text className="text-black font-semibold text-[12.5px]">{nominee.name}</Text>
              <Text className="text-[#20224480] text-[12px] mt-1">Contact No : {nominee.phone}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="black" />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

export default NomineeList;

