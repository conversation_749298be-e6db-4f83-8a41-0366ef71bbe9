
import React, { useState, useEffect, useContext} from "react";
import { View, Text, TextInput, ScrollView, Image, TouchableOpacity, FlatList, Modal, ActivityIndicator } from "react-native";
import Ionicons from '@expo/vector-icons/Ionicons';
import Slider from '@react-native-community/slider';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {SessionContext} from './login';

const NoProductPage = ({ navigation }) => {
  const { sessionId, loadSessionData } = useContext(SessionContext);
  
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);  // To store subcategories
  const [products, setProducts] = useState([]);
  const [allProducts, setAllProducts] = useState([]); // Store all products for filtering
  const [loading, setLoading] = useState(true);
  const [categoryLoading, setCategoryLoading] = useState(false);
  const [error, setError] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null); // Track selected category
  const [selectedSubCategory, setSelectedSubCategory] = useState(null); // Track selected subcategory
  const [searchQuery, setSearchQuery] = useState('');
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [cartItems, setCartItems] = useState([]);
  const [minPrice, setMinPrice] = useState(0);
  const [maxPrice, setMaxPrice] = useState(5000);
  const [attributeData, setAttributeData] = useState([]);
  const [attributeLoading, setAttributeLoading] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    metal: "Gold",
    category: [],
    gram: [],
    price: [0, 5000],
    attributes: {},
  });

  const BASE_URL = "https://gold.arihantai.com";

  const showToastWithAnimation = (message) => {
    setToastMessage(message);
    setShowToast(true);
    setTimeout(() => {
      setShowToast(false);
    }, 3000);
  };

  const productdesc = (product) => {
    navigation.navigate('ShopProduct', { 
      prodId: product.id,
      productData: product // Pass the entire product object if needed
    })
  };

  const fetchCartItems = async () => {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/cart`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // 'Cookie': `frontend_lang=en_US; session_id=b60aef71f7a16e335f631756ab6b79331515f273`
          'Cookie': `session_id=${sessionId}`,

        }
      });

      if (response.ok) {
        const data = await response.json();
        // console.log("data of carts", data);
        if (data.result && data.result.cart_items) {
          setCartItems(data.result.cart_items);
        }
      }
    } catch (error) {
      console.error("Error fetching cart items:", error);
    }
  };

  // Call this in useEffect to load cart items when component mounts
  useEffect(() => {
    fetchCartItems();
  }, []);

  const handleAddToCart = async (itemId, quantity = 1) => {
    try {
      const productToAdd = products.find(item => item.id === itemId);
      
      if (!productToAdd) {
        showToastWithAnimation("Product not found");
        return;
      }
  
      // Check if product has multiple variants that require selection
      if (productToAdd.attributes && Array.isArray(productToAdd.attributes)) {
        // Check if there's any attribute with multiple values requiring user selection
        const hasMultipleOptions = productToAdd.attributes.some(attr => 
          attr.values && attr.values.length > 1
        );
        
        if (hasMultipleOptions) {
          // Navigate to product detail page for variant selection
          navigation.navigate('ShopProduct', { 
            prodId: itemId,
            productData: productToAdd,
            showVariantSelector: true // Flag to auto-open variant selector
          });
          return; // Exit function to prevent adding directly to cart
        }
      }
      let variantId = null;
      
      // If product has attributes with single values, use the first value of each attribute
      if (productToAdd.attributes && Array.isArray(productToAdd.attributes)) {
        const variantValueIds = productToAdd.attributes.map(attr => {
          if (attr.values && attr.values.length > 0) {
            return attr.values[0].value_id;
          }
          return null;
        }).filter(Boolean);
        
        if (productToAdd.variant_id) {
          variantId = productToAdd.variant_id;
        }
      }
  
      const requestData = {
        jsonrpc: "2.0",
        params: {
          product_id: itemId,
          quantity: quantity,
          // Include variant ID if available
          ...(variantId && { variant_id: variantId })
        }
      };
  
      const response = await fetch(`${BASE_URL}/api/v1/cart/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Cookie': `frontend_lang=en_US; session_id=b60aef71f7a16e335f631756ab6b79331515f273`
          'Cookie': `session_id=${sessionId}`,
        },
        body: JSON.stringify(requestData)
      });
  
      if (response.status === 401 || response.status === 403 || 
          response.status === 440 || !response.ok) {
        
        const newToken = await refreshSessionToken();
        if (!newToken) {
          showToastWithAnimation("Session expired. Please log in again.");
          return;
        }
  
        const retryResponse = await fetch(`${BASE_URL}/api/v1/cart/add`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': `frontend_lang=en_US; session_id=${newToken}`
          },
          body: JSON.stringify(requestData)
        });
  
        // console.log(retryResponse,"454")
  
        if (!retryResponse.ok) {
          throw new Error(`Server responded with ${retryResponse.status}`);
        }
  
        const retryResult = await retryResponse.json();
        if (retryResult.error) {
          throw new Error(retryResult.error.message || 'Failed to add to cart');
        }
      } else {
        // Process the original successful response
        const result = await response.json();
        
        if (result.error) {
          // Check if the error is about session expiration
          if (result.error.message?.toLowerCase().includes('session') || 
              result.error.data?.name?.toLowerCase().includes('session')) {
            
            const newToken = await refreshSessionToken();
            if (!newToken) return;
            
            // Retry the request with the new token (recursive call with the same parameters)
            return handleAddToCart(itemId, quantity);
          }
          throw new Error(result.error.message || 'Failed to add to cart');
        }
      }
  
      // Update the local state for cart items
      const existingCartItemIndex = cartItems.findIndex(item => item.id === itemId);
      let updatedCartItems = [...cartItems];
      let message = '';
      
      if (existingCartItemIndex > -1) {
        updatedCartItems[existingCartItemIndex] = {
          ...updatedCartItems[existingCartItemIndex],
          quantity: updatedCartItems[existingCartItemIndex].quantity + quantity
        };
        message = `${productToAdd.name} quantity updated in cart`;
      } else {
        updatedCartItems.push({
          ...productToAdd,
          quantity: quantity,
          isAdded: true
        });
        message = `${productToAdd.name} added to cart`;
      }
      
      setCartItems(updatedCartItems);
      
      // Update the products state to reflect cart changes
      setProducts(products.map(item =>
        item.id === itemId
          ? { ...item, isAdded: true, quantity: (item.quantity || 0) + quantity }
          : item
      ));
      
      showToastWithAnimation(message);
    } catch (error) {
      console.error("Error adding to cart:", error);
      
      // More specific error messages based on error type
      if (error.message?.toLowerCase().includes('session')) {
        showToastWithAnimation("Session error. Please try again.");
      } else if (error.message?.toLowerCase().includes('network')) {
        showToastWithAnimation("Network error. Please check your connection.");
      } else {
        showToastWithAnimation(`Failed to add to cart: ${error.message || 'Unknown error'}`);
      }
    }
  };

  // Improved handleDecrement function
  const handleDecrement = async (itemId) => {
    const product = products.find(item => item.id === itemId);
    if (!product || product.quantity <= 1) return;
    
    try {
      // Check if we have a valid session token
      let currentToken = sessionToken;
      if (!currentToken) {
        currentToken = await refreshSessionToken();
        if (!currentToken) {
          showToastWithAnimation("Failed to establish session. Please restart the app.");
          return;
        }
      }
      
      const requestData = {
        jsonrpc: "2.0",
        params: {
          product_id: itemId,
          quantity: -1
        }
      };
  
      const response = await fetch(`${BASE_URL}/api/v1/cart/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Cookie': `frontend_lang=en_US; session_id=${currentToken}`
          'Cookie': `session_id=${sessionId}`,
        },
        body: JSON.stringify(requestData)
      });
  
      // Check for session expiration (401 or 403 status)
      if (response.status === 401 || response.status === 403 || 
          response.status === 440 || !response.ok) {
        
        // If session expired, try to refresh the token and retry the request once
        const newToken = await refreshSessionToken();
        if (!newToken) {
          showToastWithAnimation("Session expired. Please log in again.");
          return;
        }

        // Retry the request with the new token
        const retryResponse = await fetch(`${BASE_URL}/api/v1/cart/add`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': `frontend_lang=en_US; session_id=${newToken}`
          },
          body: JSON.stringify(requestData)
        });

        if (!retryResponse.ok) {
          throw new Error(`Server responded with ${retryResponse.status}`);
        }

        const retryResult = await retryResponse.json();
        if (retryResult.error) {
          throw new Error(retryResult.error.message || 'Failed to update cart');
        }
      } else {
        // Process the original successful response
        const result = await response.json();
        
        if (result.error) {
          // Check if the error is about session expiration
          if (result.error.message?.toLowerCase().includes('session') || 
              result.error.data?.name?.toLowerCase().includes('session')) {
            
            const newToken = await refreshSessionToken();
            if (!newToken) return;
            
            // Retry the request with the new token (recursive call with the same parameters)
            return handleDecrement(itemId);
          }
          throw new Error(result.error.message || 'Failed to update cart');
        }
      }
  
      setProducts(products.map(item =>
        item.id === itemId
          ? {
              ...item,
              quantity: item.quantity - 1,
              isAdded: item.quantity - 1 === 0 ? false : true
            }
          : item
      ));
      
      setCartItems(cartItems.map(item =>
        item.id === itemId
          ? {
              ...item,
              quantity: item.quantity - 1,
              isAdded: item.quantity - 1 === 0 ? false : true
            }
          : item
      ).filter(item => item.quantity > 0)); // Remove items with quantity 0
      
    } catch (error) {
      console.error("Error decrementing quantity:", error);
      
      // More specific error messages based on error type
      if (error.message?.toLowerCase().includes('session')) {
        showToastWithAnimation("Session error. Please try again.");
      } else if (error.message?.toLowerCase().includes('network')) {
        showToastWithAnimation("Network error. Please check your connection.");
      } else {
        showToastWithAnimation("Failed to update quantity. Please try again.");
      }
    }
  };

  // Improved handleIncrement function
  const handleIncrement = async (itemId) => {
    const product = products.find(item => item.id === itemId);
    if (!product) return;
    
    try {
      await handleAddToCart(itemId, 1); // Use our improved handleAddToCart method
    } catch (error) {
      console.error("Error incrementing quantity:", error);
      showToastWithAnimation("Failed to increase quantity. Please try again.");
    }
  };

  const handleProductPress = (product) => {
    // Ensure we have minimum required data
    if (!product?.id) {
      console.error("Invalid product data", product);
      showToastWithAnimation("Invalid product data");
      return;
    }
  
    navigation.navigate('ShopProduct', {
      prodId: product.id,
      productData: {
        ...product,
        // Ensure we have these basic fields at minimum
        id: product.id,
        name: product.name || 'Unnamed Product',
        price: product.price || 0,
        img_url: product.img_url || '',
        attributes: product.attributes || []
      }
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        const headers = {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          // 'Cookie': 'frontend_lang=en_US; session_id=5d7965ad1cf8a8c6acc826033296527271bbf913'
          'Cookie': `session_id=${sessionId}`,
        };

        // const response = await fetch(`${BASE_URL}/api/v3/shop?main_category_id=${selectedCategory.id}`, {
        const response = await fetch(`${BASE_URL}/api/v3/shop`, {
          method: 'GET',
          headers: headers,
        });

        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
        }

        const text = await response.text();
        let data;

        try {
          data = JSON.parse(text);
        } catch (parseError) {
          console.error("Failed to parse response as JSON:", parseError);
          throw new Error("Failed to parse server response as JSON");
        }

        if (data && data.data) {
          const { main_categories, sub_categories, products, recommended, total_pages } = data.data;
          // console.log("sub", subCategories);

          setCategories(main_categories || []);
          setSubCategories(sub_categories || []);  // Store subcategories from the response

          const processedProducts = (products || []).map(p => ({
            ...p,
            isAdded: false,
            quantity: 0
          }));

          // console.log("Products:", processedProducts);
          setProducts(processedProducts);
          setAllProducts(processedProducts); // Store all products for filtering
          setRecommended(recommended || []);
          setTotalPages(total_pages || 1);
          setError(null);
        } else {
          throw new Error('Invalid data structure received');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    fetchAttributes();
  }, []);

  const fetchAttributes = async () => {
    try {
      setAttributeLoading(true);
  
      const response = await fetch(
        `${BASE_URL}/api/v3/product.attribute`,
        {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            // 'Cookie': 'frontend_lang=en_US; session_id=7546891d028b4bfb52ab0843fc40c12361f5b0fb'
            'Cookie': `session_id=${sessionId}`,
          },
        }
      );
  
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
  
      const data = await response.json();
      console.log("Fetched attribute data:", data);
      
      // Set the attribute data directly - assuming data is the array of attributes
      if (Array.isArray(data)) {
        setAttributeData(data);
      } else {
        // If data is not in the expected format, check if there's a data property
        setAttributeData(data.data || []);
      }
    } catch (error) {
      console.error("Error fetching attribute data", error);
      setAttributeData([]);
    } finally {
      setAttributeLoading(false);
    }
  };


  const fetchProductsByCategory = async (category, subCategoryId = null) => {
    try {
      setCategoryLoading(true);
      setCurrentPage(1); // Reset to first page
      setSelectedSubCategory(subCategoryId);

      const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        // 'Cookie': 'frontend_lang=en_US; session_id=7546891d028b4bfb52ab0843fc40c12361f5b0fb'
        'Cookie': `session_id=${sessionId}`,
      };

      let url = `${BASE_URL}/api/v3/shop?main_category_id=${category.id}`;
      if (subCategoryId) {
        // url += `&sub_category_id=${subCategoryId}`;  // Add subcategory to the URL
        url = `${BASE_URL}/api/v3/shop?sub_category_id=${subCategoryId}`;  // Add subcategory to the URL
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: headers,
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }

      const text = await response.text();
      let data;

      try {
        data = JSON.parse(text);
      } catch (parseError) {
        throw new Error("Failed to parse server response as JSON");
      }

      if (data && data.data && data.data.products) {
        const categoryProducts = data.data.products.map(p => ({
          ...p,
          isAdded: false,
          quantity: 0
        }));
        setProducts(categoryProducts);
        setTotalPages(data.data.total_pages || 1);
      } else {
        setProducts([]);
        setTotalPages(1);
      }
    } finally {
      setCategoryLoading(false);
    }
  };

  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
    setSelectedSubCategory(null); // Reset subcategory selection
    fetchProductsByCategory(category); // Fetch products for selected category
  };

  const toggleFilter = (filterType, value) => {
    setSelectedFilters(prevState => {
      // Create a copy of the current filters
      const updatedFilters = { ...prevState };
      
      // Handle different filter types
      if (filterType === 'metal') {
        // For metal, simply set the value
        updatedFilters.metal = value;
      } 
      else if (filterType === 'category' || filterType === 'gram') {
        // For category and gram, toggle in array
        const currentValues = updatedFilters[filterType] || [];
        if (currentValues.includes(value)) {
          // Remove value if already selected
          updatedFilters[filterType] = currentValues.filter(item => item !== value);
        } else {
          // Add value if not selected
          updatedFilters[filterType] = [...currentValues, value];
        }
      }
      
      return updatedFilters;
    });
  };

  const handleApplyFilters = () => {
    console.log("Applying filters with attributes:", selectedFilters.attributes);
    
    const attributeNameMap = {};
    attributeData.forEach(attr => {
      attributeNameMap[attr.id] = {
        name: attr.name,
        values: {}
      };
      
      if (attr.value_ids && Array.isArray(attr.value_ids)) {
        attr.value_ids.forEach(val => {
          attributeNameMap[attr.id].values[val.id] = val.name;
        });
      }
    });
    
    // Log applied filters in a more readable format
    console.log("Applied filters:");
    console.log("Price range:", `₹${selectedFilters.price[0]} - ₹${selectedFilters.price[1]}`);
    
    Object.entries(selectedFilters.attributes).forEach(([attrId, valueId]) => {
      if (attrId === 'dropdownOpen') return;
      
      const attrInfo = attributeNameMap[attrId];
      if (!attrInfo) return;
      
      if (Array.isArray(valueId)) {
        if (valueId.length > 0) {
          console.log(`${attrInfo.name}:`, valueId.map(id => attrInfo.values[id]).join(", "));
        }
      } else {
        console.log(`${attrInfo.name}:`, attrInfo.values[valueId]);
      }
    });
  
    try {
      setCategoryLoading(true);
      
      // LOCAL FILTERING LOGIC
      let filteredProducts = [...allProducts];
      
      // Apply price filter - with error handling for undefined prices
      filteredProducts = filteredProducts.filter(product => {
        // Skip price filtering if price is undefined
        if (product.price === undefined || product.price === null) return true;
        
        // Handle number or string price
        const numericPrice = typeof product.price === 'number' ? 
          product.price : 
          parseFloat(String(product.price).replace(/[^\d.]/g, ''));
        
        // Handle NaN cases
        if (isNaN(numericPrice)) return true;
        
        return numericPrice >= selectedFilters.price[0] && numericPrice <= selectedFilters.price[1];
      });
      
      // Apply attribute filters
      Object.entries(selectedFilters.attributes).forEach(([attrId, valueId]) => {
        if (attrId === 'dropdownOpen') return;
        
        // Skip if no value is selected
        if ((Array.isArray(valueId) && valueId.length === 0) || valueId === null) return;
        
        const numericAttrId = Number(attrId);
        
        filteredProducts = filteredProducts.filter(product => {
          // Check if product has the attributes array
          if (!Array.isArray(product.attributes)) return false;
          
          // Find the matching attribute in the product's attributes array
          const productAttribute = product.attributes.find(attr => 
            attr.attribute_id === numericAttrId
          );
          
          if (!productAttribute || !Array.isArray(productAttribute.values)) return false;
          
          // Check if any of the product's attribute values match the selected value
          if (Array.isArray(valueId)) {
            // For multi-select attributes
            return valueId.some(vId => 
              productAttribute.values.some(val => val.value_id === vId)
            );
          } else {
            // For single-select attributes
            return productAttribute.values.some(val => val.value_id === valueId);
          }
        });
      });
      
      // Update products state with filtered results
      setProducts(filteredProducts.map(p => ({
        ...p,
        isAdded: false,
        quantity: 0
      })));
      
      setCurrentPage(1);
      console.log(`Found ${filteredProducts.length} products with applied filters`);
      
    } catch (error) {
      console.error("Error applying filters:", error);
      alert("Failed to apply filters. Please try again.");
    } finally {
      setCategoryLoading(false);
      setModalVisible(false);  // Close modal after applying filters
    }
  };
  
  // Toggle selection of attribute values
  const toggleAttribute = (attributeId, valueId) => {
    setSelectedFilters(prevState => {
      const updatedAttributes = { ...prevState.attributes };
      if (updatedAttributes[attributeId] === valueId) {
        delete updatedAttributes[attributeId]; // Deselect if already selected
      } else {
        updatedAttributes[attributeId] = valueId; // Select new value
      }
      return {
        ...prevState,
        attributes: updatedAttributes,
      };
    });
  };

  // Toggle checkbox for multiple selection
  const toggleMultipleAttribute = (attributeId, valueId) => {
    setSelectedFilters(prevState => {
      const updatedAttributes = { ...prevState.attributes };
      const currentValues = updatedAttributes[attributeId] || [];

      if (Array.isArray(currentValues)) {
        if (currentValues.includes(valueId)) {
          updatedAttributes[attributeId] = currentValues.filter(id => id !== valueId); // Deselect
        } else {
          updatedAttributes[attributeId] = [...currentValues, valueId]; // Select
        }
      } else {
        updatedAttributes[attributeId] = [valueId]; // Initialize as array with first selection
      }

      return {
        ...prevState,
        attributes: updatedAttributes,
      };
    });
  };

  const handlePriceChange = (newMinPrice, newMaxPrice) => {
    setMinPrice(newMinPrice);
    setMaxPrice(newMaxPrice);
    setSelectedFilters(prevState => ({
      ...prevState,
      price: [newMinPrice, newMaxPrice],
    }));
  };

  const filterData = {
    metals: ["Gold", "Silver"],
    categories: [
      "Gold Coin", "Gold Bar", "Silver Coin", "Silver Bar",],
    grams: ["10g", "25g", "50g", "100g", "250g", "500g"],
  };

  const handleClearFilters = () => {
    setSelectedFilters({
      metal: "Gold",
      category: [],
      gram: [],
    });
  };

  const renderEmptyState = () => (
    <View className="flex-1 items-center justify-center py-20">
      <Image className="w-80 h-64" source={require('../assets/goldensearch.png')} />
      <Text className="mt-8 text-lg font-medium text-gray-700 mb-2">
        SORRY! No product found
      </Text>
      <Text className="text-sm text-gray-500 text-center mb-5 px-10">
        We could not find any products related to your search.
      </Text>
    </View>
  );




  const handleSubCategorySelect = (subCategory) => {
    // console.log("testing", subCategory);
    setSelectedSubCategory(subCategory);
    fetchProductsByCategory(selectedCategory, subCategory.id); // Fetch products for selected subcategory
  };

  const getFullImageUrl = (imageUrl) => {
    if (!imageUrl) return 'https://via.placeholder.com/150';
    return imageUrl.startsWith('http') ? imageUrl : `${BASE_URL}${imageUrl}`;
  };

  const filteredProducts = products.filter(product =>
    product.name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleLoadMore = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prevPage => prevPage + 1);
    }
  };

  return (
    <View className="flex-1 bg-white">
            {showToast && (
        // <Animated.View 
        <View 
          style={{
            position: 'absolute',
            bottom: 90,
            left: 0,
            right: 0,
            backgroundColor: 'rgba(0,0,0,0.7)',
            padding: 15,
            marginHorizontal: 20,
            borderRadius: 10,
            alignItems: 'center',
            zIndex: 1000,
            // opacity: fadeAnim
          }}
        >
          <Text style={{color: 'white'}}>{toastMessage}</Text>
        {/* </Animated.View> */}
        </View>
      )}
      <ScrollView className="bg-white"
        showsHorizontalScrollIndicator={false}>
        {/* Header */}
        <View className="flex-col bg-[#ffe8ac] h-[120px] pb-4">
      {/* First row - Profile and Cart */}
      <View className="flex-row justify-end items-center px-4 pt-4">
        <TouchableOpacity 
          className="mr-4"
          onPress={() => navigation.navigate('AccountScreen')}
        >
          <FontAwesome name="user-circle-o" size={24} color="#045180" />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => navigation.navigate('MyCart')}>
          <Ionicons name="cart-outline" size={24} color="#045180" />
        </TouchableOpacity>
      </View>
      
      {/* Second row - Search and Filter */}
      <View className="flex-row items-center px-4 mt-4">
        <View className="flex-1 flex-row items-center bg-white rounded-md px-3 h-10 mr-2">
          <Ionicons name="search-sharp" size={20} color="black" />
          <TextInput
            placeholder="SEARCH"
            className="flex-1 ml-2"
            value={searchQuery}
            onChangeText={(text) => setSearchQuery(text)}
          />
        </View>
        <TouchableOpacity
          className="w-10 h-10 items-center justify-center"
          onPress={() => setModalVisible(true)}
        >
          <Ionicons name="options-outline" size={24} color="#202244" />
        </TouchableOpacity>
      </View>
    </View>

        {/* Categories */}
        {categories.length > 0 && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="flex-row py-3 px-4 bg-white border-b border-dashed border-b-[#D9D9D9]"
          >
            {categories.map((item, index) => (
              <TouchableOpacity
                key={index}
                className={`items-center mx-2 ${selectedCategory?.id === item.id ? 'opacity-100' : 'opacity-70'}`}
                onPress={() => handleCategorySelect(item)}
              >
                <View className={`w-16 h-16 rounded-full ${selectedCategory?.id === item.id ? 'border-2 border-[#013443]' : ''}`}>
                  <Image
                    source={{ uri: getFullImageUrl(item.image_url) }}
                    className="w-full h-full rounded-full"
                    resizeMode="contain"
                  />
                </View>
                <Text
                  className={`text-xs text-center w-16 mt-2 ${selectedCategory?.id === item.id ? 'text-[#013443] font-bold' : 'text-gray-700'
                    }`}
                >
                  {item.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}

        {/* Subcategories */}
        {selectedCategory && subCategories.length > 0 && (
          <View className="py-3 px-4">
            <Text className="text-lg font-bold">Sub Categories</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {subCategories.map((subCat) => (
                <TouchableOpacity
                  key={subCat.id}
                  // className={`px-4 py-2 bg-gray-300 rounded-md mr-2`}
                  className={`px-4 py-2 rounded-md mr-2`}
                  onPress={() => handleSubCategorySelect(subCat)}
                >
                  {/* <View className={`w-16 h-16 rounded-full ${selectedCategory?.id === item.id ? 'bg-gray-200' : ''}`}> */}
                  <View className={`w-20 h-20 rounded-full`}>
                    <Image
                      source={{ uri: getFullImageUrl(subCat.image_url) }}
                      className="w-12 h-12 object-cover rounded-full"
                    />
                    <Text className="text-sm">{subCat.name}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Products */}
{loading || categoryLoading ? (
      <View className="flex-1 items-center justify-center py-20">
        <ActivityIndicator size="large" color="#013443" />
        <Text className="mt-4 text-base text-gray-600">Loading products...</Text>
      </View>
    ) : filteredProducts.length > 0 ? (
      <View className="px-4">
        {filteredProducts.map((item) => (
          <View key={item.id} className="bg-white border-b border-gray-200 border-dashed py-4">
            <TouchableOpacity 
              className="flex-row"
              onPress={() => handleProductPress(item)}
            >
              <Image
                source={{ uri: getFullImageUrl(item.img_url) }}
                className="w-[120px] h-[120px] rounded-lg"
                defaultSource={require('../assets/goldensearch.png')}
              />
              <View className="flex-1 ml-4">
                <Text className="font-bold text-[14px]" style={{ color: '#202244' }}>
                  {item.name}
                </Text>
                <Text className="font-bold text-[13px] mt-1" style={{ color: '#F4672A' }}>
                  {item.price}
                </Text>
                <Text className="text-[13px] font-bold mt-1" style={{ color: '#202244' }}>
                  {item.variants?.attributes || 'No attributes'}
                </Text>
                <Text className="text-[10px] mt-1" style={{ color: '#202244' }}>
                  Also available in {item.gram}
                </Text>
                <View className="mt-3">
                  {!item.isAdded ? (
                    <TouchableOpacity
                      style={{ backgroundColor: '#013443' }}
                      className="w-36 h-7 rounded-lg justify-center items-center"
                      onPress={() => handleAddToCart(item.id)}
                    >
                      <Text className="text-white text-[10px]">ADD TO CART</Text>
                    </TouchableOpacity>
                  ) : (
                    <View className="flex-row w-[94px] h-[30px] rounded-md bg-[#013443] items-center">
                      <TouchableOpacity
                        className="w-10 h-8 flex items-center justify-center"
                        onPress={() => handleDecrement(item.id)}
                      >
                        <Text className="text-white text-lg">-</Text>
                      </TouchableOpacity>
                      <Text className="mx-2 text-lg text-white">{item.quantity}</Text>
                      <TouchableOpacity
                        className="w-8 h-8 flex items-center justify-center"
                        onPress={() => handleIncrement(item.id)}
                      >
                        <Text className="text-white text-lg">+</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              </View>
            </TouchableOpacity>
          </View>
        ))}
      </View>
    ) : (
      renderEmptyState()
    )}


        {/* Products */}
      </ScrollView>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View className="flex-1 justify-end bg-black/50">
          <TouchableOpacity
            onPress={() => setModalVisible(false)}
            className="absolute top-16 left-[45%] bg-[#202244CC] rounded-full p-2"
          >
            <Ionicons name="close" size={24} color="white" />
          </TouchableOpacity>
          <View className="bg-white rounded-t-3xl p-5 h-3/4">
            {/* Filter Content */}
            <Text className="text-xl font-bold text-[#202244] mb-4">Filters</Text>
            <ScrollView showsVerticalScrollIndicator={false}>
              {/* Price Range Filter */}
              <Text className="text-[#202244] font-semibold mb-2">Price Range</Text>
              <View className="mb-6">
                <Slider
                  style={{ width: "100%", height: 40 }}
                  minimumValue={0}
                  maximumValue={5000}
                  step={10}
                  value={minPrice}
                  onValueChange={(value) => handlePriceChange(value, maxPrice)}
                  minimumTrackTintColor="#013443"
                  maximumTrackTintColor="#d3d3d3"
                />
                <Slider
                  style={{ width: "100%", height: 40 }}
                  minimumValue={0}
                  maximumValue={5000}
                  step={10}
                  value={maxPrice}
                  onValueChange={(value) => handlePriceChange(minPrice, value)}
                  minimumTrackTintColor="#013443"
                  maximumTrackTintColor="#d3d3d3"
                />
                <Text className="text-[#202244]">{`Price: ₹${minPrice} - ₹${maxPrice}`}</Text>
              </View>


          {attributeLoading ? (
            <ActivityIndicator size="small" color="#013443" />
          ) : attributeData.length > 0 ? (
            <>
              <Text className="text-lg font-bold text-[#202244] mb-2">Product Attributes</Text>
              {attributeData.map((attribute) => {
                // Skip attributes with no values
                if (!attribute.value_ids || attribute.value_ids.length === 0) return null;
                
                return (
                  <View key={attribute.id} className="mb-6">
                    <Text className="text-[#202244] font-semibold mb-2">{attribute.name}</Text>

                    {/* RADIO BUTTON DISPLAY */}
                    {attribute.display_type === "radio" && (
                      <View className="flex-row flex-wrap">
                        {attribute.value_ids.map((value) => (
                          <TouchableOpacity
                            key={value.id}
                            className="flex-row items-center mb-2 mr-4"
                            onPress={() => toggleAttribute(attribute.id, value.id)}
                          >
                            <View className="flex-row items-center">
                              <View
                                className={`w-5 h-5 rounded-full border-2 border-[#013443] items-center justify-center`}
                              >
                                {selectedFilters.attributes[attribute.id] === value.id && (
                                  <View className="w-3 h-3 rounded-full bg-[#013443]" />
                                )}
                              </View>
                              <Text className="text-sm text-[#202244] ml-2">{value.name}</Text>
                            </View>
                          </TouchableOpacity>
                        ))}
                      </View>
                    )}

                    {/* CHECKBOX DISPLAY */}
                    {attribute.display_type === "checkbox" && (
                      <View className="flex-row flex-wrap">
                        {attribute.value_ids.map((value) => {
                          // Get current values array for this attribute or empty array if none
                          const currentValues = Array.isArray(selectedFilters.attributes[attribute.id]) 
                            ? selectedFilters.attributes[attribute.id] 
                            : [];
                          
                          const isSelected = currentValues.includes(value.id);
                          
                          return (
                            <TouchableOpacity
                              key={value.id}
                              className="w-1/2 flex-row items-center mb-2"
                              onPress={() => toggleMultipleAttribute(attribute.id, value.id)}
                            >
                              <View
                                className={`w-5 h-5 border rounded mr-2 items-center justify-center ${
                                  isSelected
                                    ? "bg-[#013443] border-[#013443] border-2"
                                    : "border-[#013443] border-2"
                                }`}
                              >
                                {isSelected && (
                                  <Ionicons name="checkmark" size={16} color="#fff" />
                                )}
                              </View>
                              <Text className="text-[#202244CC]">{value.name}</Text>
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                    )}

                    {/* PILLS/BUTTONS DISPLAY */}
                    {attribute.display_type === "pills" && (
                      <View className="flex-row flex-wrap">
                        {attribute.value_ids.map((value) => (
                          <TouchableOpacity
                            key={value.id}
                            className={`px-4 py-2 border mr-2 mb-2 rounded-full ${
                              selectedFilters.attributes[attribute.id] === value.id
                                ? "bg-[#013443] border-[#013443]"
                                : "border-gray-300"
                            }`}
                            onPress={() => toggleAttribute(attribute.id, value.id)}
                          >
                            <Text 
                              className={`text-sm ${
                                selectedFilters.attributes[attribute.id] === value.id
                                  ? "text-white"
                                  : "text-[#202244]"
                              }`}
                            >
                              {value.name}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    )}

                    {/* SELECT/DROPDOWN DISPLAY */}
                    {attribute.display_type === "select" && (
                      <View className="mb-4">
                        <TouchableOpacity
                          className="flex-row items-center justify-between px-4 py-3 border border-gray-300 rounded-lg mb-1"
                          onPress={() => {
                            // Toggle dropdown visibility
                            setSelectedFilters(prev => ({
                              ...prev,
                              dropdownOpen: prev.dropdownOpen === attribute.id ? null : attribute.id
                            }));
                          }}
                        >
                          <Text className="text-[#202244]">
                            {selectedFilters.attributes[attribute.id] 
                              ? attribute.value_ids.find(v => v.id === selectedFilters.attributes[attribute.id])?.name || "Select option"
                              : "Select option"}
                          </Text>
                          <Ionicons 
                            name={selectedFilters.dropdownOpen === attribute.id ? "chevron-up" : "chevron-down"} 
                            size={20} 
                            color="#202244" 
                          />
                        </TouchableOpacity>
                        
                        {/* Dropdown options */}
                        {selectedFilters.dropdownOpen === attribute.id && (
                          <View className="border border-gray-300 rounded-lg mt-1 max-h-40">
                            <ScrollView nestedScrollEnabled={true}>
                              {attribute.value_ids.map((value) => (
                                <TouchableOpacity
                                  key={value.id}
                                  className={`px-4 py-3 ${
                                    selectedFilters.attributes[attribute.id] === value.id
                                      ? "bg-gray-100"
                                      : ""
                                  }`}
                                  onPress={() => {
                                    toggleAttribute(attribute.id, value.id);
                                    // Close dropdown after selection
                                    setSelectedFilters(prev => ({
                                      ...prev,
                                      dropdownOpen: null
                                    }));
                                  }}
                                >
                                  <Text className="text-[#202244]">{value.name}</Text>
                                </TouchableOpacity>
                              ))}
                            </ScrollView>
                          </View>
                        )}
                      </View>
                    )}

                    {/* Color picker display (if needed) */}
                    {attribute.display_type === "color" && (
                      <View className="flex-row flex-wrap">
                        {attribute.value_ids.map((value) => {
                          // You'd need to associate color values with your attribute values
                          // This is an example implementation
                          const colorMap = {
                            'Red': '#FF0000',
                            'Blue': '#0000FF',
                            'Green': '#00FF00',
                            // Add more color mappings as needed
                          };
                          
                          const colorValue = colorMap[value.name] || '#CCCCCC'; 
                          
                          return (
                            <TouchableOpacity
                              key={value.id}
                              className={`p-1 mr-3 mb-2 ${
                                selectedFilters.attributes[attribute.id] === value.id
                                  ? "border-2 border-[#013443] rounded-full p-1"
                                  : ""
                              }`}
                              onPress={() => toggleAttribute(attribute.id, value.id)}
                            >
                              <View 
                                style={{backgroundColor: colorValue}}
                                className="w-8 h-8 rounded-full"
                              />
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                    )}

                    {/* Default case if display_type is not recognized */}
                    {!["radio", "checkbox", "pills", "select", "color"].includes(attribute.display_type) && (
                      <Text className="text-[#202244] italic">Display type not supported: {attribute.display_type}</Text>
                    )}
                  </View>
                );
              })}
            </>
          ) : (
            <Text className="text-[#202244]">No attributes found</Text>
          )}


<TouchableOpacity
              className="mt-4 w-full bg-[#013443] py-2 rounded-lg"
              onPress={handleApplyFilters}
            >
              <Text className="text-white text-center">Apply Filters</Text>
            </TouchableOpacity>

              </ScrollView>
            </View>
          {/* </View> */}
        </View>
      </Modal>
    </View>
  );
};

export default NoProductPage;
