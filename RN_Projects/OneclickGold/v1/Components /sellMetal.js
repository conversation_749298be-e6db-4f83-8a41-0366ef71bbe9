
// import React, { useState, useEffect } from "react";
// import { View, Text, TextInput, Pressable, Alert, TouchableOpacity, ActivityIndicator } from "react-native";
// import Ionicons from "@expo/vector-icons/Ionicons";
// import MaterialIcons from "@expo/vector-icons/MaterialIcons";

// const SellMetalForm = ({ route, navigation }) => {
//   const [selectedMetal, setSelectedMetal] = useState(null);
//   const [grams, setGrams] = useState("1");
//   const [metals, setMetals] = useState([]);
//   const [metalId, setMetalId] = useState();
//   const [loading, setLoading] = useState(false);
//   const [inputFocused, setInputFocused] = useState(false);

//   useEffect(() => {
//     const { metal, quantity, metalId } = route.params;
//     setSelectedMetal(metal);
//     setGrams(quantity.toString());
//     setMetalId(metalId);
//   }, [route.params]);

//   const fetchMetals = async () => {
//     try {
//       const response = await fetch('https://gold.arihantai.com/api/v1/metals', {
//         method: 'GET',
//         headers: {
//           'Accept': 'application/json',
//           'Cookie': 'frontend_lang=en_US; session_id=e8af7d119f50004b70ae445617a4ae66ca190141',
//         },
//       });

//       if (response.ok) {
//         const data = await response.json();
//         setMetals(data.data || []);
//       } else {
//         console.error('Failed to fetch metals');
//       }
//     } catch (error) {
//       console.error('Error fetching metals:', error);
//     }
//   };

//   useEffect(() => {
//     fetchMetals();
//   }, []);

//   const handleGramChange = (text) => {
//     // Allow only numbers and empty string
//     if (text === "" || /^\d*\.?\d*$/.test(text)) {
//       setGrams(text);
//     }
//   };

//   const increaseGrams = () => {
//     const current = parseFloat(grams) || 0;
//     setGrams((current + 1).toString());
//   };

//   const decreaseGrams = () => {
//     const current = parseFloat(grams) || 0;
//     if (current > 1) {
//       setGrams((current - 1).toString());
//     }
//   };

//   const handleSellMetal = async () => {
//     setLoading(true);

//     try {
//       const selectedMetalId = metals.find(metal => 
//         metal.name.toLowerCase() === selectedMetal?.toLowerCase()
//       )?.id;

//       if (!selectedMetalId) {
//         Alert.alert("Error", "Invalid metal selected");
//         setLoading(false);
//         return;
//       }

//       const gramsValue = parseFloat(grams);
//       if (isNaN(gramsValue) || gramsValue <= 0) {
//         Alert.alert("Error", "Please enter a valid amount of grams");
//         setLoading(false);
//         return;
//       }

//       const response = await fetch('https://gold.arihantai.com/api/v1/metal/sell', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//           'Cookie': 'frontend_lang=en_US; session_id=e8af7d119f50004b70ae445617a4ae66ca190141',
//         },
//         body: JSON.stringify({
//           jsonrpc: '2.0',
//           params: {
//             metal_id: selectedMetalId,
//             grams: gramsValue,
//           },
//         }),
//       });

//       if (response.ok) {
//         const data = await response.json();
//         Alert.alert(
//           "Success", 
//           `You have sold ${gramsValue} grams of ${selectedMetal}`,
//           [
//             {
//               text: "OK",
//               onPress: () => navigation.navigate("MetalVaultAccount")
//             }
//           ]
//         );
//       } else {
//         const errorData = await response.json();
//         Alert.alert(
//           "Error", 
//           errorData.error?.message || "Failed to sell metal"
//         );
//       }
//     } catch (error) {
//       console.error("Error selling metal:", error);
//       Alert.alert("Error", "An error occurred while selling the metal");
//     } finally {
//       setLoading(false);
//     }x
//     navigation.navigate('MetalVault');
//   };

//   return (
//     <View className="flex-1 bg-white px-6 py-4">
//       {/* Header */}
//       <View className="flex-row items-center mb-7 top-6">
//         <Pressable onPress={() => navigation.goBack()}>
//           <Ionicons name="arrow-back-sharp" size={24} color="black" />
//         </Pressable>
//         <Text className="text-lg font-bold text-black ml-4">Sell Metal</Text>
//       </View>

//       {/* Selected Metal */}
//       <Text className="text-base top-3 mb-4 text-black font-semibold mt-3">
//         Selected Metal
//       </Text>
//       <TextInput
//         value={selectedMetal}
//         editable={false}
//         className="text-lg text-black ml-2 mb-2 border border-gray-300 rounded-lg p-2"
//       />

//       {/* Grams Input */}
//       <Text className="text-base top-3 mb-4 text-black mb-2 font-semibold mt-5">
//         Grams to Sell
//       </Text>
//       <View 
//         className={`flex-row h-12 mt-4 items-center w-full border ${inputFocused ? 'border-[#013443]' : 'border-gray-300'} rounded-lg mb-4`}
//       >
//         <TextInput
//           value={grams}
//           onChangeText={handleGramChange}
//           keyboardType="numeric"
//           onFocus={() => setInputFocused(true)}
//           onBlur={() => setInputFocused(false)}
//           className="flex-1 text-left ml-3 text-lg text-black"
//         />
//         <View className="flex flex-col justify-between h-full">
//           {/* Increment Button */}
//           <TouchableOpacity
//             className="px-4 items-center justify-center h-1/2 border-b border-gray-200"
//             onPress={increaseGrams}
//           >
//             <MaterialIcons name="keyboard-arrow-up" size={20} color="#013443" />
//           </TouchableOpacity>

//           {/* Decrement Button */}
//           <TouchableOpacity
//             className="px-4 items-center justify-center h-1/2"
//             onPress={decreaseGrams}
//           >
//             <MaterialIcons name="keyboard-arrow-down" size={20} color="#013443" />
//           </TouchableOpacity>
//         </View>
//       </View>

//       {/* Info Box */}
//       <View className="flex-row w-full items-center mb-4 bg-[#F4672A24] rounded-md h-12 p-2">
//         <View className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center mr-2">
//           <Text className="text-black text-xs">i</Text>
//         </View>
//         <Text className="text-sm text-gray-600 flex-1">
//           Current market rates will be applied to your sale
//         </Text>
//       </View>

//       {/* Sell Button */}
//       <Pressable
//         className={`w-full h-12 items-center justify-center bg-[#013443] rounded-lg mt-4 ${loading ? 'opacity-70' : ''}`}
//         onPress={handleSellMetal}
//         disabled={loading}
//       >
//         {loading ? (
//           <ActivityIndicator color="white" size="small" />
//         ) : (
//           <Text className="text-white text-lg font-semibold">Sell Metal</Text>
//         )}
//       </Pressable>
//     </View>
//   );
// };

// export default SellMetalForm;


import React, { useState, useEffect } from "react";
import { 
  View, 
  Text, 
  TextInput, 
  Pressable, 
  Alert, 
  TouchableOpacity, 
  ActivityIndicator, 
  ScrollView,
  TouchableWithoutFeedback,
  Keyboard
} from "react-native";
import Ionicons from "@expo/vector-icons/Ionicons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

const SellMetalForm = ({ route, navigation }) => {
  const [selectedMetal, setSelectedMetal] = useState(null);
  const [grams, setGrams] = useState("1");
  const [metals, setMetals] = useState([]);
  const [metalId, setMetalId] = useState();
  const [loading, setLoading] = useState(false);
  const [inputFocused, setInputFocused] = useState(false);
  const [showMetalDropdown, setShowMetalDropdown] = useState(false);

  useEffect(() => {
    // If route.params exists, set the initial values
    if (route.params) {
      const { metal, quantity, metalId } = route.params;
      if (metal) setSelectedMetal(metal);
      if (quantity) setGrams(quantity.toString());
      if (metalId) setMetalId(metalId);
    }
  }, [route.params]);

  // Add keyboard dismiss listener to close dropdown
  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        if (showMetalDropdown) {
          setShowMetalDropdown(false);
        }
      }
    );

    return () => {
      keyboardDidHideListener.remove();
    };
  }, [showMetalDropdown]);

  const fetchMetals = async () => {
    try {
      setLoading(true);
      const response = await fetch('https://gold.arihantai.com/api/v1/metals', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cookie': 'frontend_lang=en_US; session_id=e8af7d119f50004b70ae445617a4ae66ca190141',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMetals(data.data || []);
        
        // If no metal is selected yet and we have metals data, select the first one
        if (!selectedMetal && data.data && data.data.length > 0) {
          setSelectedMetal(data.data[0].name);
          setMetalId(data.data[0].id);
        }
      } else {
        console.error('Failed to fetch metals');
        Alert.alert("Error", "Failed to fetch available metals");
      }
    } catch (error) {
      console.error('Error fetching metals:', error);
      Alert.alert("Error", "An error occurred while fetching available metals");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetals();
  }, []);

  const handleGramChange = (text) => {
    // Allow only numbers and decimal points
    if (text === "" || /^\d*\.?\d*$/.test(text)) {
      setGrams(text);
    }
  };

  const increaseGrams = () => {
    const current = parseFloat(grams) || 0;
    setGrams((current + 1).toString());
  };

  const decreaseGrams = () => {
    const current = parseFloat(grams) || 0;
    if (current > 1) {
      setGrams((current - 1).toString());
    }
  };

  const handleSellMetal = async () => {
    setLoading(true);

    try {
      const selectedMetalObj = metals.find(metal => 
        metal.name.toLowerCase() === selectedMetal?.toLowerCase()
      );

      if (!selectedMetalObj) {
        Alert.alert("Error", "Invalid metal selected");
        setLoading(false);
        return;
      }

      const gramsValue = parseFloat(grams);
      if (isNaN(gramsValue) || gramsValue <= 0) {
        Alert.alert("Error", "Please enter a valid amount of grams");
        setLoading(false);
        return;
      }

      // Check if user has enough metal to sell
      if (gramsValue > selectedMetalObj.available_grams) {
        Alert.alert("Error", `You only have ${selectedMetalObj.available_grams} grams of ${selectedMetal} available to sell`);
        setLoading(false);
        return;
      }

      const response = await fetch('https://gold.arihantai.com/api/v1/metal/sell', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'frontend_lang=en_US; session_id=e8af7d119f50004b70ae445617a4ae66ca190141',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          params: {
            metal_id: selectedMetalObj.id,
            grams: gramsValue,
          },
        }),
      });

      if (response.ok) {
        const data = await response.json();
        Alert.alert(
          "Success", 
          `You have sold ${gramsValue} grams of ${selectedMetal}`,
          [
            {
              text: "OK",
              onPress: () => navigation.navigate("MetalVaultAccount")
            }
          ]
        );
      } else {
        const errorData = await response.json();
        Alert.alert(
          "Error", 
          errorData.error?.message || "Failed to sell metal"
        );
      }
    } catch (error) {
      console.error("Error selling metal:", error);
      Alert.alert("Error", "An error occurred while selling the metal");
    } finally {
      setLoading(false);
    }
    navigation.navigate('MetalVault');
  };

  // Format the display name for metal in dropdown
  const getMetalDisplayName = (metal) => {
    return `${metal.name}`;
  };

  // Handle metal selection from dropdown
  const handleSelectMetal = (metal) => {
    setSelectedMetal(metal.name);
    setMetalId(metal.id);
    setShowMetalDropdown(false);
  };

  return (
    <TouchableWithoutFeedback onPress={() => {
      Keyboard.dismiss();
      setShowMetalDropdown(false);
    }}>
      <View className="flex-1 bg-white px-6 py-4">
        {/* Header */}
        {/* <View className="flex-row items-center mb-7 top-6">
          <Pressable onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back-sharp" size={24} color="black" />
          </Pressable>
          <Text className="text-lg font-bold text-black ml-4">Sell Metal</Text>
        </View> */}

        {/* Selected Metal */}
        <Text className="text-base top-3 mb-4 text-black font-semibold mt-3">
          Select Metal
        </Text>
        
        {/* Dropdown for Metal Selection */}
        <View className="mb-6 z-10">
          <TouchableOpacity 
            onPress={() => {
              Keyboard.dismiss();
              setShowMetalDropdown(!showMetalDropdown);
            }}
            activeOpacity={0.7}
            className="border border-gray-300 rounded-lg p-3 flex-row justify-between items-center"
          >
            <Text className="text-lg text-black">
              {selectedMetal ? 
                metals.find(m => m.name === selectedMetal) ? 
                  getMetalDisplayName(metals.find(m => m.name === selectedMetal)) : 
                  selectedMetal : 
                "Choose Metal"}
            </Text>
            <MaterialIcons 
              name={showMetalDropdown ? "arrow-drop-up" : "arrow-drop-down"} 
              size={24} 
              color="#013443" 
            />
          </TouchableOpacity>

          {/* Dropdown Menu */}
          {showMetalDropdown && (
            <View className="border border-gray-300 rounded-lg mt-1 bg-white absolute top-12 left-0 right-0 z-20 shadow-lg">
              <ScrollView style={{ maxHeight: 180 }}>
                {metals.map((metal, index) => (
                  <TouchableOpacity
                    key={metal.id || index}
                    onPress={() => handleSelectMetal(metal)}
                    className={`p-3 border-b border-gray-200 ${
                      selectedMetal === metal.name ? "bg-gray-100" : ""
                    }`}
                  >
                    <Text className="text-base">
                      {getMetalDisplayName(metal)}
                    </Text>
                  </TouchableOpacity>
                ))}
                
                {metals.length === 0 && (
                  <View className="p-4 items-center">
                    {loading ? (
                      <ActivityIndicator color="#013443" size="small" />
                    ) : (
                      <Text className="text-gray-500">No metals available</Text>
                    )}
                  </View>
                )}
              </ScrollView>
            </View>
          )}
        </View>

        {/* Grams Input */}
        <Text className="text-base top-3 mb-4 text-black mb-2 font-semibold mt-5">
          Grams to Sell
        </Text>
        <View 
          className={`flex-row h-12 mt-4 items-center w-full border ${inputFocused ? 'border-[#013443]' : 'border-gray-300'} rounded-lg mb-4`}
        >
          <TextInput
            value={grams}
            onChangeText={handleGramChange}
            keyboardType="numeric"
            onFocus={() => setInputFocused(true)}
            onBlur={() => setInputFocused(false)}
            className="flex-1 text-left ml-3 text-lg text-black"
          />
          <View className="flex flex-col justify-between h-full">
            {/* Increment Button */}
            <TouchableOpacity
              className="px-4 items-center justify-center h-1/2 border-b border-gray-200"
              onPress={increaseGrams}
            >
              <MaterialIcons name="keyboard-arrow-up" size={20} color="#013443" />
            </TouchableOpacity>

            {/* Decrement Button */}
            <TouchableOpacity
              className="px-4 items-center justify-center h-1/2"
              onPress={decreaseGrams}
            >
              <MaterialIcons name="keyboard-arrow-down" size={20} color="#013443" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Info Box */}
        <View className="flex-row w-full items-center mb-4 bg-[#F4672A24] rounded-md h-12 p-2">
          <View className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center mr-2">
            <Text className="text-black text-xs">i</Text>
          </View>
          <Text className="text-sm text-gray-600 flex-1">
            Current market rates will be applied to your sale. Minimum selling amount is 1 gram.
          </Text>
        </View>

        {/* Sell Button */}
        <Pressable
          className={`w-full h-12 items-center justify-center bg-[#013443] rounded-lg mt-4 ${loading ? 'opacity-70' : ''}`}
          onPress={handleSellMetal}
          disabled={loading || !selectedMetal}
        >
          {loading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Text className="text-white text-lg font-semibold">Sell Metal</Text>
          )}
        </Pressable>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default SellMetalForm;