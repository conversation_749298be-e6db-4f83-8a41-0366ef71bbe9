// import React, { useState, useEffect } from "react";
// import { View, Text, FlatList, Image, TouchableOpacity, Alert } from "react-native";

// const RequestDeliveryScreen = () => {
//   const [products, setProducts] = useState([]);
//   const [loading, setLoading] = useState(true);
//   const selectedMetalId = 2; // Change this based on selection

//   useEffect(() => {
//     fetchProducts();
//   }, []);

//   // Fetch products from API
//   const fetchProducts = async () => {
//     try {
//       const response = await fetch("https://gold.arihantai.com/api/v1/metal/deliveries/request_delivery", {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           "Cookie": "frontend_lang=en_US; session_id=374fb80411a5f37ecab9aa94ca4eb95034560dfe",
//         },
//         body: JSON.stringify({
//           jsonrpc: 2.0,
//           params: {
//             metal_id: 2,
//           },
//         }),
//       });

//       const data = await response.json();
//       console.log(data);
//       if (data && data.result) {
//         setProducts(data.result.products); // Assuming API returns { result: { products: [] } }
//       } else {
//         Alert.alert("Error", "No products found.");
//       }
//     } catch (error) {
//       Alert.alert("Error", "Failed to fetch products.");
//       console.error(error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <View className="flex-1 bg-white p-4">
//       <Text className="text-xl font-bold text-center mb-2">Request Delivery</Text>
//       <Text className="text-lg font-semibold">Your Balance</Text>
//       <Text className="text-gray-600 mb-4">0.0 grams available</Text>
      
//       <Text className="text-lg font-bold mb-2">Available Products</Text>

//       {loading ? (
//         <Text className="text-center text-gray-500">Loading...</Text>
//       ) : (
//         <FlatList
//           data={products}
//           keyExtractor={(item) => item.id.toString()}
//           renderItem={({ item }) => (
//             <View className="bg-white border border-gray-300 rounded-lg p-4 mb-4 shadow-sm">
//               <Text className="text-lg font-semibold text-gray-800">{item.name}</Text>
//               <Image source={{ uri: item.image_url }} className="w-full h-40 rounded-lg mt-2" resizeMode="contain" />
//               <Text className="text-gray-700 mt-2">Price: ₹{item.price}</Text>
//             </View>
//           )}
//         />
//       )}
//     </View>
//   );
// };

// export default RequestDeliveryScreen;


import React, { useState, useEffect } from "react";
import { View, Text, FlatList, Image, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import { ScrollView } from "react-native-gesture-handler";

const RequestDeliveryScreen = ({route,navigation}) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null); // To manage error state
  const [userBalance, setUserBalance] = useState(0.0); // User's balance
 
  // console.log(route.params,"route")

  // useEffect(() => {
    // if (!route.params || !route.params.metal || route.params.quantity === 0) {
    //   Alert.alert("Error", "Metal or quantity data is missing.");
    //   navigation.goBack();
    //   return;
    // }

  //   useEffect(() => {
  //   const { metal, quantity, metalId } = route.params;
  //   setSelectedMetal(metal);
  //   setGrams(quantity);
  //   // setMetalId(metalId);
  // }, [route.params, navigation]);

  
  const handleNavigate = (prodId) => {
    navigation.navigate('ProductPage', { prodId }); // Pass accountId as a parameter to the next page
  };

  const fetchMetals = async () => {
    try {
      const response = await fetch('https://gold.arihantai.com/api/v1/metals', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cookie': 'frontend_lang=en_US; session_id=e8af7d119f50004b70ae445617a4ae66ca190141',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMetals(data.data || []);
      } else {
        console.error('Failed to fetch metals');
      }
    } catch (error) {
      console.error('Error fetching metals:', error);
    }
  };

  const { metalId } = route.params;
  console.log(metalId,"metalId");
  const baseUrl = "https://gold.arihantai.com";
  const getFullImageUrl = (imagePath) => {
    if (imagePath && !imagePath.startsWith("http")) {
      return `${baseUrl}${imagePath}`; // Prepend the base URL to the relative path
    }
    return imagePath;
  };
  // console.log(getFullImageUrl("/images/1.jpg"));

  useEffect(() => {
    fetchProducts();
  }, []);

  // Fetch products from API
  const fetchProducts = async () => {
    try {
      const response = await fetch("https://gold.arihantai.com/api/v1/metal/deliveries/request_delivery", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Cookie": "frontend_lang=en_US; session_id=374fb80411a5f37ecab9aa94ca4eb95034560dfe",
        },
        body: JSON.stringify({
          jsonrpc: 2.0,
          params: {
            metal_id: metalId,
          },
        }),
      });

      const data = await response.json();
      // console.log(data);

      if (data && data.result && data.result.status === "success") {
        const { metal_products, user_balance } = data.result.data;
        // console.log(metal_products,"metal_products");
        setProducts(metal_products); // Set the list of products
        setUserBalance(user_balance.balance); // Set the user balance
      } else {
        setError("No products found or failed to fetch data.");
      }
    } catch (error) {
      setError("Failed to fetch products. Please try again later.");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: "white", padding: 16 }}>
      <Text style={{ fontSize: 24, fontWeight: "bold", textAlign: "center", marginBottom: 16 }}>Request Delivery</Text>
      <Text style={{ fontSize: 18, fontWeight: "600" }}>Your Balance</Text>
      <Text style={{ color: "gray", marginBottom: 16 }}>{userBalance} grams available</Text>

      <Text style={{ fontSize: 18, fontWeight: "bold", marginBottom: 8 }}>Available Products</Text>
      <ScrollView>
      {loading ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : error ? (
        <Text style={{ textAlign: "center", color: "red" }}>{error}</Text>
      ) : (
        <FlatList
          data={products}
          keyExtractor={(item) => item.id.toString()}
           renderItem={({ item }) => (
            <TouchableOpacity style={{ backgroundColor: "white", borderColor: "gray", borderWidth: 1, borderRadius: 8, padding: 10, marginBottom: 10, shadowColor: "#000", shadowOpacity: 0.1, shadowRadius: 8 }}
            onPress={() => handleNavigate(item.id)}
            >
            {/* // <View style={{ backgroundColor: "white", borderColor: "gray", borderWidth: 1, borderRadius: 8, padding: 10, marginBottom: 10, shadowColor: "#000", shadowOpacity: 0.1, shadowRadius: 8 }} > */}
              <Text style={{ fontSize: 18, fontWeight: "600", color: "#333" }}>{item.name}</Text>
             <Image
             source={{ uri: `data:image/jpeg;base64,${item.image}` }} // Prepend the base64 string
             style={{ width: "80%", height: 100, borderRadius: 8, marginTop: 5 }}
             resizeMode="contain"
           />
              <Text style={{ color: "#553", marginTop: 6 ,fontWeight:12}}>Price: ₹{item.price}</Text>
              {/* <TouchableOpacity onPress={() => handleNavigate(item.id)} className="bg-[#13C443] ml-2 mr-2 w-[100px] h-[20px] rounded-md items-center justify-center"> View Product </TouchableOpacity> */}
            {/* </View> */}
            </TouchableOpacity>
          )}
        />
      )}
      </ScrollView>
    </View>
  );
};

export default RequestDeliveryScreen;

