import React, { useState, useEffect ,useRef,useContext} from 'react';
import { View, Text, TouchableOpacity, ScrollView, Image, Modal, TextInput } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Ionicon from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/FontAwesome';
import Ionicons from '@expo/vector-icons/Ionicons';
import RazorpayWeb from './Razorpay';
import { SessionContext } from './login';

const MyCart = ({ navigation }) => {

  const { sessionId, loadSessionData } = useContext(SessionContext);
  const [modalVisible, setModalVisible] = useState(false);
  const [receiverModalVisible, setReceiverModalVisible] = useState(false);
  const [providerModalVisible, setProviderModalVisible] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [receiverName, setReceiverName] = useState('');
  const [receiverContact, setReceiverContact] = useState('');
  const [addresses, setAddresses] = useState([]);
  const [selectedShippingAddress, setSelectedShippingAddress] = useState(null);
  const [selectedBillingAddress, setSelectedBillingAddress] = useState(null);
  const [isGoldBalanceApplied, setIsGoldBalanceApplied] = useState(false);
  const [showAddressList, setShowAddressList] = useState({
    shipping: false,
    billing: false
  });
  const [razorpayModalVisible, setRazorpayModalVisible] = useState(false);
  const [razorpayData, setRazorpayData] = useState({
    orderId: '',
    amount: 0,
    email: '',
    contact: '',
    name: ''
  });
  const webViewRef = useRef(null);

  const calculateFinalPrice = () => {
    if (isGoldBalanceApplied) {
      const goldBalanceValue = cartData.vault.metal_balances[1].wallet_amount;
      return Math.max(0, cartData.total - goldBalanceValue); // Ensure price doesn't go negative
    }
    return cartData.total;
  };

  const handleRazorpayResponse = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('Payment response:', data);
      
      if (data.status === 'success') {
        // Close the Razorpay modal
        setRazorpayModalVisible(false);
        
        // Navigate to order confirmation
        // navigation.navigate('OrderConfirmationScreen');
        navigation.navigate('OrderConfirmationScreen', {
          // delivery_address: orderData.result.data.delivery_address?.name || 'No address found',
          delivery_address: formattedAddress,
          saved_amount: orderData.result.data.amount_total, // Or whatever discount logic you apply
          order_items: items, // items from cart
          order_id: orderData.result.data.order_id,
          order_name: orderData.result.data.order_name,
        });

        
        // You might want to send the payment details to your backend for verification
        // verifyPayment(data.response);
      } else if (data.status === 'error') {
        setRazorpayModalVisible(false);
        alert('Payment failed: ' + (data.error?.description || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error processing payment response:', error);
    }
  };

  useEffect(() => {
    const fetchAddresses = async () => {
      try {
        const response = await fetch('https://gold.arihantai.com/api/v1/addresses', {
          method: 'GET',
          headers: {
            // 'Cookie': 'frontend_lang=en_US; session_id=5d7965ad1cf8a8c6acc826033296527271bbf913',
            'Cookie': `session_id=${sessionId}`,

          },
        });

        const apiResponse = await response.json();
        console.log("Address API Response:", apiResponse); // Log the response
        
        if (apiResponse.status === "success") {
          setAddresses(apiResponse.data); // Changed from apiResponse.data.addresses to apiResponse.data
          
          // Filter delivery addresses (assuming you want to show only delivery addresses)
          const deliveryAddresses = apiResponse.data.filter(addr => addr.type === 'delivery');
          
          if (deliveryAddresses.length > 0) {
            setSelectedShippingAddress(deliveryAddresses[0]);
            setSelectedBillingAddress(deliveryAddresses[0]);
          }
        }
      } catch (error) {
        console.error('Error fetching addresses:', error);
      }
    };

    fetchAddresses();
  }, []);

  const updateQuantity = (itemId, newQuantity) => {
        if (newQuantity < 1) return;
    
        setCartData((prevData) => ({
          ...prevData,
          cartItems: prevData.cartItems.map((item) =>
            item.id === itemId ? { ...item, quantity: newQuantity } : item
          )
        }));
      };

  const applyCartBalance ={

  }
    
      const clearCart = () => {
        setCartData((prevData) => ({ ...prevData, cartItems: [] }));
      };
    
      const calculateTotal = () => {
        return cartData.cartItems
          .filter(item => item.quantity > 0)
          .reduce((sum, item) => sum + item.price * item.quantity, 0);
      };    

  const toggleModal = () => {
    setModalVisible(!modalVisible);
  };

  const handlePlaceOrder = async () => {
  if (!selectedShippingAddress || !selectedBillingAddress || !selectedProvider) {
    alert('Please select shipping, billing address, and payment method before placing the order.');
    return;
  }

  try {
    const response = await fetch('https://gold.arihantai.com/api/v1/cart/place_order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'frontend_lang=en_US; session_id=a1880618fb896635271588ae3309675acc3abd95',
      },
      body: JSON.stringify({
        jsonrpc: "2.0",
        params: {
          shipping_address_id: selectedShippingAddress.id,
          billing_address_id: selectedBillingAddress.id,
          provider_id: selectedProvider.id,
          note: "Please deliver between 9am - 5pm"
        }
      })
    });

    const result = await response.json();
    // const delAdd = result.data.delivery_address;
    // console.log("Order Response:", result.result.data.order_id);
    console.log("Order Response:", result);
    // console.log("delivery:", delAdd);

    if (result?.result?.status === "success") {
      // navigation.navigate('OrderConfirmationScreen');
       const deliveryAddress = result.result.data.delivery_address;
      const formattedAddress = [
        deliveryAddress.street,
        deliveryAddress.city,
        deliveryAddress.state,
        deliveryAddress.zip,
        deliveryAddress.country
      ].filter(Boolean).join(', ');
      
      navigation.navigate('OrderConfirmationScreen', {
        // delivery_address: result.result.data.delivery_address.city,
        delivery_address: formattedAddress,
        // saved_amount: cartData.total - calculateFinalPrice(),
        // order_items: cartData.cartItems,
        order_id: result.result.data.order_id,
        order_name: result.result.data.order_name,
        total_amount: result.result.data.amount_total
      });

    } else {
      alert("Order failed: " + (result?.error?.message || "Unknown error"));
    }
  } catch (error) {
    console.error("Error placing order:", error);
    alert("Something went wrong. Please try again.");
  }
  // navigation.navigate('OrderConfirmationScreen');
};


// const handlePlaceOrder = async () => {
//   if (!selectedShippingAddress || !selectedBillingAddress || !selectedProvider) {
//     alert('Please select shipping, billing address, and payment method before placing the order.');
//     return;
//   }

//   // If Razorpay is selected
//   if (selectedProvider.name.toLowerCase() === 'razorpay') {
//     setRazorpayData({
//       // orderId: 'order_' + Math.random().toString(36).substring(7), // Generate a random order ID
//       orderId:'order1',
//       amount: cartData.total * 100, // Razorpay expects amount in paise
//       email: '<EMAIL>', // Replace with actual user email
//       contact: receiverContact || cartData.recievers_mobile,
//       name: receiverName || cartData.recievers_name
//     });
//     setRazorpayModalVisible(true);
//     return;
//   }

//   // For other payment methods
//   try {
//     const response = await fetch('https://gold.arihantai.com/api/v1/cart/place_order', {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//         'Cookie': `session_id=${sessionId}`,
//       },
//       body: JSON.stringify({
//         jsonrpc: "2.0",
//         params: {
//           shipping_address_id: selectedShippingAddress.id,
//           billing_address_id: selectedBillingAddress.id,
//           provider_id: selectedProvider.id,
//           note: "Please deliver between 9am - 5pm"
//         }
//       })
//     });

//     const result = await response.json();
//     console.log("Order Response:", result);

//     if (result?.result?.status === "success") {
//       navigation.navigate('OrderConfirmationScreen');
//     } else {
//       alert("Order failed: " + (result?.error?.message || "Unknown error"));
//     }
//   } catch (error) {
//     console.error("Error placing order:", error);
//     alert("Something went wrong. Please try again.");
//   }
// };

  const toggleReceiverModal = () => {
    setReceiverModalVisible(!receiverModalVisible);
  };
  
  const [cartData, setCartData] = useState({
    cartItems: [],
    vault: {
      total_invested: 0,
      total_wallet: 0,
      metal_balances: [
        { metal_name: 'Silver', balance: 0, current_price: 0, wallet_amount: 0 },
        { metal_name: 'Gold', balance: 0, current_price: 0, wallet_amount: 0 }
      ]
    },
    total: 0,
    subtotal: 0,
    tax: 0,
    providers: [],
    recievers_name: "Krishna",
    recievers_mobile: "**********"
  });

  const location = () => {
    navigation.navigate('ConfirmLocation');
  };

  const bill = () => {
    navigation.navigate('Billing Summary');
  };

  const information = () => {
    toggleReceiverModal();
  };

  // Fetch cart data from API
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('https://gold.arihantai.com/api/v1/cart', {
          method: 'GET',
          headers: {
            'Cookie': 'frontend_lang=en_US; session_id=a1880618fb896635271588ae3309675acc3abd95',
          },
        });

        const apiResponse = await response.json();
        console.log(apiResponse,"2");

        if (apiResponse.status === "success") {
          const { items, vault, subtotal, tax, total, providers, recievers_name, recievers_mobile } = apiResponse.data;

          console.log("items",items);

          setCartData({
            cartItems: items.map((item) => ({
              id: item.product_id,
              name: item.name,
              price: item.price_unit,
              quantity: item.quantity,
              imageUrl: item.image_url,
              alternativeProducts: item.alternative_products || [] 
            })),
            vault,
            subtotal,
            tax,
            total,
            providers,
            recievers_name: recievers_name || "Krishna",
            recievers_mobile: recievers_mobile || "**********"
          });
          
          // Initialize receiver info for the modal
          setReceiverName(recievers_name);
          setReceiverContact(recievers_mobile);
        } else {
          console.log('Error fetching data:', apiResponse.message);
        }
      } catch (error) {
        console.error('Error fetching cart data:', error);
      }
    };

    console.log("cartData", cartData);
    fetchData();
  }, []);

  // Function to clear receiver input fields
  const clearReceiverField = (field) => {
    if (field === 'name') {
      setReceiverName('');
    } else if (field === 'contact') {
      setReceiverContact('');
    }
  };

  return (
    <View className="flex-1">
      <ScrollView className="bg-white-400">
        <View className="p-4 bg-[#fae6b2] h-[120px] ">
          <View className="flex-row mt-10 justify-between items-center">
            <Text className="text-[22px] font-bold">My Cart</Text>
            <TouchableOpacity>
              <MaterialIcons name="account-circle" size={26} color="#045180" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Cart Content */}
        <View className="bg-white  rounded-t-2xl m" keyboardShouldPersistTaps="handled">
          <View className="p-4">
            <Text className="text underline text-[15px] font-bold mt-1">Total Items</Text>
          </View>

          <View className="p-1 mx-2">
            {cartData.cartItems.map((item) => (
              <View key={item.id} className="flex-row justify-between items-center mb-3 pb-3 border-b border-dashed">
                <View className="flex-1">
                  <View className="flex-row">
                    <Image
                      source={{ uri: `https://gold.arihantai.com${item.imageUrl}` || 'https://via.placeholder.com/100' }}
                      className="w-24 h-24 rounded-lg"
                    />
                    
                    <View className="flex-col w-[154px] h-[72px] mt-3">
                      <Text className="font-bold w-5/6">{item.name}</Text>
                      <Text className="text-sm font-medium text-[#F4672A] mt-2">₹{item.price.toFixed(2)}</Text>
                    </View>
                  </View>
                </View>
                <View className="items-end mr-1">
                  <View className="flex-row items-center bg-[#013443] rounded-[6px]">
                    <TouchableOpacity
                      onPress={() => updateQuantity(item.id, item.quantity - 1)}
                      className="px-3 py-1">
                      <Text className="text-white font-bold">-</Text>
                    </TouchableOpacity>
                    <Text className="text-white px-2 text-[15px] font-bold">{item.quantity}</Text>
                    <TouchableOpacity
                      onPress={() => updateQuantity(item.id, item.quantity + 1)}
                      className="px-3 py-1">
                      <Text className="text-white font-bold">+</Text>
                    </TouchableOpacity>
                  </View>
                  <Text className="font-semibold mt-1 right-2">₹{(item.price * item.quantity).toFixed(2)}</Text>
                </View>
              </View>
            ))}
          </View>
          <TouchableOpacity
          //  onPress={clearCart} 
          className="flex-row left-64 py-2  mb-4 bg">
           <MaterialIcons name="delete" size={24} color="#045180" />
          <Text className=" font-bold  mr-2">Clear Cart</Text>
        </TouchableOpacity>
        </View>


        <View className="bg-[#F6F9FF] p-2">
        <Text className="text-lg font-semibold mb-6">Complete your purchase with</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {cartData.cartItems.flatMap(item => 
            item.alternativeProducts.map((product) => (
              <View
                key={product.id}
                className="bg-white rounded-lg left-4 p-2 mb-6 mr-4 w-[190px] h-[120px] "
              >
                <View className="flex-row mb-3">
                  {/* Product image */}
                  <View className="bg-gray-300 w-[79px] h-[104px] bottom-2 rounded-lg mb-2 relative">
                    {product.image_1920 && (
                      <Image
                        source={{ uri: `https://gold.arihantai.com${product.image_1920}` }}
                        className="w-full h-full rounded-lg"
                      />
                    )}
                    <TouchableOpacity
                      className="absolute bg-[#F3FAFF] w-[59px] h-[18px] p-2 left-2 bottom-[-12px] flex-row items-center justify-center border border-[#045180] rounded-md py-1"
                    >
                      <Text className="text-[#202244] font-bold text-sm left-1 bottom-0.5 h-[14px] mr-1">ADD</Text>
                      <Text className="text-[#202244] text-lg">+</Text>
                    </TouchableOpacity>
                  </View>
                  <View className="flex-col ml-3 mt-2">
                    <Text
                      className="font-bold text-justify text-[10px] mb-1 w-[69px] h-[44px]" 
                    >
                      {product.name}
                    </Text>
                    <Text className="text-[#F4672A] font-semibold text-base">
                      ₹{product.list_price.toFixed(2)}
                    </Text>
                  </View>
                </View>
              </View>
            ))
          )}
        </ScrollView>
        </View>
        {/* Shipping Address */}
      <TouchableOpacity 
        className="p-3 border-dashed border-b bg-white flex-row items-center" 
        onPress={() => setShowAddressList({...showAddressList, shipping: true})}
      >
        <MaterialIcons name="home" size={20} color="#045180" />
        <View className="flex-1 ml-2">
          {selectedShippingAddress ? (
            <>
              <Text className="font-bold">{selectedShippingAddress.name}</Text>
              <Text className="text-sm text-gray-600" numberOfLines={1}>
                {selectedShippingAddress.street}, {selectedShippingAddress.city}, {selectedShippingAddress.state}
              </Text>
            </>
          ) : (
            <Text className="text-bold">Add Shipping Address</Text>
          )}
        </View>
        <MaterialIcons name="keyboard-arrow-right" size={30} color="#111" />
      </TouchableOpacity>

      {/* Billing Address */}
      <TouchableOpacity 
        className="p-3 border-dashed border-b bg-white flex-row items-center" 
        onPress={() => setShowAddressList({...showAddressList, billing: true})}
      >
        <MaterialIcons name="home" size={20} color="#045180" />
        <View className="flex-1 ml-2">
          {selectedBillingAddress ? (
            <>
              <Text className="font-bold">{selectedBillingAddress.name}</Text>
              <Text className="text-sm text-gray-600" numberOfLines={1}>
                {selectedBillingAddress.street}, {selectedBillingAddress.city}, {selectedBillingAddress.state}
              </Text>
            </>
          ) : (
            <Text className="text-bold">Add Billing Address</Text>
          )}
        </View>
        <MaterialIcons name="keyboard-arrow-right" size={30} color="#111" />
      </TouchableOpacity>

{/* Address Selection Modal */}
    <Modal
      animationType="slide"
      transparent={true}
      visible={showAddressList.shipping || showAddressList.billing}
      onRequestClose={() => setShowAddressList({shipping: false, billing: false})}
    >
      <View className="flex-1 justify-end bg-[#000]/60">
        <TouchableOpacity
          onPress={() => setShowAddressList({shipping: false, billing: false})}
          className="mb-3 self-center z-10 bg-[#202244]/80 h-[35px] w-[35px] rounded-full"
        >
          <Ionicon name="close" size={24} color="#fff" style={{ left: 6, top: 5 }} />
        </TouchableOpacity>
        <View className="bg-white rounded-t-3xl p-6 h-[70%]">
          <Text className="text-xl font-bold mb-4">
            {showAddressList.shipping ? 'Select Shipping Address' : 'Select Billing Address'}
          </Text>
          
          <ScrollView>
            {addresses.map((address) => (
              <TouchableOpacity
                key={address.id}
                onPress={() => {
                  if (showAddressList.shipping) {
                    setSelectedShippingAddress(address);
                  } else {
                    setSelectedBillingAddress(address);
                  }
                  setShowAddressList({shipping: false, billing: false});
                }}
                className={`p-4 mb-2 border rounded-lg ${
                  ((showAddressList.shipping && selectedShippingAddress?.id === address.id) || 
                  (showAddressList.billing && selectedBillingAddress?.id === address.id)) 
                    ? 'border-[#045180] bg-blue-50' : 'border-gray-200'
                }`}
              >
                <Text className="font-bold">{address.name}</Text>
                <Text className="text-gray-600">
                  {address.street}, {address.city}, {address.state} - {address.zip}
                </Text>
                <Text className="text-gray-600 mt-1">Phone: {address.phone}</Text>
              </TouchableOpacity>
            ))}
            
            {/* Add New Address Button */}
            <TouchableOpacity
              onPress={() => {
                setShowAddressList({shipping: false, billing: false});
                navigation.navigate('ConfirmLocation');
              }}
              className="flex-row items-center justify-center p-4 border border-[#045180] rounded-lg mt-2"
            >
              <MaterialIcons name="add" size={20} color="#045180" />
              <Text className="text-[#045180] font-bold ml-2">Add New Address</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>

        <TouchableOpacity className="p-3 border-dotted border-b-[#D9D9Dd] bg-white flex-row" onPress={information}>
        <MaterialIcons name="phone" size={26} color="#045180" />
          <Text className="text-bold"> {cartData.recievers_name} , {cartData.recievers_mobile}  </Text>
          <MaterialIcons name="keyboard-arrow-right" className="ml-40"  size={30} color="#111" />
        </TouchableOpacity>

        <TouchableOpacity className="p-4 flex-row items-center bg-white" onPress={toggleModal}>
          <MaterialCommunityIcons name="note-text-outline" size={24} color="black" className="ml-2" />
          <Text className="text-[14px] font-semibold ml-4">Total Bill</Text>
          <Text className="text-[14px] ml-40">₹{cartData.total}</Text>
          <MaterialIcons name="keyboard-arrow-right" className="ml-4"  size={30} color="#111" />
        </TouchableOpacity>

        {/* Vault Balance */}
        {/*<View className="bg-white rounded-lg p-1 ">
          <Text className="ml-4 mb-3 font-bold text-xl"> Vault Balance </Text>
          <View className="flex-row  h-20 justify-between items-center mb-2">
            <Text className="text-base font-medium ml-2">{cartData.vault.metal_balances[1].metal_name}</Text>
            <Text className="text-base font-medium">{cartData.vault.metal_balances[1].balance.toFixed(1)}g</Text>
            <Text className="text-base font-medium">₹{(cartData.vault.metal_balances[1].wallet_amount).toFixed(2)}</Text>
          </View>
          <TouchableOpacity>
            <Text className="text-base font-medium underline text-red-500">Apply Gold Balance to Cart</Text>
          </TouchableOpacity>
        </View>  */}

        {/* Vault Balance */}
       

        {/* // In the render section for Vault Balance, add a check: */}
<View className="bg-white rounded-lg p-1 ">
  <Text className="ml-4 mb-3 font-bold text-xl">Vault Balance</Text>
  {/* {cartData.vault.metal_balances && cartData.vault.metal_balances.length > 0 ? ( */}
  { cartData.vault.metal_balances.length > 0 ? (
    <>
      {cartData.vault.metal_balances.map((metal, index) => (
        <View key={index} className="flex-row h-20 justify-between items-center mb-2">
          <Text className="text-base font-medium ml-2">{metal.metal_name}</Text>
          <Text className="text-base font-medium">{metal.balance.toFixed(1)}g</Text>
          <Text className="text-base font-medium">₹{(metal.wallet_amount || 0).toFixed(2)}</Text>
        </View>
      ))}
      <TouchableOpacity
        onPress={() => {
          setIsGoldBalanceApplied(!isGoldBalanceApplied);
        }}
      >
        <Text className={`text-base font-medium underline ${isGoldBalanceApplied ? 'text-green-500' : 'text-red-500'}`}>
          {isGoldBalanceApplied ? 'Remove Gold Balance from Cart' : 'Apply Gold Balance to Cart'}
        </Text>
      </TouchableOpacity>
      {isGoldBalanceApplied && (
        <View className="mt-2 p-2 bg-green-50 rounded">
          <Text className="text-green-700">
            Gold balance of ₹{cartData.vault.metal_balances.find(m => m.metal_name === 'Gold')?.wallet_amount.toFixed(2) || 0} applied to your order
          </Text>
        </View>
      )}
    </>
  ) : (
    <Text className="text-base font-medium ml-2 mb-4">No metal balances available</Text>
  )}
</View>

        {/* Bill Summary Modal */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={toggleModal}
        >
          <View className="flex-1 justify-end bg-[#000]/60 bg-opacity-50">
            <TouchableOpacity
              onPress={toggleModal}
              className="mb-3 self-center z-10 bg-[#202244]/80 h-[35px] w-[35px] rounded-full"
            >
              <Ionicon name="close" size={24} color="#fff" style={{ left: 6, top: 5 }} />
            </TouchableOpacity>
            <View className="bg-white rounded-t-3xl p-6 h-[70%]">
              <Text className="text-2xl font-[700] font-[Jost] text-[18px] leading-[26px] mb-2 text-[#202244]">Bill Summary</Text>
              <View className="h-px border-[#D9D9D9] mb-1 border-dashed border" />

              <ScrollView className="mb-4">
                <View className="flex-row justify-between items-center my-2">
                  <View className="flex-row items-center">
                    <Icon name="lock" size={22} color="#4B5563" style={{left:2}} />
                    <Text className="ml-3 text-[#202244] font-[Mulish] font-[700] text-[14px] leading-[18px]">Item Sub Total</Text>
                  </View>
                  <Text className="font-[600] text-[15px] font-[Mulish] leading-[16px] text-[#202244]">₹{cartData.subtotal}</Text>
                </View>
                <View className="flex-row justify-between items-center my-2">
                  <View className="flex-row items-center">
                    <Ionicons name="home-outline" size={24} color="black" />
                    <Text className="ml-2 text-[#202244] font-[Mulish] font-[700] text-[14px] leading-[18px]">Making Charges</Text>
                  </View>
                  <Text className="font-[600] text-[15px] font-[Mulish] leading-[16px] text-[#202244]">₹{cartData.tax}</Text>
                </View>

                <View className="flex-row justify-between items-center my-2">
                  <View className="flex-row items-center">
                    <Icon name="credit-card" size={18} color="#4B5563" />
                    <Text className="ml-2 text-[#202244] font-[Mulish] font-[700] text-[14px] leading-[18px]">GST and Taxes</Text>
                  </View>
                  <Text className="font-[600] text-[15px] font-[Mulish] leading-[16px] text-[#202244]">₹{cartData.tax}</Text>
                </View>
                <View className="h-px mx-[125px] ml-7 border-[#D9D9D9] mt-3 mb-3 border-dashed border" />

                <View className="flex-row justify-between items-center my-2">
                  <Text className="ml-7 text-[#202244] font-[Mulish] font-[700] text-[14px] leading-[18px]">Grand Total</Text>
                  {/* <Text className="font-[800] text-[15px] font-[Mulish] leading-[16px] text-[#202244]">₹{cartData.total}</Text> */}
                  <Text className="font-[800] text-[15px] font-[Mulish] leading-[16px] text-[#202244]">₹{calculateFinalPrice()}</Text>
                </View>
              </ScrollView>
            </View>
          </View>
        </Modal>

        {/* Receiver's Information Modal */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={receiverModalVisible}
          onRequestClose={toggleReceiverModal}
        >
          <View className="flex-1 justify-end bg-[#000]/60">
            <TouchableOpacity
              onPress={toggleReceiverModal}
              className="bg-[#202244]/80 h-[35px] w-[35px] rounded-full self-center mb-2"
            >
              <Ionicon name="close" size={24} color="#fff" style={{ left: 6, top: 5 }} />
            </TouchableOpacity>
            <View className="bg-white rounded-t-3xl p-6 h-[75%]">
              <Text className="text-[18px] font-[Jost] font-[700] leading-[26px] text-[#202244">Receiver's Information</Text>
              <View className="border border-[#D9D9D9] border-dashed my-4 mb-6"></View>
              
              {/* Form fields */}
              <View className="relative p-2 shadow-sm shadow-[#202244]/40 mb-2">
                <View className="mb-4">
                  <Text className="absolute left-6 pl-1 text-xs text-[#202244]/50 bg-white z-10 -top-2">
                    Receiver's Name
                  </Text>
                  <TouchableOpacity 
                    onPress={() => clearReceiverField('name')} 
                    className="w-4 h-4 rounded-full bg-gray-300 z-10 absolute right-4 top-3"
                  >
                    <Ionicon name='close' size={13} color="white" style={{ left: 1.5, top: 1 }} />
                  </TouchableOpacity>
                  <TextInput
                    value={receiverName}
                    onChangeText={setReceiverName}
                    className="h-10 pl-2 pr-10 border border-[#202244]/50 rounded-lg text-base text-gray-800"
                    placeholder=""
                    placeholderTextColor="#aaa"
                  />
                </View>
                <View className="mb-1 mt-1">
                  <Text className="absolute pl-1 left-6 text-xs text-[#202244]/50 bg-white -top-2 z-10">
                    Receiver's contact
                  </Text>
                  <TouchableOpacity 
                    onPress={() => clearReceiverField('contact')}
                    className="w-4 h-4 rounded-full bg-gray-300 z-10 absolute right-4 top-3"
                  >
                    <Ionicon name='close' size={13} color="white" style={{ left: 1.5, top: 1 }} />
                  </TouchableOpacity>
                  <TextInput
                    value={receiverContact}
                    onChangeText={setReceiverContact}
                    className="h-10 pl-2 pr-10 border border-[#202244]/50 rounded-lg text-base text-gray-800"
                    placeholder=""
                    placeholderTextColor="#aaa"
                  />
                  <Text className="ml-2">May be used to assist delivery</Text>
                </View>
              </View>
              
              {/* Save button */}
              <TouchableOpacity 
                className="bg-[#045180] rounded-lg p-3 mt-4 items-center"
                onPress={() => {
                  // Update cartData with new receiver info
                  setCartData({
                    ...cartData,
                    recievers_name: receiverName,
                    recievers_mobile: receiverContact
                  });
                  toggleReceiverModal();
                }}
              >
                <Text className="text-white font-bold">Save Information</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>

        {/* <Modal
        animationType="slide"
        transparent={false}
        visible={razorpayModalVisible}
        onRequestClose={() => setRazorpayModalVisible(false)}
      >
        <RazorpayWeb
          orderId={razorpayData.orderId}
          amount={razorpayData.amount}
          email={razorpayData.email}
          contact={razorpayData.contact}
          name={razorpayData.name}
          onPaymentResponse={(event) => {
            try {
              const data = JSON.parse(event.nativeEvent.data);
              handleRazorpayResponse(data);
            } catch (error) {
              console.error('Error processing payment response:', error);
            }
          }}
        />
        
        <TouchableOpacity
          onPress={() => setRazorpayModalVisible(false)}
          className="absolute top-10 right-5 z-10 bg-[#202244]/80 h-[35px] w-[35px] rounded-full"
        >
          <Ionicon name="close" size={24} color="#fff" style={{ left: 6, top: 5 }} />
        </TouchableOpacity>
      </Modal> */}

<Modal
  animationType="slide"
  transparent={false}
  visible={razorpayModalVisible}
  onRequestClose={() => setRazorpayModalVisible(false)}
>
  <RazorpayWeb
    orderId={razorpayData.orderId}
    amount={razorpayData.amount}
    email={razorpayData.email}
    contact={razorpayData.contact}
    name={razorpayData.name}
    onPaymentResponse={(event) => {
      try {
        const data = JSON.parse(event.nativeEvent.data);
        console.log('Payment response:', data);
        
        if (data.status === 'success') {
          setRazorpayModalVisible(false);
          navigation.navigate('OrderConfirmationScreen');
        } else if (data.status === 'error') {
          setRazorpayModalVisible(false);
          alert('Payment failed: ' + (data.error?.description || 'Unknown error'));
        }
      } catch (error) {
        console.error('Error processing payment response:', error);
      }
    }}
  />
  
  <TouchableOpacity
    onPress={() => setRazorpayModalVisible(false)}
    className="absolute top-10 right-5 z-10 bg-[#202244]/80 h-[35px] w-[35px] rounded-full"
  >
    <Ionicon name="close" size={24} color="#fff" style={{ left: 6, top: 5 }} />
  </TouchableOpacity>
</Modal>

        {/* Payment section */}
        <View className="bg-white p-4 border-t border-gray-200 mb-2">
          <View className="flex-row items-center justify-between">
            {/* Payment Method Section */}
            <TouchableOpacity
              className="flex-row items-center space-x-3"
              onPress={() => {
                if (cartData.providers.length > 1) {
                  setProviderModalVisible(true);
                }
              }}
            >
              <View>
                <Text className="text-sm text-gray-600">PAY USING</Text>
                <Text className="text-sm font-semibold">
                  {selectedProvider ? selectedProvider.name : 'Select a method'}
                </Text>
              </View>
              {cartData.providers.length > 1 && (
                <Ionicons name="chevron-down" size={16} color="gray" />
              )}
            </TouchableOpacity>

            {/* Place Order Button */}
            <TouchableOpacity 
            onPress={handlePlaceOrder}
            className="bg-[#045180] rounded-lg flex-row w-[200px] h-[50px] items-center justify-between px-4 ml-2">
              <View>
                {/* <Text className="text-white font-semibold text-sm">₹{cartData.total}</Text> */}
                <Text className="text-white font-semibold text-sm">
                  ₹{calculateFinalPrice()}
                </Text>
                <Text className="text-white text-xs">TOTAL</Text>
              </View>
              <View className="flex-row items-center">
                <Text className="text-white text-sm font-bold mr-2">Place Order</Text>
                <Ionicons name="chevron-forward" size={20} color="white" />
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Payment Provider Modal */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={providerModalVisible}
          onRequestClose={() => setProviderModalVisible(false)}
        >
          <View className="flex-1 justify-end bg-[#000]/60">
            <TouchableOpacity
              onPress={() => setProviderModalVisible(false)}
              className="mb-3 self-center z-10 bg-[#202244]/80 h-[35px] w-[35px] rounded-full"
            >
              <Ionicon name="close" size={24} color="#fff" style={{ left: 6, top: 5 }} />
            </TouchableOpacity>

            <View className="bg-white rounded-t-3xl p-6">
              <Text className="text-xl font-bold text-[#202244] mb-4">Select Payment Method</Text>
              {cartData.providers.map((provider) => (
                <TouchableOpacity
                  key={provider.id}
                  onPress={() => {
                    setSelectedProvider(provider);
                    setProviderModalVisible(false);
                  }}
                  className={`p-3 mb-2 rounded-lg border ${
                    selectedProvider?.id === provider.id ? 'border-[#045180]' : 'border-gray-300'
                  }`}
                >
                  <Text className="text-base font-medium text-[#202244]">{provider.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </Modal>
      </ScrollView>
    </View>
  );
};

export default MyCart;
