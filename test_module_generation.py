#!/usr/bin/env python3
"""
XMLRPC Script to test AI Module Generator and identify format specifier issues
"""

import xmlrpc.client
import os
import sys
import time
import traceback
import shutil

# Configuration
URL = "https://oneclickvakil.com"
DATABASE = "oneclickvakil.com"
USERNAME = "<EMAIL>"
PASSWORD_FILE = "odoo_password.txt"  # You'll need to create this file with the password

# Module paths
GENERATED_MODULE_PATH = "/mnt/extra-addons/ovakil_legal_services_complete"

def get_password():
    """Read password from file"""
    try:
        with open(PASSWORD_FILE, 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"❌ Password file not found: {PASSWORD_FILE}")
        print("Please create the file with your password:")
        print(f"echo 'YOUR_PASSWORD' > {PASSWORD_FILE}")
        sys.exit(1)

def connect_odoo():
    """Connect to Odoo via XMLRPC"""
    try:
        password = get_password()
        
        # Connect to common endpoint
        common = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/common')
        
        # Authenticate
        uid = common.authenticate(DATABASE, USERNAME, password, {})
        if not uid:
            print("❌ Authentication failed!")
            return None, None, None
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        # Connect to object endpoint
        models = xmlrpc.client.ServerProxy(f'{URL}/xmlrpc/2/object')
        
        return common, models, uid, password
        
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        traceback.print_exc()
        return None, None, None, None

def cleanup_existing_module():
    """Remove existing generated module if it exists"""
    if os.path.exists(GENERATED_MODULE_PATH):
        try:
            print(f"🧹 Removing existing module: {GENERATED_MODULE_PATH}")
            shutil.rmtree(GENERATED_MODULE_PATH)
            print("✅ Existing module removed successfully")
        except Exception as e:
            print(f"❌ Failed to remove existing module: {str(e)}")
            return False
    return True

def find_legal_template(models, uid, password):
    """Find the Legal Document Services template"""
    try:
        # First, list all available templates
        print("🔍 Searching for all available templates...")
        all_template_ids = models.execute_kw(
            DATABASE, uid, password,
            'module.template', 'search',
            [[]]
        )

        if all_template_ids:
            all_templates = models.execute_kw(
                DATABASE, uid, password,
                'module.template', 'read',
                [all_template_ids], {'fields': ['id', 'name', 'code']}
            )

            print(f"📋 Found {len(all_templates)} templates:")
            for tmpl in all_templates:
                print(f"  - {tmpl['name']} (ID: {tmpl['id']}, Code: {tmpl.get('code', 'N/A')})")

        # Search for the template with various patterns
        search_patterns = [
            [['name', 'ilike', 'Legal Document Services']],
            [['name', 'ilike', 'Legal']],
            [['name', 'ilike', 'Document']],
            [['code', 'ilike', 'legal']],
        ]

        template = None
        for pattern in search_patterns:
            template_ids = models.execute_kw(
                DATABASE, uid, password,
                'module.template', 'search',
                [pattern]
            )

            if template_ids:
                template_data = models.execute_kw(
                    DATABASE, uid, password,
                    'module.template', 'read',
                    [template_ids[0]], {'fields': ['id', 'name', 'code']}
                )
                # template_data is a list, get the first item
                if template_data and len(template_data) > 0:
                    template = template_data[0]
                    print(f"✅ Found template with pattern {pattern}: {template['name']} (ID: {template['id']})")
                    break

        if not template:
            print("❌ No legal-related template found!")
            # Use the first available template for testing
            if all_templates:
                template = all_templates[0]
                print(f"⚠️ Using first available template for testing: {template['name']} (ID: {template['id']})")
            else:
                print("❌ No templates available at all!")
                return None

        return template

    except Exception as e:
        print(f"❌ Error finding template: {str(e)}")
        traceback.print_exc()
        return None

def test_module_generation(models, uid, password, template):
    """Test module generation using the wizard"""
    try:
        print("🚀 Starting module generation test...")

        # Create wizard record
        wizard_data = {
            'template_id': template['id'],
            'module_name': 'legal_services_complete',
            'module_title': 'Legal Document Services Complete',
            'module_description': 'Complete legal document services module generated via XMLRPC test',
            'module_author': 'AI Module Generator Test',
            'module_version': '********.0',
            'include_portal': True,
            'include_website': True,
        }

        wizard_id = models.execute_kw(
            DATABASE, uid, password,
            'module.generator.wizard', 'create',
            [wizard_data]
        )

        print(f"✅ Created wizard record: {wizard_id}")

        # Execute module generation with detailed error handling
        print("⚙️ Executing module generation...")

        try:
            result = models.execute_kw(
                DATABASE, uid, password,
                'module.generator.wizard', 'action_generate_module',
                [wizard_id]
            )

            print(f"📋 Generation result: {result}")

            # Check if generation was successful
            if isinstance(result, dict):
                if result.get('type') == 'ir.actions.client':
                    params = result.get('params', {})
                    message = params.get('message', '')
                    msg_type = params.get('type', '')

                    if 'success' in msg_type:
                        print("✅ Module generation completed successfully!")
                        return True, None
                    elif 'error' in msg_type or 'danger' in msg_type:
                        print(f"❌ Module generation failed: {message}")
                        return False, message
                    else:
                        print(f"⚠️ Unexpected result type: {msg_type}, message: {message}")
                        return False, f"Unexpected result: {message}"

        except xmlrpc.client.Fault as fault:
            # This will catch Odoo server errors including format specifier issues
            error_msg = str(fault)
            print(f"❌ XMLRPC Fault during generation: {error_msg}")

            # Check for specific format specifier error
            if 'Invalid format specifier' in error_msg or 'format specifier' in error_msg.lower():
                print("🔍 DETECTED: Format specifier error!")
                # Extract more details from the error
                lines = error_msg.split('\n')
                for line in lines:
                    if 'format specifier' in line.lower() or 'invalid format' in line.lower():
                        print(f"🎯 Error detail: {line.strip()}")

            return False, error_msg

        except Exception as e:
            error_msg = f"Unexpected exception during generation: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return False, error_msg

        # Get wizard record to check for errors
        try:
            wizard_record = models.execute_kw(
                DATABASE, uid, password,
                'module.generator.wizard', 'read',
                [wizard_id], {'fields': ['generation_log', 'generated_module_path']}
            )

            generation_log = wizard_record.get('generation_log', '')
            generated_path = wizard_record.get('generated_module_path', '')

            print(f"📝 Generation log:\n{generation_log}")
            print(f"📁 Generated path: {generated_path}")

            # Check for errors in log
            if 'ERROR' in generation_log or 'Failed' in generation_log or 'format specifier' in generation_log.lower():
                print("❌ Errors found in generation log")
                return False, generation_log

            print("✅ Module generation appears successful")
            return True, None

        except Exception as e:
            print(f"⚠️ Could not read wizard record: {str(e)}")
            return False, f"Could not verify generation status: {str(e)}"

    except Exception as e:
        error_msg = f"Exception during generation setup: {str(e)}"
        print(f"❌ {error_msg}")
        traceback.print_exc()
        return False, error_msg

def check_generated_module():
    """Check if the generated module exists and is valid"""
    try:
        if not os.path.exists(GENERATED_MODULE_PATH):
            print(f"❌ Generated module not found at: {GENERATED_MODULE_PATH}")
            return False
        
        print(f"✅ Generated module found at: {GENERATED_MODULE_PATH}")
        
        # Check for key files
        key_files = [
            '__manifest__.py',
            '__init__.py',
            'models/__init__.py',
            'models/legal_document_services.py',
            'views/legal_document_services_views.xml',
        ]
        
        missing_files = []
        for file_path in key_files:
            full_path = os.path.join(GENERATED_MODULE_PATH, file_path)
            if os.path.exists(full_path):
                print(f"✅ Found: {file_path}")
            else:
                print(f"❌ Missing: {file_path}")
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ Module incomplete - missing {len(missing_files)} files")
            return False
        
        # Check for syntax errors in Python files
        python_files = []
        for root, dirs, files in os.walk(GENERATED_MODULE_PATH):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        syntax_errors = []
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                compile(content, py_file, 'exec')
                print(f"✅ Syntax OK: {os.path.relpath(py_file, GENERATED_MODULE_PATH)}")
            except SyntaxError as e:
                error_msg = f"Syntax error in {os.path.relpath(py_file, GENERATED_MODULE_PATH)}: {str(e)}"
                print(f"❌ {error_msg}")
                syntax_errors.append(error_msg)
            except Exception as e:
                error_msg = f"Error checking {os.path.relpath(py_file, GENERATED_MODULE_PATH)}: {str(e)}"
                print(f"⚠️ {error_msg}")
        
        if syntax_errors:
            print(f"❌ Found {len(syntax_errors)} syntax errors")
            return False
        
        print("✅ All Python files have valid syntax")
        return True
        
    except Exception as e:
        print(f"❌ Error checking generated module: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 AI Module Generator XMLRPC Test")
    print("=" * 50)
    
    # Step 1: Connect to Odoo
    print("\n📡 Step 1: Connecting to Odoo...")
    common, models, uid, password = connect_odoo()
    if not models:
        sys.exit(1)
    
    # Step 2: Cleanup existing module
    print("\n🧹 Step 2: Cleaning up existing module...")
    if not cleanup_existing_module():
        print("⚠️ Warning: Could not clean up existing module, continuing anyway...")
    
    # Step 3: Find template
    print("\n🔍 Step 3: Finding Legal Document Services template...")
    template = find_legal_template(models, uid, password)
    if not template:
        sys.exit(1)
    
    # Step 4: Test module generation
    print("\n⚙️ Step 4: Testing module generation...")
    success, error = test_module_generation(models, uid, password, template)
    
    # Step 5: Check generated module
    print("\n🔍 Step 5: Checking generated module...")
    module_valid = check_generated_module()
    
    # Step 6: Summary and cleanup
    print("\n📊 Test Summary:")
    print("=" * 30)
    
    if success and module_valid:
        print("✅ SUCCESS: Module generation completed successfully!")
        print(f"📁 Generated module location: {GENERATED_MODULE_PATH}")
    else:
        print("❌ FAILURE: Module generation failed!")
        if error:
            print(f"🔍 Error details: {error}")
        
        # Keep failed module for debugging
        if os.path.exists(GENERATED_MODULE_PATH):
            print(f"🔍 Keeping failed module for debugging: {GENERATED_MODULE_PATH}")
            print("📁 Generated files:")
            for root, dirs, files in os.walk(GENERATED_MODULE_PATH):
                for file in files:
                    rel_path = os.path.relpath(os.path.join(root, file), GENERATED_MODULE_PATH)
                    print(f"  - {rel_path}")
            print("⚠️ Module kept for debugging - please examine manually")
    
    print("\n🏁 Test completed!")

if __name__ == "__main__":
    main()
